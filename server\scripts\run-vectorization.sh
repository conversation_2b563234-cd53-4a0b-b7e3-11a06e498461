#!/bin/bash

# Product Vectorization Script Runner
# This script runs product vectorization with the correct environment variables

# Set environment variables
# export DATABASE_URL="postgresql://halal_user:halal_password_2025@localhost:15633/halal_chat"

# Check command line arguments
case "$1" in
  "verify")
    echo "🔍 Running vectorization verification..."
    bun run vectorize:verify
    ;;
  "force")
    echo "🔄 Running forced vectorization (re-vectorize all products)..."
    bun run vectorize:force
    ;;
  "")
    echo "🚀 Running vectorization for products that haven't been vectorized..."
    bun run vectorize
    ;;
  *)
    echo "Usage: $0 [verify|force]"
    echo "  verify - Check vectorization status"
    echo "  force  - Re-vectorize all products"
    echo "  (no args) - Vectorize only products that haven't been vectorized"
    exit 1
    ;;
esac
