const { WebSocketServer } = require('ws')
const http = require('http')
const express = require('express')
const cors = require('cors')

// Create Express app
const app = express()

// Middleware
app.use(
  cors({
    origin: ['http://localhost:16000', 'http://localhost:16005'],
    credentials: true,
  }),
)
app.use(express.json())

// Store WebSocket connections
const connections = new Map()

// Basic health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// Basic chat endpoint (for testing)
app.post('/api/chat/session', (req, res) => {
  const sessionId = Math.random().toString(36).substring(7)
  res.json({ sessionId })
})

app.post('/api/chat/:sessionId/message', (req, res) => {
  const { sessionId } = req.params
  const { message } = req.body

  // Simple echo response for testing
  const response = {
    message: `Echo: ${message}`,
    sessionId,
  }

  res.json(response)
})

// Create HTTP server
const server = http.createServer(app)

// Create WebSocket server
const wss = new WebSocketServer({ server, path: '/ws' })

wss.on('connection', (ws, request) => {
  const connectionId = Math.random().toString(36).substring(7)
  console.log(`WebSocket connection opened: ${connectionId}`)

  // Store connection
  connections.set(connectionId, {
    id: connectionId,
    ws: ws,
    type: null,
    sessionId: null,
    agentId: null,
    lastSeen: new Date(),
  })

  // Send connection established message
  ws.send(
    JSON.stringify({
      type: 'connection_established',
      data: { connectionId },
    }),
  )

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString())
      console.log(`Received message from ${connectionId}:`, message)

      const connection = connections.get(connectionId)
      if (connection) {
        connection.lastSeen = new Date()
      }

      // Handle different message types
      switch (message.type) {
        case 'register':
          handleRegistration(connectionId, message, ws)
          break
        case 'ping':
          handlePing(connectionId, ws)
          break
        case 'agent_message':
          handleAgentMessage(message)
          break
        case 'user_message':
          handleUserMessage(message)
          break
        default:
          console.log('Unknown message type:', message.type)
      }
    } catch (error) {
      console.error('Error processing message:', error)
      ws.send(
        JSON.stringify({
          type: 'error',
          data: { message: 'Invalid message format' },
        }),
      )
    }
  })

  ws.on('close', () => {
    console.log(`WebSocket connection closed: ${connectionId}`)
    connections.delete(connectionId)
  })

  ws.on('error', (error) => {
    console.error(`WebSocket error for ${connectionId}:`, error)
  })
})

function handleRegistration(connectionId, message, ws) {
  const { data } = message
  const connection = connections.get(connectionId)

  if (!connection) return

  if (data.connectionType === 'user' && data.sessionId) {
    connection.type = 'user'
    connection.sessionId = data.sessionId
    console.log(
      `User connection registered: ${connectionId}, session: ${data.sessionId}`,
    )
  } else if (data.connectionType === 'agent' && data.agentId) {
    connection.type = 'agent'
    connection.agentId = data.agentId
    connection.sessionId = data.sessionId
    console.log(
      `Agent connection registered: ${connectionId}, agent: ${data.agentId}`,
    )
  }

  // Send registration confirmation
  ws.send(
    JSON.stringify({
      type: 'registration_confirmed',
      data: { connectionType: data.connectionType },
    }),
  )
}

function handlePing(connectionId, ws) {
  ws.send(
    JSON.stringify({
      type: 'pong',
      data: { timestamp: new Date().toISOString() },
    }),
  )
}

function handleAgentMessage(message) {
  const { data } = message
  const { sessionId, content, agentId } = data

  if (!sessionId || !content) {
    console.error('Invalid agent message data')
    return
  }

  // Broadcast to all connections in the session
  broadcastToSession(sessionId, {
    type: 'agent_message',
    data: {
      sessionId,
      content,
      agentId,
      timestamp: new Date().toISOString(),
    },
  })

  console.log(`Agent message broadcasted to session ${sessionId}`)
}

function handleUserMessage(message) {
  const { data } = message
  const { sessionId, content, userId } = data

  if (!sessionId || !content) {
    console.error('Invalid user message data')
    return
  }

  // Broadcast to agents monitoring this session
  broadcastToSession(sessionId, {
    type: 'user_message',
    data: {
      sessionId,
      content,
      userId,
      timestamp: new Date().toISOString(),
    },
  })

  console.log(`User message broadcasted to session ${sessionId}`)
}

function broadcastToSession(sessionId, message) {
  let sent = 0
  for (const connection of connections.values()) {
    if (connection.sessionId === sessionId) {
      try {
        connection.ws.send(JSON.stringify(message))
        sent++
      } catch (error) {
        console.error('Failed to send message to connection:', error)
      }
    }
  }
  console.log(
    `Broadcasted message to ${sent} connections in session ${sessionId}`,
  )
}

// Start server
const port = process.env.PORT || 16001
server.listen(port, () => {
  console.log(`🚀 Server running on port ${port}`)
  console.log(`HTTP endpoints: http://localhost:${port}`)
  console.log(`WebSocket endpoint: ws://localhost:${port}/ws`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down server...')
  wss.close(() => {
    server.close(() => {
      console.log('Server closed')
      process.exit(0)
    })
  })
})
