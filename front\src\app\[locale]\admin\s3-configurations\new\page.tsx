'use client'

export const runtime = 'edge'

import { ArrowLeft, Save } from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { Link, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'
import type {
  S3ConfigurationPreset as PresetDetails,
  S3ConfigurationCreationRequest,
} from '@/types'
import { UserRole } from '@/types/roles'

type PresetKey = 'aws_s3' | 'cloudflare_r2' | 'digitalocean_spaces' | 'other'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function NewS3ConfigurationPage() {
  const [formData, setFormData] = useState<S3ConfigurationCreationRequest>({
    serviceName: '',
    accessKeyId: '',
    secretAccessKey: '',
    bucketName: '',
    region: '',
    endpointUrl: '',
  })
  const [selectedPreset, setSelectedPreset] = useState<PresetKey | ''>('')
  const [presets, setPresets] = useState<Record<string, PresetDetails>>({})

  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [checkingAuth, setCheckingAuth] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAdminRoleAndFetchPresets = async () => {
      setCheckingAuth(true)
      try {
        const meResponse = await api.admin.getMe()
        if (meResponse.user?.role === UserRole.ADMIN) {
          setIsAdmin(true)
          const presetsResponse = await api.admin.getS3Presets()
          // Assuming presetsResponse.data or presetsResponse directly contains the presets object
          const presetsData = presetsResponse.data || presetsResponse
          if (presetsData) {
            setPresets(presetsData)
          } else {
            console.warn('No presets data found in response:', presetsResponse)
            setPresets({}) // Set to empty if no data
          }
        } else {
          setError(
            'Access Denied: You do not have permission to create S3 configurations.'
          )
        }
      } catch (err) {
        setError('Failed to verify user role or fetch presets.')
        console.error(err)
      } finally {
        setCheckingAuth(false)
      }
    }
    checkAdminRoleAndFetchPresets()
  }, [])

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handlePresetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const presetKey = e.target.value as PresetKey | ''
    setSelectedPreset(presetKey)
    if (presetKey && presets[presetKey]) {
      const preset = presets[presetKey]
      setFormData(prev => ({
        ...prev, // Keep existing data like bucketName, keys
        serviceName: preset.serviceName || prev.serviceName,
        region: preset.region || '', // Reset or use preset's, ensure it's not null for input
        endpointUrl: preset.endpointUrl || '', // Reset or use preset's
      }))
    } else if (presetKey === '') {
      // "Custom" or no preset selected
      // Optionally clear fields or leave as is
      setFormData(prev => ({
        ...prev,
        serviceName: prev.serviceName || '', // Keep if user typed, or clear: ''
        // region: '', // Clear region and endpoint if desired for "Custom"
        // endpointUrl: '',
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (
      !formData.serviceName ||
      !formData.accessKeyId ||
      !formData.secretAccessKey ||
      !formData.bucketName
    ) {
      setError(
        'Service Name, Access Key ID, Secret Access Key, and Bucket Name are required.'
      )
      return
    }
    setIsLoading(true)
    setError(null)

    const payload: S3ConfigurationCreationRequest = {
      ...formData,
      region: formData.region || null,
      endpointUrl: formData.endpointUrl || null,
    }

    try {
      await api.admin.createS3Configuration(payload)
      router.push('/admin/s3-configurations')
    } catch (err: any) {
      setError(
        err.response?.data?.error ||
          err.message ||
          'Failed to create S3 configuration.'
      )
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  if (checkingAuth) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    )
  }

  if (!isAdmin) {
    const accessError =
      error ||
      'Access Denied: You do not have permission to create S3 configurations.'
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{accessError}</span>
        </div>
        <Link
          href="/admin/s3-configurations"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to S3 Configurations
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <Link
          href="/admin/s3-configurations"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to S3 Configurations
        </Link>
      </div>
      <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-6">
        Add New S3 Configuration
      </h1>

      <form
        onSubmit={handleSubmit}
        className="bg-white shadow-md rounded-lg p-6 md:p-8"
      >
        {error && (
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
            role="alert"
          >
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        <div className="mb-4">
          <label
            htmlFor="preset"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Configuration Preset
          </label>
          <select
            id="preset"
            name="preset"
            value={selectedPreset}
            onChange={handlePresetChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
            disabled={isLoading}
          >
            <option value="">Custom / Select a Preset</option>
            {Object.keys(presets).map(key => (
              <option key={key} value={key}>
                {presets[key].serviceName}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            Selecting a preset will pre-fill some fields. You may need to
            complete Endpoint URL for R2/DigitalOcean.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="mb-4">
            <label
              htmlFor="serviceName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Service Name*
            </label>
            <input
              type="text"
              name="serviceName"
              id="serviceName"
              value={formData.serviceName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="bucketName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Bucket Name*
            </label>
            <input
              type="text"
              name="bucketName"
              id="bucketName"
              value={formData.bucketName}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="mb-4">
          <label
            htmlFor="accessKeyId"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Access Key ID*
          </label>
          <input
            type="text"
            name="accessKeyId"
            id="accessKeyId"
            value={formData.accessKeyId}
            onChange={handleInputChange}
            required
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="secretAccessKey"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Secret Access Key*
          </label>
          <input
            type="password"
            name="secretAccessKey"
            id="secretAccessKey"
            value={formData.secretAccessKey}
            onChange={handleInputChange}
            required
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="mb-4">
            <label
              htmlFor="region"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Region (optional)
            </label>
            <input
              type="text"
              name="region"
              id="region"
              value={formData.region || ''}
              onChange={handleInputChange}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="endpointUrl"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Endpoint URL (optional, for S3 compatible)
            </label>
            <input
              type="text"
              name="endpointUrl"
              id="endpointUrl"
              value={formData.endpointUrl || ''}
              onChange={handleInputChange}
              disabled={isLoading}
              placeholder="e.g., https://<ACCOUNT_ID>.r2.cloudflarestorage.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="flex items-center justify-end mt-6">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out disabled:opacity-50"
            disabled={isLoading || !isAdmin}
          >
            <Save size={18} className="mr-2" />
            {isLoading ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>
      </form>
    </div>
  )
}
