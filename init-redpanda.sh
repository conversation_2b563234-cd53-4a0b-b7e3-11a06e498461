#!/bin/bash
# init-redpanda.sh
echo "Waiting for <PERSON><PERSON><PERSON> to be ready..."
until rpk cluster health -X admin.hosts=localhost:9644; do
  sleep 2
done
echo "Redpanda is ready. Creating user..."

# Create a user if they don't exist
# Consider adding logic to check if user exists to prevent errors on restart
rpk user create "${KAFKA_USERNAME}" --password "${KAFKA_PASSWORD}" -X admin.hosts=localhost:9644

echo "User 'myuser' created."