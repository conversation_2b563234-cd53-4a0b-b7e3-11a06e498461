import express from 'express'
import R2RService from '../services/r2r'
import type { ParseR2rOptions, SearchResponse } from '../types'

const router: express.Router = express.Router()
const r2rService = new R2RService()

// Search endpoint
router.get(
  '/',
  async (req: express.Request, res: express.Response): Promise<void> => {
    const {
      query,
      retrieveDocument = 'true',
      maxWordCount = '3000',
      includeGraph = 'true',
    } = req.query
    console.log('Received search query:', query)

    if (!query) {
      res.status(400).json({ error: 'Query parameter is missing' })
      return
    }

    const { R2R_USERNAME, R2R_PASSWORD, R2R_URL, R2R_COLLECTION_ID } =
      process.env

    if (!R2R_USERNAME || !R2R_PASSWORD || !R2R_URL) {
      console.error('R2R credentials not configured')
      res.status(500).json({ error: 'R2R credentials not configured' })
      return
    }

    try {
      // Perform search
      const { chunks, graph } = await r2rService.search(query as string, {
        retrieveDocument: retrieveDocument === 'true',
        maxWordCount: Number.parseInt(maxWordCount as string, 10),
        includeGraph: includeGraph === 'true',
        collectionId: R2R_COLLECTION_ID,
      })

      console.log('Extracted chunks:', chunks.length)
      console.log('Extracted graph results:', graph.length)

      // Parse options
      const options: ParseR2rOptions = {
        retrieveDocument: retrieveDocument === 'true',
        maxWordCount: Number.parseInt(maxWordCount as string, 10),
        includeGraph: includeGraph === 'true',
        minScore: 0.1, // Set a default minimum score
        limit: 20,
      }

      // Get the R2R client for parsing
      const client = r2rService.getClient()
      const r2rResult = await r2rService.parseR2rResult(
        client,
        chunks,
        options.includeGraph ? graph : [],
        options,
      )

      const searchResponse: SearchResponse = {
        query: query as string,
        results: r2rResult.texts,
        totalChunks: chunks.length,
        totalGraphResults: graph.length,
        options,
      }

      console.log('Final search response:', {
        query,
        resultsCount: r2rResult.texts.length,
        totalChunks: chunks.length,
        totalGraphResults: graph.length,
        results: r2rResult.texts,
      })

      res.json(searchResponse)
    } catch (error) {
      console.error('R2R search error:', error)
      res.status(500).json({
        error: 'Failed to fetch search results',
        details: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  },
)

export default router
