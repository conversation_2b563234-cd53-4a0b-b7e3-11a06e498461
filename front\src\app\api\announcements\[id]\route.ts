import { type NextRequest, NextResponse } from 'next/server'
import { announcements } from '@/data/content'
export const runtime = 'edge'
export const dynamic = 'force-dynamic'

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const announcement = announcements.find(item => item.id === id)

    if (!announcement) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'Announcement not found',
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: announcement,
    })
  } catch (error) {
    console.error('Announcement detail API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch announcement',
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    const announcementIndex = announcements.findIndex(item => item.id === id)

    if (announcementIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'Announcement not found',
        },
        { status: 404 }
      )
    }

    // Update announcement (in real app, update database)
    const updatedAnnouncement = {
      ...announcements[announcementIndex],
      ...body,
      id, // Ensure ID doesn't change
    }

    return NextResponse.json({
      success: true,
      data: updatedAnnouncement,
      message: 'Announcement updated successfully',
    })
  } catch (error) {
    console.error('Update announcement API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to update announcement',
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const announcementIndex = announcements.findIndex(item => item.id === id)

    if (announcementIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'Announcement not found',
        },
        { status: 404 }
      )
    }

    // Delete announcement (in real app, delete from database)
    // announcements.splice(announcementIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'Announcement deleted successfully',
    })
  } catch (error) {
    console.error('Delete announcement API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to delete announcement',
      },
      { status: 500 }
    )
  }
}
