'use client'

export const runtime = 'edge'

import { ArrowLeft } from 'lucide-react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ContactForm, type ContactFormData } from '@/components/ui/contact-form'
import { Link, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function NewContactPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (data: ContactFormData) => {
    setIsLoading(true)
    setError(null)

    try {
      await api.post('/admin/contacts', data)
      router.push('/admin/contact')
    } catch (err: any) {
      console.error('Error creating contact:', err)
      setError(
        err.response?.data?.error || err.message || 'Failed to create contact'
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/admin/contact')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Link href="/admin/contact">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Contacts
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Contact</h1>
          <p className="text-gray-600 mt-2">
            Create a new contact entry in the system
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <ContactForm
        mode="create"
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}
