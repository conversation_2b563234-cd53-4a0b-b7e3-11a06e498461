'use client'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  MessageSquare,
  Star,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react'
import { useState } from 'react'

interface AgentPerformanceData {
  agentId: number
  agentName: string
  email: string
  role: string
  isOnline: boolean
  totalSessions: number
  completedSessions: number
  averageResponseTime: number // in seconds
  averageSessionDuration: number // in minutes
  customerSatisfactionScore: number // 1-5 scale
  onlineHours: number
  handoverRate: number // percentage
  lastActive: string
  trend: 'up' | 'down' | 'stable'
}

interface AgentPerformanceTableProps {
  data: AgentPerformanceData[]
  onAgentClick?: (agentId: number) => void
}

type SortField = keyof AgentPerformanceData
type SortDirection = 'asc' | 'desc'

export function AgentPerformanceTable({
  data,
  onAgentClick,
}: AgentPerformanceTableProps) {
  const [sortField, setSortField] = useState<SortField>('totalSessions')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const sortedData = [...data].sort((a, b) => {
    const aValue = a[sortField]
    const bValue = b[sortField]

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    }

    return 0
  })

  const formatResponseTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes.toFixed(1)}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.floor(minutes % 60)
    return `${hours}h ${remainingMinutes}m`
  }

  const getPerformanceColor = (
    score: number,
    type: 'satisfaction' | 'response' | 'completion'
  ) => {
    switch (type) {
      case 'satisfaction':
        if (score >= 4.5) return 'text-green-600'
        if (score >= 4.0) return 'text-yellow-600'
        return 'text-red-600'
      case 'response':
        if (score <= 60) return 'text-green-600'
        if (score <= 120) return 'text-yellow-600'
        return 'text-red-600'
      case 'completion':
        if (score >= 95) return 'text-green-600'
        if (score >= 85) return 'text-yellow-600'
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <div className="h-4 w-4" />
    }
  }

  const SortableHeader = ({
    field,
    children,
  }: {
    field: SortField
    children: React.ReactNode
  }) => (
    <th
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        {sortField === field &&
          (sortDirection === 'asc' ? (
            <ArrowUp className="h-3 w-3" />
          ) : (
            <ArrowDown className="h-3 w-3" />
          ))}
      </div>
    </th>
  )

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Agent Performance Details
        </h3>
        <p className="text-sm text-gray-600">
          Detailed performance metrics for all agents
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <SortableHeader field="agentName">Agent</SortableHeader>
              <SortableHeader field="totalSessions">Sessions</SortableHeader>
              <SortableHeader field="averageResponseTime">
                Response Time
              </SortableHeader>
              <SortableHeader field="averageSessionDuration">
                Avg Duration
              </SortableHeader>
              <SortableHeader field="customerSatisfactionScore">
                Satisfaction
              </SortableHeader>
              <SortableHeader field="handoverRate">
                Handover Rate
              </SortableHeader>
              <SortableHeader field="onlineHours">Online Hours</SortableHeader>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trend
              </th>
              <SortableHeader field="lastActive">Last Active</SortableHeader>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedData.map(agent => {
              const completionRate =
                (agent.completedSessions / agent.totalSessions) * 100

              return (
                <tr
                  key={agent.agentId}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onAgentClick?.(agent.agentId)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-700">
                            {agent.agentName
                              .split(' ')
                              .map(n => n[0])
                              .join('')}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="flex items-center space-x-2">
                          <div className="text-sm font-medium text-gray-900">
                            {agent.agentName}
                          </div>
                          <div
                            className={`w-2 h-2 rounded-full ${
                              agent.isOnline ? 'bg-green-500' : 'bg-gray-400'
                            }`}
                          />
                        </div>
                        <div className="text-sm text-gray-500">
                          {agent.email}
                        </div>
                        <div className="text-xs text-gray-400">
                          {agent.role}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {agent.totalSessions}
                        </div>
                        <div
                          className={`text-xs ${getPerformanceColor(completionRate, 'completion')}`}
                        >
                          {completionRate.toFixed(1)}% completed
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <div
                        className={`text-sm font-medium ${getPerformanceColor(agent.averageResponseTime, 'response')}`}
                      >
                        {formatResponseTime(agent.averageResponseTime)}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {formatDuration(agent.averageSessionDuration)}
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <Star className="h-4 w-4 text-yellow-400" />
                      <div
                        className={`text-sm font-medium ${getPerformanceColor(agent.customerSatisfactionScore, 'satisfaction')}`}
                      >
                        {agent.customerSatisfactionScore.toFixed(1)}/5.0
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {agent.handoverRate.toFixed(1)}%
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <div className="text-sm text-gray-900">
                        {agent.onlineHours}h
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    {getTrendIcon(agent.trend)}
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(agent.lastActive).toLocaleDateString()}
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}
