'use client'

import { ChevronRight, ExternalLink } from 'lucide-react'
import Image from 'next/image'
import { quickLinks } from '@/data/navigation'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'

interface QuickLinksProps {
  variant?: 'grid' | 'list' | 'compact'
  showIcons?: boolean
  showDescriptions?: boolean
  columns?: 2 | 3 | 4 | 6
  className?: string
}

export function QuickLinks({
  variant = 'grid',
  showIcons = true,
  showDescriptions = true,
  columns = 4,
  className,
}: QuickLinksProps) {
  const { language } = useLanguage()

  const getGridCols = () => {
    switch (columns) {
      case 2:
        return 'grid-cols-1 md:grid-cols-2'
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      case 6:
        return 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6'
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
    }
  }

  if (variant === 'compact') {
    return (
      <div className={cn('space-y-2', className)}>
        {quickLinks.map(link => (
          <div key={link.id}>
            {link.external ? (
              <a
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-all duration-200"
              >
                <div className="flex items-center gap-3">
                  {showIcons && (
                    <div className="flex-shrink-0 w-8 h-8">
                      <Image
                        src={link.icon}
                        alt=""
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  )}
                  <span className="font-medium text-gray-900">
                    {language === 'bm' ? link.titleBM : link.title}
                  </span>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400" />
              </a>
            ) : (
              <Link
                href={link.href}
                className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-all duration-200"
              >
                <div className="flex items-center gap-3">
                  {showIcons && (
                    <div className="flex-shrink-0 w-8 h-8">
                      <Image
                        src={link.icon}
                        alt=""
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  )}
                  <span className="font-medium text-gray-900">
                    {language === 'bm' ? link.titleBM : link.title}
                  </span>
                </div>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </Link>
            )}
          </div>
        ))}
      </div>
    )
  }

  if (variant === 'list') {
    return (
      <div className={cn('space-y-4', className)}>
        {quickLinks.map(link => (
          <div key={link.id} className="card">
            {link.external ? (
              <a
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-start gap-4 group"
              >
                {showIcons && (
                  <div className="flex-shrink-0 w-12 h-12 p-2 bg-primary-green bg-opacity-10 rounded-lg group-hover:bg-opacity-20 transition-colors">
                    <Image
                      src={link.icon}
                      alt=""
                      width={32}
                      height={32}
                      className="w-full h-full object-contain"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900 group-hover:text-primary-green transition-colors">
                      {language === 'bm' ? link.titleBM : link.title}
                    </h3>
                    <ExternalLink className="w-4 h-4 text-gray-400" />
                  </div>
                  {showDescriptions && (
                    <p className="text-gray-600 text-sm">
                      {language === 'bm'
                        ? link.descriptionBM
                        : link.description}
                    </p>
                  )}
                </div>
              </a>
            ) : (
              <Link href={link.href} className="flex items-start gap-4 group">
                {showIcons && (
                  <div className="flex-shrink-0 w-12 h-12 p-2 bg-primary-green bg-opacity-10 rounded-lg group-hover:bg-opacity-20 transition-colors">
                    <Image
                      src={link.icon}
                      alt=""
                      width={32}
                      height={32}
                      className="w-full h-full object-contain"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 group-hover:text-primary-green transition-colors mb-2">
                    {language === 'bm' ? link.titleBM : link.title}
                  </h3>
                  {showDescriptions && (
                    <p className="text-gray-600 text-sm">
                      {language === 'bm'
                        ? link.descriptionBM
                        : link.description}
                    </p>
                  )}
                </div>
              </Link>
            )}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cn('grid gap-6', getGridCols(), className)}>
      {quickLinks.map(link => (
        <div
          key={link.id}
          className="card group hover:shadow-lg transition-all duration-200"
        >
          {link.external ? (
            <a
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              className="block text-center"
            >
              {showIcons && (
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 p-3 bg-primary-green bg-opacity-10 rounded-xl group-hover:bg-opacity-20 transition-colors">
                    <Image
                      src={link.icon}
                      alt=""
                      width={40}
                      height={40}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
              )}
              <div className="flex items-center justify-center gap-2 mb-3">
                <h3 className="font-semibold text-gray-900 group-hover:text-primary-green transition-colors">
                  {language === 'bm' ? link.titleBM : link.title}
                </h3>
                <ExternalLink className="w-4 h-4 text-gray-400" />
              </div>
              {showDescriptions && (
                <p className="text-gray-600 text-sm leading-relaxed">
                  {language === 'bm' ? link.descriptionBM : link.description}
                </p>
              )}
            </a>
          ) : (
            <Link href={link.href} className="block text-center">
              {showIcons && (
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 p-3 bg-primary-green bg-opacity-10 rounded-xl group-hover:bg-opacity-20 transition-colors">
                    <Image
                      src={link.icon}
                      alt=""
                      width={40}
                      height={40}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
              )}
              <h3 className="font-semibold text-gray-900 group-hover:text-primary-green transition-colors mb-3">
                {language === 'bm' ? link.titleBM : link.title}
              </h3>
              {showDescriptions && (
                <p className="text-gray-600 text-sm leading-relaxed">
                  {language === 'bm' ? link.descriptionBM : link.description}
                </p>
              )}
            </Link>
          )}
        </div>
      ))}
    </div>
  )
}

// Featured quick links for homepage
export function FeaturedQuickLinks({ className }: { className?: string }) {
  const { language } = useLanguage()

  // Show only the most important links
  const featuredLinks = quickLinks.slice(0, 6)

  return (
    <div
      className={cn(
        'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4',
        className
      )}
    >
      {featuredLinks.map(link => (
        <div key={link.id} className="group">
          {link.external ? (
            <a
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-green hover:shadow-md transition-all duration-200 text-center"
            >
              <div className="flex justify-center mb-3">
                <div className="w-12 h-12 p-2 bg-primary-green bg-opacity-10 rounded-lg group-hover:bg-opacity-20 transition-colors">
                  <Image
                    src={link.icon}
                    alt=""
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <div className="flex items-center justify-center gap-1 mb-2">
                <h3 className="font-medium text-gray-900 group-hover:text-primary-green transition-colors text-sm">
                  {language === 'bm' ? link.titleBM : link.title}
                </h3>
                <ExternalLink className="w-3 h-3 text-gray-400" />
              </div>
            </a>
          ) : (
            <Link
              href={link.href}
              className="block p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-green hover:shadow-md transition-all duration-200 text-center"
            >
              <div className="flex justify-center mb-3">
                <div className="w-12 h-12 p-2 bg-primary-green bg-opacity-10 rounded-lg group-hover:bg-opacity-20 transition-colors">
                  <Image
                    src={link.icon}
                    alt=""
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <h3 className="font-medium text-gray-900 group-hover:text-primary-green transition-colors text-sm">
                {language === 'bm' ? link.titleBM : link.title}
              </h3>
            </Link>
          )}
        </div>
      ))}
    </div>
  )
}
