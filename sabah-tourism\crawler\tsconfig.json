{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/crawlers/*": ["src/crawlers/*"], "@/database/*": ["src/database/*"], "@/reporting/*": ["src/reporting/*"]}, "types": ["node", "bun-types"], "lib": ["ES2022", "DOM"]}, "include": ["src/**/*", "scripts/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "output"], "ts-node": {"esm": true}}