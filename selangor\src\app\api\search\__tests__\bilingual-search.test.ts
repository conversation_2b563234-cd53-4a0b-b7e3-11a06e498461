/**
 * @jest-environment node
 */
import { NextRequest } from 'next/server'
import { GET } from '../route'

// Mock the R2RService
jest.mock('@/lib/r2r', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      search: jest.fn(),
      parseR2rResult: jest.fn(),
    })),
  }
})

// Mock environment variables
const originalEnv = process.env

beforeEach(() => {
  jest.resetModules()
  process.env = {
    ...originalEnv,
    R2R_URL: 'http://localhost:7272',
    R2R_COLLECTION_ID: 'test-collection',
  }
})

afterEach(() => {
  process.env = originalEnv
})

describe('Bilingual Search API', () => {
  const mockR2RService = require('@/lib/r2r').default

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()

    // Setup default mock implementations
    const mockInstance = {
      search: jest.fn().mockResolvedValue({
        chunks: [
          {
            id: '1',
            text: 'Sample halal certification text',
            type: 'vector',
            document_id: 'doc1',
            score: 0.8,
            wordCount: 10,
            metadata: { title: 'Test Document' },
          },
        ],
      }),
      parseR2rResult: jest.fn().mockResolvedValue({
        texts: [
          {
            id: '1',
            text: 'Sample halal certification text',
            type: 'vector',
            document_id: 'doc1',
            score: 0.8,
            wordCount: 10,
            metadata: { title: 'Test Document' },
          },
        ],
        totalWordCount: 10,
      }),
    }

    mockR2RService.mockImplementation(() => mockInstance)
  })

  it('should perform basic search for English query', async () => {
    const url = new URL(
      'http://localhost:3000/api/search?q=halal%20certification'
    )
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.query).toBe('halal certification')
    expect(data.results).toHaveLength(1)
    expect(data.translatedQueries).toBeUndefined() // No distinctly Malay terms detected
  })

  it('should detect Malay terms and perform bilingual search', async () => {
    const mockInstance = mockR2RService()

    // Mock multiple search calls for bilingual search
    mockInstance.search
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '1',
            text: 'Sijil halal untuk makanan',
            type: 'vector',
            document_id: 'doc1',
            score: 0.9,
            wordCount: 5,
            metadata: { title: 'Malay Document' },
          },
        ],
      })
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '2',
            text: 'Halal certificate for food',
            type: 'vector',
            document_id: 'doc2',
            score: 0.8,
            wordCount: 5,
            metadata: { title: 'English Document' },
          },
        ],
      })

    mockInstance.parseR2rResult.mockResolvedValue({
      texts: [
        {
          id: '1',
          text: 'Sijil halal untuk makanan',
          type: 'vector',
          document_id: 'doc1',
          score: 0.9,
          wordCount: 5,
          metadata: { title: 'Malay Document' },
        },
        {
          id: '2',
          text: 'Halal certificate for food',
          type: 'vector',
          document_id: 'doc2',
          score: 0.8,
          wordCount: 5,
          metadata: { title: 'English Document' },
        },
      ],
      totalWordCount: 10,
    })

    const url = new URL('http://localhost:3000/api/search?q=sijil%20makanan')
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.query).toBe('sijil makanan')
    expect(data.translatedQueries).toBeDefined()
    expect(data.translatedQueries).toContain('certificate food')
    expect(data.results).toHaveLength(2)

    // Verify that multiple searches were performed (original + 1 translated query)
    expect(mockInstance.search).toHaveBeenCalledTimes(2)
  })

  it('should handle mixed Malay-English queries', async () => {
    const mockInstance = mockR2RService()

    mockInstance.search
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '1',
            text: 'Halal certification for ayam',
            type: 'vector',
            document_id: 'doc1',
            score: 0.85,
            wordCount: 5,
            metadata: { title: 'Mixed Language Document' },
          },
        ],
      })
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '2',
            text: 'Halal certification for chicken',
            type: 'vector',
            document_id: 'doc2',
            score: 0.8,
            wordCount: 5,
            metadata: { title: 'English Document' },
          },
        ],
      })

    mockInstance.parseR2rResult.mockResolvedValue({
      texts: [
        {
          id: '1',
          text: 'Halal certification for ayam',
          type: 'vector',
          document_id: 'doc1',
          score: 0.85,
          wordCount: 5,
          metadata: { title: 'Mixed Language Document' },
        },
        {
          id: '2',
          text: 'Halal certification for chicken',
          type: 'vector',
          document_id: 'doc2',
          score: 0.8,
          wordCount: 5,
          metadata: { title: 'English Document' },
        },
      ],
      totalWordCount: 10,
    })

    const url = new URL('http://localhost:3000/api/search?q=halal%20ayam')
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.query).toBe('halal ayam')
    expect(data.translatedQueries).toContain('halal chicken')
    expect(data.results).toHaveLength(2)
  })

  it('should deduplicate similar results from different language searches', async () => {
    const mockInstance = mockR2RService()

    // Mock searches returning similar content
    mockInstance.search
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '1',
            text: 'Halal certification requirements for restaurants',
            type: 'vector',
            document_id: 'doc1',
            score: 0.9,
            wordCount: 6,
            metadata: { title: 'Restaurant Guide' },
          },
        ],
      })
      .mockResolvedValueOnce({
        chunks: [
          {
            id: '2',
            text: 'Halal certification requirements for restaurants', // Same content
            type: 'vector',
            document_id: 'doc1', // Same document
            score: 0.85, // Lower score
            wordCount: 6,
            metadata: { title: 'Restaurant Guide' },
          },
        ],
      })

    mockInstance.parseR2rResult.mockResolvedValue({
      texts: [
        {
          id: '1',
          text: 'Halal certification requirements for restaurants',
          type: 'vector',
          document_id: 'doc1',
          score: 0.9, // Higher score should be kept
          wordCount: 6,
          metadata: { title: 'Restaurant Guide' },
        },
      ],
      totalWordCount: 6,
    })

    const url = new URL('http://localhost:3000/api/search?q=restoran%20halal')
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.results).toHaveLength(1) // Duplicates should be removed
    expect(data.results[0].score).toBe(0.9) // Higher score should be kept
  })

  it('should handle queries with no Malay terms', async () => {
    const url = new URL(
      'http://localhost:3000/api/search?q=organic%20certification'
    )
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.query).toBe('organic certification')
    expect(data.translatedQueries).toBeUndefined()

    // Should only perform one search
    const mockInstance = mockR2RService()
    expect(mockInstance.search).toHaveBeenCalledTimes(1)
  })

  it('should handle empty search results', async () => {
    const mockInstance = mockR2RService()

    mockInstance.search.mockResolvedValue({
      chunks: [],
    })

    mockInstance.parseR2rResult.mockResolvedValue({
      texts: [],
      totalWordCount: 0,
    })

    const url = new URL('http://localhost:3000/api/search?q=nonexistent%20term')
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.results).toHaveLength(0)
    expect(data.totalChunks).toBe(0)
  })
})
