import { Hono } from 'hono'
import {
  createAgent,
  deleteAgent,
  getAgent,
  getAgents,
  getAgentStats,
  updateAgent,
} from '@/controllers/adminAgentsController'
import { authenticateAdmin } from '@/middleware/auth'

const agentsRouter = new Hono()

// Apply authentication middleware to all routes
agentsRouter.use('*', authenticateAdmin)

// Agent statistics (must be before parameterized routes)
agentsRouter.get('/stats', getAgentStats)

// CRUD operations
agentsRouter.post('/', createAgent)
agentsRouter.get('/', getAgents)
agentsRouter.get('/:id', getAgent)
agentsRouter.put('/:id', updateAgent)
agentsRouter.delete('/:id', deleteAgent)

export default agentsRouter
