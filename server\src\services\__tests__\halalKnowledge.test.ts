import express from 'express'
import request from 'supertest'
import halalKnowledgeRoutes from '../../routes/halalKnowledge'

// Create test app
const createTestApp = () => {
  const app = express()
  app.use(express.json())
  app.use('/api/halal-knowledge', halalKnowledgeRoutes)
  return app
}

describe('Halal Knowledge Service', () => {
  let app: express.Application

  beforeEach(() => {
    app = createTestApp()
  })

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/halal-knowledge/health')
        .expect(200)

      expect(response.body).toHaveProperty('status')
      expect(response.body.status).toBe('healthy')
    })
  })

  describe('GET /info', () => {
    it('should return service information', async () => {
      const response = await request(app)
        .get('/api/halal-knowledge/info')
        .expect(200)

      expect(response.body).toHaveProperty('service')
      expect(response.body).toHaveProperty('version')
      expect(response.body.service).toBe('Halal Knowledge Search')
    })
  })

  describe('POST /ask', () => {
    it('should handle halal-related queries', async () => {
      const query = {
        query: 'Is chicken halal?',
        sessionId: 'test-session-1',
        maxResults: 3,
        minScore: 0.3,
        includeContext: true,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)
        .expect(200)

      expect(response.body).toHaveProperty('success')
      expect(response.body.success).toBe(true)
      expect(response.body).toHaveProperty('answer')
      expect(response.body.answer).toBeDefined()
      expect(typeof response.body.answer).toBe('string')
      expect(response.body.answer.length).toBeGreaterThan(0)
    }, 15000)

    it('should handle non-halal queries appropriately', async () => {
      const query = {
        query: 'What is the weather today?',
        sessionId: 'test-session-2',
        maxResults: 3,
        minScore: 0.3,
        includeContext: true,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)
        .expect(200)

      expect(response.body).toHaveProperty('success')
      expect(response.body).toHaveProperty('answer')
      // Non-halal queries should still get a response, but it might be different
    }, 15000)

    it('should validate required fields', async () => {
      const invalidQuery = {
        query: '', // Empty query
        sessionId: 'test-session-3',
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(invalidQuery)
        .expect(400)

      expect(response.body).toHaveProperty('success')
      expect(response.body.success).toBe(false)
      expect(response.body).toHaveProperty('error')
    })

    it('should handle missing sessionId', async () => {
      const query = {
        query: 'Is beef halal?',
        // Missing sessionId
        maxResults: 3,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)

      // Should either work with default sessionId or return validation error
      expect(response.body).toHaveProperty('success')
      expect(typeof response.body.success).toBe('boolean')
    })

    it('should respect maxResults parameter', async () => {
      const query = {
        query: 'What are the pillars of Islam?',
        sessionId: 'test-session-4',
        maxResults: 2,
        includeContext: true,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)
        .expect(200)

      expect(response.body.success).toBe(true)
      if (response.body.sources) {
        expect(response.body.sources.length).toBeLessThanOrEqual(2)
      }
    }, 15000)

    it('should handle minScore parameter', async () => {
      const query = {
        query: 'Islamic prayer guidelines',
        sessionId: 'test-session-5',
        maxResults: 5,
        minScore: 0.8, // High threshold
        includeContext: true,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)
        .expect(200)

      expect(response.body.success).toBe(true)
      // Should still return an answer even with high threshold
      expect(response.body.answer).toBeDefined()
    }, 15000)

    it('should include context when requested', async () => {
      const query = {
        query: 'Halal certification process',
        sessionId: 'test-session-6',
        maxResults: 3,
        includeContext: true,
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(query)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.answer).toBeDefined()
      // Context might be included in sources or metadata
      if (response.body.sources) {
        expect(Array.isArray(response.body.sources)).toBe(true)
      }
    }, 15000)

    it('should handle concurrent requests', async () => {
      const queries = [
        'Is fish halal?',
        'What is halal meat?',
        'Islamic dietary laws',
      ].map((query, index) => ({
        query,
        sessionId: `concurrent-session-${index}`,
        maxResults: 2,
      }))

      const promises = queries.map((query) =>
        request(app).post('/api/halal-knowledge/ask').send(query),
      )

      const responses = await Promise.all(promises)

      responses.forEach((response) => {
        expect(response.status).toBe(200)
        expect(response.body.success).toBe(true)
        expect(response.body.answer).toBeDefined()
      })
    }, 30000)
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400)

      expect(response.body).toHaveProperty('success')
      expect(response.body.success).toBe(false)
      expect(response.body).toHaveProperty('error')
    })

    it('should handle very long queries', async () => {
      const longQuery = {
        query: 'A'.repeat(10000), // Very long query
        sessionId: 'test-session-long',
      }

      const response = await request(app)
        .post('/api/halal-knowledge/ask')
        .send(longQuery)

      // Should handle gracefully, either process or reject with appropriate error
      expect(response.body).toHaveProperty('success')
      expect(typeof response.body.success).toBe('boolean')
    })
  })
})
