CREATE TABLE "twilio_configs" (
	"id" serial PRIMARY KEY NOT NULL,
	"site_id" integer NOT NULL,
	"account_sid" varchar(255) NOT NULL,
	"auth_token" varchar(255) NOT NULL,
	"phone_number" varchar(50) NOT NULL,
	"webhook_url" varchar(255),
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "twilio_messages" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"site_id" integer NOT NULL,
	"account_sid" varchar(255),
	"from" varchar(50) NOT NULL,
	"to" varchar(50) NOT NULL,
	"body" text,
	"type" varchar(50) DEFAULT 'text' NOT NULL,
	"media_url" varchar(255),
	"media_content_type" varchar(100),
	"status" varchar(50),
	"direction" varchar(10) NOT NULL,
	"error_code" integer,
	"error_message" text,
	"timestamp" timestamp NOT NULL,
	"session_id" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "twilio_configs" ADD CONSTRAINT "twilio_configs_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "public"."sites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "twilio_messages" ADD CONSTRAINT "twilio_messages_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "public"."sites"("id") ON DELETE cascade ON UPDATE no action;