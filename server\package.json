{"name": "halal-chat-server", "version": "1.0.0", "description": "Express.js server for OpenAI chat application with voice and image support", "main": "dist/server.js", "scripts": {"build": "bun build src/server.ts --outdir dist --target node --sourcemap", "start": "NODE_ENV=production bun src/server.ts", "start:bun": "NODE_ENV=production bun src/server.ts", "start:node": "NODE_ENV=production ts-node src/server.ts", "dev": "NODE_ENV=development bun --hot src/server.ts", "dev:bun": "NODE_ENV=development bun --hot src/server.ts", "dev:node": "NODE_ENV=development ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:security": "node scripts/security-test.js", "security:audit": "npm audit && npm run test:security", "security:fix": "npm audit fix", "clean": "rm -rf dist", "env": "dotenv -e .env", "db:migrate": "dotenv -e .env -- bunx drizzle-kit migrate", "db:migrate:generate": "dotenv -e .env -- bunx drizzle-kit generate --config=drizzle.config.ts", "db:migrate:prod": "dotenv -e .env.production -- bunx drizzle-kit migrate", "db:push:prod": "dotenv -e .env.production -- bunx drizzle-kit migrate --config=drizzle.config.ts", "db:seed:prod": "dotenv -e .env.production -- bun drizzle/seed-production.ts", "db:init": "bun run db:migrate && bun run db:seed", "db:init:prod": "bun run db:migrate:prod && bun run db:seed:prod", "db:status:prod": "dotenv -e .env.production -- bun scripts/db-management.ts status", "db:backup:prod": "dotenv -e .env.production -- bun scripts/db-management.ts backup", "db:check:prod": "dotenv -e .env.production -- bun scripts/db-management.ts check", "db:seed": "dotenv -e .env -- bun drizzle/seed.ts", "db:seed:twilio": "dotenv -e .env -- bun drizzle/seed-twilio-migration.ts", "db:validate:twilio": "dotenv -e .env -- bun scripts/validate-twilio-migration.ts", "test:twilio": "dotenv -e .env -- bun src/tests/test-hono-twilio-integration.mjs", "test:db": "dotenv -e .env -- bun src/tests/inspect-database.mjs", "db:studio": "dotenv -e .env -- bun x drizzle-kit studio --config=drizzle.config.ts", "db:push": "dotenv -e .env -- bun x drizzle-kit push --config=drizzle.config.ts", "db:drop": "dotenv -e .env -- bun x drizzle-kit drop --config=drizzle.config.ts", "db:drop-all": "dotenv -e .env -- bun scripts/drop-all-tables.ts", "db:drop-all:prod": "dotenv -e .env.production -- bun scripts/drop-all-tables.ts", "drizzle:generate": "dotenv -e .env -- bun x drizzle-kit generate --config=drizzle.config.ts", "drizzle:studio": "dotenv -e .env -- bun x drizzle-kit studio --config=drizzle.config.ts", "vectorize": "bun scripts/vectorize-products.ts", "vectorize:force": "bun scripts/vectorize-products.ts --force", "vectorize:verify": "bun scripts/vectorize-products.ts --verify", "test:kafka": "bun test --bail --serial kafka.test.ts", "kafka:client": "tsx kafka-client.ts", "lint": "eslint src", "lint:fix": "eslint src --fix && prettier --write ."}, "keywords": ["express", "openai", "chat", "voice", "image"], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.621.0", "@aws-sdk/s3-request-presigner": "^3.621.0", "@eslint/js": "^9.31.0", "@hono/node-server": "^1.15.0", "@types/validator": "^13.15.2", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.44.2", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "hono": "^4.8.3", "isomorphic-dompurify": "^2.25.0", "itty-router": "^5.0.18", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "postgres": "^3.4.7", "r2r-js": "^0.4.43", "twilio": "^5.7.1", "uuid": "^9.0.1", "validator": "^13.15.15", "ws": "^8.18.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/ms": "^2.1.0", "@types/multer": "^1.4.13", "@types/node": "^24.0.2", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.2", "eslint": "^9.31.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.6.2", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "~5.5.0", "typescript-eslint": "^8.37.0"}}