'use client'

import {
  Building2,
  Calendar,
  FileText,
  Globe,
  Mail,
  MapPin,
  Phone,
  User,
} from 'lucide-react'
import type { CompanySearchResponse } from '@/types/company'

interface CompanyResultsProps {
  companyResponse: CompanySearchResponse
  onPageChange?: (page: number) => void
  isLoading?: boolean
}

export function CompanyResults({
  companyResponse,
  onPageChange,
  isLoading = false,
}: CompanyResultsProps) {
  const { companies, pagination, query } = companyResponse

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="bg-white rounded-lg shadow-sm border p-6 animate-pulse"
          >
            <div className="h-6 bg-gray-200 rounded w-3/4 mb-3" />
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2" />
            <div className="h-4 bg-gray-200 rounded w-2/3" />
          </div>
        ))}
      </div>
    )
  }

  if (!companies || companies.length === 0) {
    return (
      <div className="text-center py-12">
        <Building2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No companies found
        </h3>
        <p className="text-gray-500">
          No companies match your search for "{query}". Try different keywords.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {query ? 'Company Results' : 'All Halal Companies'}
          </h2>
          <p className="text-gray-600 text-sm mt-1">
            {query
              ? `Found ${pagination.total} companies for "${query}"`
              : `Showing ${pagination.total} halal companies`}
          </p>
        </div>
      </div>

      {/* Company Results */}
      <div className="space-y-4">
        {companies.map(company => (
          <div
            key={company.id}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow p-6"
          >
            {/* Company Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {company.name}
                </h3>

                {/* Registration Number */}
                {company.registrationNumber && (
                  <div className="flex items-center text-sm text-gray-600 mb-2">
                    <FileText className="w-4 h-4 mr-2" />
                    <span className="font-medium">Registration:</span>
                    <span className="ml-1">{company.registrationNumber}</span>
                  </div>
                )}

                {/* Business Type & Category */}
                <div className="flex flex-wrap gap-2 mb-3">
                  {company.businessType && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {company.businessType}
                    </span>
                  )}
                  {company.category && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {company.category}
                    </span>
                  )}
                  {company.subcategory && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {company.subcategory}
                    </span>
                  )}
                </div>
              </div>

              {/* Certificate Status */}
              {company.certificateStatus && (
                <div className="ml-4">
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      company.certificateStatus
                        .toLowerCase()
                        .includes('active') ||
                      company.certificateStatus.toLowerCase().includes('valid')
                        ? 'bg-green-100 text-green-800'
                        : company.certificateStatus
                              .toLowerCase()
                              .includes('expired')
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {company.certificateStatus}
                  </span>
                </div>
              )}
            </div>

            {/* Company Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {/* Address */}
              {company.address && (
                <div className="flex items-start">
                  <MapPin className="w-4 h-4 text-gray-400 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <span className="text-gray-900">{company.address}</span>
                    {(company.city || company.state || company.postcode) && (
                      <div className="text-gray-600 mt-1">
                        {[company.city, company.state, company.postcode]
                          .filter(Boolean)
                          .join(', ')}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Contact Person */}
              {company.contactPerson && (
                <div className="flex items-center">
                  <User className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-gray-900">{company.contactPerson}</span>
                </div>
              )}

              {/* Phone */}
              {company.phone && (
                <div className="flex items-center">
                  <Phone className="w-4 h-4 text-gray-400 mr-2" />
                  <a
                    href={`tel:${company.phone}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {company.phone}
                  </a>
                </div>
              )}

              {/* Email */}
              {company.email && (
                <div className="flex items-center">
                  <Mail className="w-4 h-4 text-gray-400 mr-2" />
                  <a
                    href={`mailto:${company.email}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {company.email}
                  </a>
                </div>
              )}

              {/* Website */}
              {company.website && (
                <div className="flex items-center">
                  <Globe className="w-4 h-4 text-gray-400 mr-2" />
                  <a
                    href={company.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {company.website}
                  </a>
                </div>
              )}

              {/* Certificate Info */}
              {(company.certificateNumber || company.certificateType) && (
                <div className="flex items-start">
                  <FileText className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                  <div>
                    {company.certificateNumber && (
                      <div className="text-gray-900">
                        {company.certificateNumber}
                      </div>
                    )}
                    {company.certificateType && (
                      <div className="text-gray-600">
                        {company.certificateType}
                      </div>
                    )}
                    {(company.issuedDate || company.expiryDate) && (
                      <div className="text-gray-500 text-xs mt-1">
                        {company.issuedDate && `Issued: ${company.issuedDate}`}
                        {company.issuedDate && company.expiryDate && ' • '}
                        {company.expiryDate && `Expires: ${company.expiryDate}`}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => onPageChange?.(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange?.(pagination.page + 1)}
              disabled={!pagination.hasMore}
              className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">
                  {(pagination.page - 1) * pagination.limit + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span>{' '}
                results
              </p>
            </div>
            <div>
              <nav
                className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                aria-label="Pagination"
              >
                <button
                  onClick={() => onPageChange?.(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>

                {/* Page Numbers */}
                {Array.from(
                  { length: Math.min(5, pagination.totalPages) },
                  (_, i) => {
                    const pageNum =
                      Math.max(
                        1,
                        Math.min(pagination.totalPages - 4, pagination.page - 2)
                      ) + i
                    if (pageNum > pagination.totalPages) return null

                    return (
                      <button
                        key={pageNum}
                        onClick={() => onPageChange?.(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                          pageNum === pagination.page
                            ? 'z-10 bg-green-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600'
                            : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  }
                )}

                <button
                  onClick={() => onPageChange?.(pagination.page + 1)}
                  disabled={!pagination.hasMore}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
