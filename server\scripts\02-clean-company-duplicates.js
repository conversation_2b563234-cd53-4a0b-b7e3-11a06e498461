const postgres = require('postgres')
require('dotenv').config()

const sql = postgres(process.env.DATABASE_URL)

// Malaysian state mappings to ISO 3166-2:MY codes
const STATE_MAPPINGS = {
  // Full state names
  johor: 'MY-01',
  kedah: 'MY-02',
  kelantan: 'MY-03',
  melaka: 'MY-04',
  malacca: 'MY-04',
  'negeri sembilan': 'MY-05',
  pahang: 'MY-06',
  penang: 'MY-07',
  'pulau pinang': 'MY-07',
  perak: 'MY-08',
  perlis: 'MY-09',
  selangor: 'MY-10',
  terengganu: 'MY-11',
  sabah: 'MY-12',
  sarawak: 'MY-13',
  'kuala lumpur': 'MY-14',
  labuan: 'MY-15',
  putrajaya: 'MY-16',

  // Common abbreviations and variations
  kl: 'MY-14',
  wp: 'MY-14', // <PERSON><PERSON><PERSON> Persekutuan
  'wilayah persekutuan': 'MY-14',
  n9: 'MY-05', // Negeri Sembilan
  ns: 'MY-05',
  pg: 'MY-07', // Penang
  jb: 'MY-01', // Johor Bahru (Johor)
  ipoh: 'MY-08', // Perak
  'shah alam': 'MY-10', // Selangor
  'petaling jaya': 'MY-10', // Selangor
  'subang jaya': 'MY-10', // Selangor
  klang: 'MY-10', // Selangor
  kajang: 'MY-10', // Selangor
  ampang: 'MY-10', // Selangor
  puchong: 'MY-10', // Selangor
  cyberjaya: 'MY-10', // Selangor
  bangi: 'MY-10', // Selangor
  serdang: 'MY-10', // Selangor
  rawang: 'MY-10', // Selangor
  banting: 'MY-10', // Selangor
  sepang: 'MY-10', // Selangor
  gombak: 'MY-10', // Selangor
  'hulu langat': 'MY-10', // Selangor
  'kuala selangor': 'MY-10', // Selangor
  georgetown: 'MY-07', // Penang
  butterworth: 'MY-07', // Penang
  'alor setar': 'MY-02', // Kedah
  'kota bharu': 'MY-03', // Kelantan
  kuantan: 'MY-06', // Pahang
  seremban: 'MY-05', // Negeri Sembilan
  'melaka bandaraya bersejarah': 'MY-04', // Melaka
  'kota kinabalu': 'MY-12', // Sabah
  kuching: 'MY-13', // Sarawak
  miri: 'MY-13', // Sarawak
  sibu: 'MY-13', // Sarawak
  bintulu: 'MY-13', // Sarawak
  sandakan: 'MY-12', // Sabah
  tawau: 'MY-12', // Sabah
}

// Function to extract state from address
function extractStateFromAddress(address) {
  if (!address || typeof address !== 'string') return null

  const addressLower = address.toLowerCase()

  // Check for state names/codes in the address
  for (const [stateName, stateCode] of Object.entries(STATE_MAPPINGS)) {
    if (addressLower.includes(stateName)) {
      return stateCode
    }
  }

  return null
}

// Function to normalize state values
function normalizeState(state) {
  if (!state || typeof state !== 'string') return null

  const stateLower = state.toLowerCase().trim()

  // If it's already an ISO code, return as is
  if (stateLower.match(/^my-\d{2}$/)) {
    return stateLower.toUpperCase()
  }

  // Look up in mappings
  return STATE_MAPPINGS[stateLower] || null
}

async function analyzeCompanies() {
  console.log('🔍 Analyzing company data...')

  // Get all companies
  const companies = await sql`
    SELECT id, company_name, address, state, created_at
    FROM companies 
    ORDER BY id
  `

  console.log(`📊 Total companies: ${companies.length}`)

  // Analyze state data
  let companiesWithState = 0
  let companiesWithStateInAddress = 0
  let companiesWithoutState = 0

  const stateStats = {}
  const addressStateStats = {}

  companies.forEach((company) => {
    const currentState = normalizeState(company.state)
    const addressState = extractStateFromAddress(company.address)

    if (currentState) {
      companiesWithState++
      stateStats[currentState] = (stateStats[currentState] || 0) + 1
    } else if (addressState) {
      companiesWithStateInAddress++
      addressStateStats[addressState] =
        (addressStateStats[addressState] || 0) + 1
    } else {
      companiesWithoutState++
    }
  })

  console.log(`\n📍 State analysis:`)
  console.log(`  Companies with state field: ${companiesWithState}`)
  console.log(
    `  Companies with state in address: ${companiesWithStateInAddress}`,
  )
  console.log(`  Companies without state info: ${companiesWithoutState}`)

  console.log(`\n🗺️ Current state field values:`)
  Object.entries(stateStats).forEach(([state, count]) => {
    console.log(`  ${state}: ${count} companies`)
  })

  console.log(`\n🏠 States found in addresses:`)
  Object.entries(addressStateStats).forEach(([state, count]) => {
    console.log(`  ${state}: ${count} companies`)
  })

  return companies
}

async function findDuplicates() {
  console.log('\n🔄 Finding duplicate companies by address...')

  const duplicates = await sql`
    SELECT address, 
           COUNT(*) as count,
           ARRAY_AGG(
             JSON_BUILD_OBJECT(
               'id', id,
               'company_name', company_name,
               'state', state,
               'created_at', created_at
             ) ORDER BY 
               CASE WHEN state IS NOT NULL AND state != '' AND state != 'null' THEN 0 ELSE 1 END,
               created_at DESC
           ) as companies
    FROM companies 
    WHERE address IS NOT NULL AND address != '' AND address != 'null'
      AND TRIM(address) != ''
    GROUP BY address 
    HAVING COUNT(*) > 1
    ORDER BY count DESC
  `

  console.log(`\n📋 Found ${duplicates.length} duplicate address groups`)

  let totalDuplicates = 0
  const duplicatesToRemove = []

  duplicates.forEach((group, index) => {
    const companies = group.companies
    totalDuplicates += companies.length

    console.log(`\n${index + 1}. Address: ${group.address.substring(0, 80)}...`)
    console.log(`   Companies (${companies.length}):`)

    // Find the best company to keep (with state, most recent)
    const keepCompany = companies[0] // Already sorted by state presence and date

    companies.forEach((company, i) => {
      const hasState =
        company.state && company.state.trim() && company.state !== 'null'
      const addressState = extractStateFromAddress(group.address)

      console.log(
        `   ${i === 0 ? '✅' : '❌'} ID:${company.id} - ${company.company_name.substring(0, 40)}...`,
      )
      console.log(
        `      State: ${company.state || 'NONE'} | Address state: ${addressState || 'NONE'} | Created: ${company.created_at}`,
      )

      if (i > 0) {
        // Don't remove the first one (the one we're keeping)
        duplicatesToRemove.push(company.id)
      }
    })
  })

  console.log(`\n📊 Duplicate summary:`)
  console.log(`  Total duplicate groups: ${duplicates.length}`)
  console.log(`  Total companies in duplicates: ${totalDuplicates}`)
  console.log(`  Companies to remove: ${duplicatesToRemove.length}`)
  console.log(`  Companies to keep: ${duplicates.length}`)

  return duplicatesToRemove
}

async function updateStatesFromAddresses() {
  console.log('\n🏠 Updating state fields from addresses...')

  const companiesWithoutState = await sql`
    SELECT id, company_name, address, state
    FROM companies 
    WHERE (state IS NULL OR state = '' OR state = 'null')
      AND address IS NOT NULL AND address != '' AND address != 'null'
  `

  console.log(
    `📍 Found ${companiesWithoutState.length} companies without state that have addresses`,
  )

  let updatedCount = 0
  const updates = []

  for (const company of companiesWithoutState) {
    const addressState = extractStateFromAddress(company.address)
    if (addressState) {
      updates.push({
        id: company.id,
        name: company.company_name,
        oldState: company.state,
        newState: addressState,
        address: company.address,
      })
      updatedCount++
    }
  }

  console.log(
    `\n🔄 Will update ${updatedCount} companies with state from address:`,
  )
  updates.slice(0, 10).forEach((update, i) => {
    console.log(
      `${i + 1}. ID:${update.id} - ${update.name.substring(0, 40)}...`,
    )
    console.log(`   State: "${update.oldState}" → "${update.newState}"`)
    console.log(`   Address: ${update.address.substring(0, 60)}...`)
  })

  if (updates.length > 10) {
    console.log(`   ... and ${updates.length - 10} more`)
  }

  return updates
}

async function normalizeAllStates() {
  console.log('\n🗺️ Normalizing all state values to ISO format...')

  const companiesWithState = await sql`
    SELECT id, company_name, state
    FROM companies 
    WHERE state IS NOT NULL AND state != '' AND state != 'null'
  `

  console.log(
    `📍 Found ${companiesWithState.length} companies with state values`,
  )

  const normalizations = []

  for (const company of companiesWithState) {
    const normalizedState = normalizeState(company.state)
    if (normalizedState && normalizedState !== company.state) {
      normalizations.push({
        id: company.id,
        name: company.company_name,
        oldState: company.state,
        newState: normalizedState,
      })
    }
  }

  console.log(`\n🔄 Will normalize ${normalizations.length} state values:`)
  normalizations.slice(0, 10).forEach((norm, i) => {
    console.log(`${i + 1}. ID:${norm.id} - ${norm.name.substring(0, 40)}...`)
    console.log(`   State: "${norm.oldState}" → "${norm.newState}"`)
  })

  if (normalizations.length > 10) {
    console.log(`   ... and ${normalizations.length - 10} more`)
  }

  return normalizations
}

async function executeUpdates(
  duplicatesToRemove,
  stateUpdates,
  stateNormalizations,
) {
  console.log('\n🚀 EXECUTING UPDATES...')

  let totalUpdated = 0

  // Step 1: Remove duplicates (if any)
  if (duplicatesToRemove.length > 0) {
    console.log(
      `\n🗑️  Removing ${duplicatesToRemove.length} duplicate companies...`,
    )
    await sql`DELETE FROM companies WHERE id = ANY(${duplicatesToRemove})`
    console.log(`✅ Removed ${duplicatesToRemove.length} duplicate companies`)
    totalUpdated += duplicatesToRemove.length
  }

  // Step 2: Update states from addresses
  if (stateUpdates.length > 0) {
    console.log(
      `\n🏠 Updating ${stateUpdates.length} companies with state from address...`,
    )
    for (const update of stateUpdates) {
      await sql`
        UPDATE companies
        SET state = ${update.newState}, updated_at = NOW()
        WHERE id = ${update.id}
      `
    }
    console.log(
      `✅ Updated ${stateUpdates.length} companies with state from address`,
    )
    totalUpdated += stateUpdates.length
  }

  // Step 3: Normalize state values
  if (stateNormalizations.length > 0) {
    console.log(
      `\n🗺️  Normalizing ${stateNormalizations.length} state values...`,
    )
    for (const norm of stateNormalizations) {
      await sql`
        UPDATE companies
        SET state = ${norm.newState}, updated_at = NOW()
        WHERE id = ${norm.id}
      `
    }
    console.log(`✅ Normalized ${stateNormalizations.length} state values`)
    totalUpdated += stateNormalizations.length
  }

  console.log(`\n🎉 COMPLETED! Updated ${totalUpdated} records total.`)

  // Verify results
  console.log('\n📊 Verification:')
  const finalStats = await sql`
    SELECT
      COUNT(*) as total_companies,
      COUNT(CASE WHEN state IS NOT NULL AND state != '' AND state != 'null' THEN 1 END) as companies_with_state,
      COUNT(CASE WHEN state ~ '^MY-[0-9]{2}$' THEN 1 END) as companies_with_iso_state
    FROM companies
  `

  const stats = finalStats[0]
  console.log(`  Total companies: ${stats.total_companies}`)
  console.log(`  Companies with state: ${stats.companies_with_state}`)
  console.log(
    `  Companies with ISO state codes: ${stats.companies_with_iso_state}`,
  )
}

async function main() {
  try {
    console.log('🚀 Starting company cleanup analysis...\n')

    // Step 1: Analyze current data
    await analyzeCompanies()

    // Step 2: Find duplicates
    const duplicatesToRemove = await findDuplicates()

    // Step 3: Find state updates from addresses
    const stateUpdates = await updateStatesFromAddresses()

    // Step 4: Find state normalizations
    const stateNormalizations = await normalizeAllStates()

    console.log('\n📋 SUMMARY:')
    console.log(`  Duplicates to remove: ${duplicatesToRemove.length}`)
    console.log(`  States to extract from addresses: ${stateUpdates.length}`)
    console.log(`  States to normalize: ${stateNormalizations.length}`)

    if (shouldExecute) {
      console.log('\n⚠️  EXECUTE MODE - Applying changes to the database...')
      await executeUpdates(
        duplicatesToRemove,
        stateUpdates,
        stateNormalizations,
      )
    } else {
      console.log(
        '\n⚠️  This was a DRY RUN - no changes were made to the database.',
      )
      console.log('   Run with --execute flag to apply changes.')
    }
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await sql.end()
  }
}

// Check if --execute flag is provided
const shouldExecute = process.argv.includes('--execute')

if (shouldExecute) {
  console.log('⚠️  EXECUTE MODE - Changes will be applied to the database!')
} else {
  console.log('🔍 DRY RUN MODE - No changes will be made to the database.')
}

main().catch(console.error)
