{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*/src/*"]}}, "include": ["server/src/**/*", "front/src/**/*", "admin/src/**/*", "selangor/src/**/*", "crawler/**/*", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "**/node_modules", "**/.next", "**/dist", "**/build", "**/.wrangler", "**/coverage"]}