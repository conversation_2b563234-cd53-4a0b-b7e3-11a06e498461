import { sessionManager } from '../sessionManager'

describe('SessionManager', () => {
  beforeEach(() => {
    // No need to instantiate, sessionManager is already an instance
  })

  describe('Session Creation', () => {
    it('should create a new session with unique ID', () => {
      const { sessionId } = sessionManager.createSession()

      expect(sessionId).toBeDefined()
      expect(typeof sessionId).toBe('string')
      expect(sessionId.length).toBeGreaterThan(0)
    })

    it('should create unique session IDs', () => {
      const { sessionId: sessionId1 } = sessionManager.createSession()
      const { sessionId: sessionId2 } = sessionManager.createSession()

      expect(sessionId1).not.toBe(sessionId2)
    })

    it('should create session with custom data', () => {
      const customData = {
        userId: 'user-123',
        platform: 'web',
      }

      const { sessionId, session } = sessionManager.createSession(
        customData.userId,
        '127.0.0.1',
        'test-agent',
      )

      expect(session).toBeDefined()
      expect(session?.userId).toBe(customData.userId)
      expect(session?.platform).toBe(customData.platform)
    })
  })

  describe('Session Retrieval', () => {
    it('should retrieve existing session', () => {
      const { sessionId } = sessionManager.createSession()
      const session = sessionManager.getSession(sessionId)

      expect(session).toBeDefined()
      expect(session?.id).toBe(sessionId)
    })

    it('should return null for non-existent session', () => {
      const session = sessionManager.getSession('non-existent-id')

      expect(session).toBeNull()
    })

    it('should return session with correct timestamps', () => {
      const beforeCreate = Date.now()
      const { sessionId, session } = sessionManager.createSession()
      const afterCreate = Date.now()

      expect(session?.createdAt.getTime()).toBeGreaterThanOrEqual(beforeCreate)
      expect(session?.createdAt.getTime()).toBeLessThanOrEqual(afterCreate)
    })
  })

  describe('Session Updates', () => {
    it('should update session data', () => {
      const { sessionId } = sessionManager.createSession()
      const updateMessage = {
        role: 'user' as const,
        content: 'updated content',
        timestamp: new Date(),
      }

      const success = sessionManager.updateSession(sessionId, updateMessage)
      const session = sessionManager.getSession(sessionId)

      expect(success).toBe(true)
      expect(session?.messages[session.messages.length - 1].content).toBe(
        updateMessage.content,
      )
    })

    it('should update last activity timestamp', (done) => {
      const { sessionId } = sessionManager.createSession()
      const originalSession = sessionManager.getSession(sessionId)

      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        sessionManager.updateSession(sessionId, {
          role: 'user',
          content: 'activity',
          timestamp: new Date(),
        })
        const updatedSession = sessionManager.getSession(sessionId)

        expect(updatedSession?.lastMessageAt?.getTime()).toBeGreaterThan(
          originalSession!.lastMessageAt!.getTime(),
        )
        done()
      }, 10)
    })

    it('should return false when updating non-existent session', () => {
      const success = sessionManager.updateSession('non-existent', {
        role: 'user',
        content: 'data',
        timestamp: new Date(),
      })

      expect(success).toBe(false)
    })
  })

  describe('Session Deletion', () => {
    it('should delete existing session', () => {
      const { sessionId } = sessionManager.createSession()

      expect(sessionManager.getSession(sessionId)).toBeDefined()

      const success = sessionManager.deleteSession(sessionId)

      expect(success).toBe(true)
      expect(sessionManager.getSession(sessionId)).toBeNull()
    })

    it('should return false when deleting non-existent session', () => {
      const success = sessionManager.deleteSession('non-existent')

      expect(success).toBe(false)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid session data gracefully', () => {
      const { sessionId } = sessionManager.createSession(
        undefined,
        undefined,
        undefined,
      )

      expect(sessionId).toBeDefined()
      expect(sessionManager.getSession(sessionId)).toBeDefined()
    })

    it('should handle undefined session ID gracefully', () => {
      const session = sessionManager.getSession(undefined as any)

      expect(session).toBeNull()
    })

    it('should handle empty string session ID', () => {
      const session = sessionManager.getSession('')

      expect(session).toBeNull()
    })
  })
})
