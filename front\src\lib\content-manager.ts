import type { Announcement, NewsItem } from '@/data/content'

export interface ContentFilter {
  category?: string
  featured?: boolean
  dateFrom?: string
  dateTo?: string
  search?: string
  tags?: string[]
  status?: 'published' | 'draft' | 'archived'
  language?: 'en' | 'bm' | 'both'
}

export interface ContentSort {
  field: 'date' | 'title' | 'category' | 'featured'
  order: 'asc' | 'desc'
}

export interface PaginationOptions {
  page: number
  limit: number
}

export interface ContentResult<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface DocumentMetadata {
  id: string
  title: string
  titleBM?: string
  description?: string
  descriptionBM?: string
  category: string
  categoryBM?: string
  fileUrl: string
  fileSize?: number
  fileType: string
  uploadDate: string
  lastModified?: string
  downloadCount?: number
  tags?: string[]
  featured?: boolean
  language?: 'en' | 'bm' | 'both'
  status: 'published' | 'draft' | 'archived'
}

export class ContentManager {
  // Filter and sort announcements
  static filterAnnouncements(
    announcements: Announcement[],
    filter: ContentFilter = {},
    sort: ContentSort = { field: 'date', order: 'desc' },
    pagination: PaginationOptions = { page: 1, limit: 10 }
  ): ContentResult<Announcement> {
    let filtered = [...announcements]

    // Apply filters
    if (filter.category) {
      filtered = filtered.filter(item => item.category === filter.category)
    }

    if (filter.featured !== undefined) {
      filtered = filtered.filter(item => !!item.featured === filter.featured)
    }

    if (filter.dateFrom) {
      filtered = filtered.filter(item => item.date >= filter.dateFrom!)
    }

    if (filter.dateTo) {
      filtered = filtered.filter(item => item.date <= filter.dateTo!)
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase()
      filtered = filtered.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.titleBM?.toLowerCase().includes(searchLower) ||
          item.content.toLowerCase().includes(searchLower) ||
          item.contentBM?.toLowerCase().includes(searchLower)
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sort.field) {
        case 'date':
          aValue = new Date(a.date)
          bValue = new Date(b.date)
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'category':
          aValue = a.category
          bValue = b.category
          break
        case 'featured':
          aValue = a.featured ? 1 : 0
          bValue = b.featured ? 1 : 0
          break
        default:
          return 0
      }

      if (aValue < bValue) {
        return sort.order === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sort.order === 'asc' ? 1 : -1
      }
      return 0
    })

    // Apply pagination
    const total = filtered.length
    const totalPages = Math.ceil(total / pagination.limit)
    const startIndex = (pagination.page - 1) * pagination.limit
    const endIndex = startIndex + pagination.limit
    const items = filtered.slice(startIndex, endIndex)

    return {
      items,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    }
  }

  // Filter and sort news
  static filterNews(
    news: NewsItem[],
    filter: ContentFilter = {},
    sort: ContentSort = { field: 'date', order: 'desc' },
    pagination: PaginationOptions = { page: 1, limit: 10 }
  ): ContentResult<NewsItem> {
    let filtered = [...news]

    // Apply filters
    if (filter.category) {
      filtered = filtered.filter(
        item =>
          item.category === filter.category ||
          item.categoryBM === filter.category
      )
    }

    if (filter.dateFrom) {
      filtered = filtered.filter(item => item.date >= filter.dateFrom!)
    }

    if (filter.dateTo) {
      filtered = filtered.filter(item => item.date <= filter.dateTo!)
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase()
      filtered = filtered.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.titleBM?.toLowerCase().includes(searchLower) ||
          item.excerpt.toLowerCase().includes(searchLower) ||
          item.excerptBM?.toLowerCase().includes(searchLower) ||
          item.content.toLowerCase().includes(searchLower) ||
          item.contentBM?.toLowerCase().includes(searchLower)
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sort.field) {
        case 'date':
          aValue = new Date(a.date)
          bValue = new Date(b.date)
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'category':
          aValue = a.category
          bValue = b.category
          break
        default:
          return 0
      }

      if (aValue < bValue) {
        return sort.order === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sort.order === 'asc' ? 1 : -1
      }
      return 0
    })

    // Apply pagination
    const total = filtered.length
    const totalPages = Math.ceil(total / pagination.limit)
    const startIndex = (pagination.page - 1) * pagination.limit
    const endIndex = startIndex + pagination.limit
    const items = filtered.slice(startIndex, endIndex)

    return {
      items,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    }
  }

  // Get content statistics
  static getContentStats(announcements: Announcement[], news: NewsItem[]) {
    const now = new Date()
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)

    const announcementsThisMonth = announcements.filter(
      item => new Date(item.date) >= thisMonth
    ).length

    const announcementsLastMonth = announcements.filter(
      item =>
        new Date(item.date) >= lastMonth && new Date(item.date) < thisMonth
    ).length

    const newsThisMonth = news.filter(
      item => new Date(item.date) >= thisMonth
    ).length

    const newsLastMonth = news.filter(
      item =>
        new Date(item.date) >= lastMonth && new Date(item.date) < thisMonth
    ).length

    const featuredAnnouncements = announcements.filter(
      item => item.featured
    ).length

    const categories = {
      announcements: [...new Set(announcements.map(item => item.category))],
      news: [...new Set(news.map(item => item.category))],
    }

    return {
      announcements: {
        total: announcements.length,
        thisMonth: announcementsThisMonth,
        lastMonth: announcementsLastMonth,
        featured: featuredAnnouncements,
        categories: categories.announcements,
      },
      news: {
        total: news.length,
        thisMonth: newsThisMonth,
        lastMonth: newsLastMonth,
        categories: categories.news,
      },
    }
  }

  // Generate content sitemap
  static generateContentSitemap(
    announcements: Announcement[],
    news: NewsItem[]
  ) {
    const pages = [
      { url: '/', priority: 1.0, changeFrequency: 'daily' as const },
      {
        url: '/announcements',
        priority: 0.9,
        changeFrequency: 'daily' as const,
      },
      { url: '/news', priority: 0.9, changeFrequency: 'daily' as const },
      { url: '/corporate', priority: 0.8, changeFrequency: 'weekly' as const },
      {
        url: '/corporate/about',
        priority: 0.7,
        changeFrequency: 'monthly' as const,
      },
      { url: '/procedure', priority: 0.8, changeFrequency: 'weekly' as const },
      { url: '/contact', priority: 0.7, changeFrequency: 'monthly' as const },
    ]

    // Add announcement pages
    announcements.forEach(announcement => {
      pages.push({
        url: `/announcements/${announcement.id}`,
        priority: announcement.featured ? 0.8 : 0.6,
        changeFrequency: 'weekly' as const,
        lastModified: announcement.date,
      })
    })

    // Add news pages
    news.forEach(newsItem => {
      pages.push({
        url: `/news/${newsItem.id}`,
        priority: 0.6,
        changeFrequency: 'weekly' as const,
        lastModified: newsItem.date,
      })
    })

    return pages
  }

  // Search across all content
  static searchContent(
    query: string,
    announcements: Announcement[],
    news: NewsItem[],
    options: { limit?: number; includeContent?: boolean } = {}
  ) {
    const { limit = 20, includeContent = false } = options
    const searchLower = query.toLowerCase()
    const results: Array<{
      type: 'announcement' | 'news'
      id: string
      title: string
      titleBM?: string
      excerpt: string
      date: string
      url: string
      relevance: number
    }> = []

    // Search announcements
    announcements.forEach(item => {
      let relevance = 0
      const titleMatch = item.title.toLowerCase().includes(searchLower)
      const titleBMMatch = item.titleBM?.toLowerCase().includes(searchLower)
      const contentMatch =
        includeContent && item.content.toLowerCase().includes(searchLower)
      const contentBMMatch =
        includeContent && item.contentBM?.toLowerCase().includes(searchLower)

      if (titleMatch) {
        relevance += 10
      }
      if (titleBMMatch) {
        relevance += 10
      }
      if (contentMatch) {
        relevance += 5
      }
      if (contentBMMatch) {
        relevance += 5
      }

      if (relevance > 0) {
        results.push({
          type: 'announcement',
          id: item.id,
          title: item.title,
          titleBM: item.titleBM,
          excerpt: `${item.content.substring(0, 200)}...`,
          date: item.date,
          url: `/announcements/${item.id}`,
          relevance,
        })
      }
    })

    // Search news
    news.forEach(item => {
      let relevance = 0
      const titleMatch = item.title.toLowerCase().includes(searchLower)
      const titleBMMatch = item.titleBM?.toLowerCase().includes(searchLower)
      const excerptMatch = item.excerpt.toLowerCase().includes(searchLower)
      const excerptBMMatch = item.excerptBM?.toLowerCase().includes(searchLower)
      const contentMatch =
        includeContent && item.content.toLowerCase().includes(searchLower)
      const contentBMMatch =
        includeContent && item.contentBM?.toLowerCase().includes(searchLower)

      if (titleMatch) {
        relevance += 10
      }
      if (titleBMMatch) {
        relevance += 10
      }
      if (excerptMatch) {
        relevance += 7
      }
      if (excerptBMMatch) {
        relevance += 7
      }
      if (contentMatch) {
        relevance += 5
      }
      if (contentBMMatch) {
        relevance += 5
      }

      if (relevance > 0) {
        results.push({
          type: 'news',
          id: item.id,
          title: item.title,
          titleBM: item.titleBM,
          excerpt: item.excerpt,
          date: item.date,
          url: `/news/${item.id}`,
          relevance,
        })
      }
    })

    // Sort by relevance and date
    return results
      .sort((a, b) => {
        if (a.relevance !== b.relevance) {
          return b.relevance - a.relevance
        }
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      })
      .slice(0, limit)
  }
}
