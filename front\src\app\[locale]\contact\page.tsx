'use client'

export const runtime = 'edge'

import { Clock, Mail, MapPin, Phone } from 'lucide-react'
import { ContactForm } from '@/components/contact-form'
import { PageWrapper } from '@/components/page-wrapper'
import { CONTACT_INFO, OPERATION_HOURS } from '@/data/constants'
import { useLanguage } from '@/lib/language-context'

export default function ContactPage() {
  const { language } = useLanguage()

  const breadcrumbs = [
    {
      label: 'Contact Us',
      labelBM: 'Hubungi Kami',
    },
  ]

  return (
    <PageWrapper
      title="Contact Us"
      titleBM="Hubungi Kami"
      description="Get in touch with JAKIM's Halal Management Division for inquiries and support."
      descriptionBM="Hubungi Bahagian Pengurusan Halal JAKIM untuk pertanyaan dan sokongan."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Contact Information' : 'Maklumat Hubungan'}
          </h3>

          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <MapPin className="w-6 h-6 text-primary-green mt-1" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en' ? 'Address' : 'Alamat'}
                </h4>
                <p className="text-gray-600 whitespace-pre-line">
                  {language === 'bm'
                    ? CONTACT_INFO.address.bm
                    : CONTACT_INFO.address.en}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Phone className="w-6 h-6 text-primary-green mt-1" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en' ? 'Phone & Fax' : 'Telefon & Faks'}
                </h4>
                <p className="text-gray-600">
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  {CONTACT_INFO.phone}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">
                    {language === 'en' ? 'Fax:' : 'Faks:'}
                  </span>{' '}
                  {CONTACT_INFO.fax}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Mail className="w-6 h-6 text-primary-green mt-1" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en' ? 'Email' : 'Emel'}
                </h4>
                <p className="text-gray-600">
                  <a
                    href={`mailto:${CONTACT_INFO.email}`}
                    className="text-primary-green hover:text-primary-green-dark"
                  >
                    {CONTACT_INFO.email}
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Counter Operation Hours'
              : 'Waktu Operasi Kaunter'}
          </h3>

          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Clock className="w-6 h-6 text-primary-green mt-1" />
              </div>
              <div className="flex-1">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'en'
                        ? 'Monday - Thursday'
                        : 'Isnin - Khamis'}
                    </h4>
                    <p className="text-gray-600">
                      {language === 'bm'
                        ? OPERATION_HOURS.weekdays.bm.morning
                        : OPERATION_HOURS.weekdays.en.morning}
                    </p>
                    <p className="text-gray-600">
                      {language === 'bm'
                        ? OPERATION_HOURS.weekdays.bm.afternoon
                        : OPERATION_HOURS.weekdays.en.afternoon}
                    </p>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'en' ? 'Friday' : 'Jumaat'}
                    </h4>
                    <p className="text-gray-600">
                      {language === 'bm'
                        ? OPERATION_HOURS.friday.bm.morning
                        : OPERATION_HOURS.friday.en.morning}
                    </p>
                    <p className="text-gray-600">
                      {language === 'bm'
                        ? OPERATION_HOURS.friday.bm.afternoon
                        : OPERATION_HOURS.friday.en.afternoon}
                    </p>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'en' ? 'Saturday - Sunday' : 'Sabtu - Ahad'}
                    </h4>
                    <p className="text-gray-600">
                      {language === 'bm'
                        ? OPERATION_HOURS.weekend.bm
                        : OPERATION_HOURS.weekend.en}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Form Section */}
      <div className="card mb-12">
        <h3 className="text-2xl font-bold mb-6 text-gray-900">
          {language === 'en' ? 'Send us a Message' : 'Hantar Mesej kepada Kami'}
        </h3>
        <ContactForm
          onSubmit={async formData => {
            // Handle form submission
            console.log('Form submitted:', formData)
            // In a real app, this would send to your backend
            await new Promise(resolve => setTimeout(resolve, 2000))
          }}
          variant="default"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Quick Links' : 'Pautan Pantas'}
          </h3>
          <div className="space-y-4">
            <a
              href="https://myehalal.halal.gov.my/domestik/v1/"
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-colors"
            >
              <h4 className="font-semibold text-primary-green mb-2">
                {language === 'en' ? 'MYeHALAL Domestic' : 'MYeHALAL Domestik'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'Apply for domestic Halal certification'
                  : 'Mohon pensijilan Halal domestik'}
              </p>
            </a>

            <a
              href="https://myehalal.halal.gov.my/international/v1/pemohon/"
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-colors"
            >
              <h4 className="font-semibold text-primary-green mb-2">
                {language === 'en'
                  ? 'MYeHALAL International'
                  : 'MYeHALAL Antarabangsa'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'Apply for international Halal certification'
                  : 'Mohon pensijilan Halal antarabangsa'}
              </p>
            </a>

            <a
              href="https://islam.spab.gov.my/eApps/system/index.do"
              target="_blank"
              rel="noopener noreferrer"
              className="block p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-colors"
            >
              <h4 className="font-semibold text-primary-green mb-2">
                {language === 'en' ? 'E-Complaint System' : 'Sistem E-Aduan'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'Submit complaints and feedback'
                  : 'Hantar aduan dan maklum balas'}
              </p>
            </a>
          </div>
        </div>

        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Frequently Asked Questions' : 'Soalan Lazim'}
          </h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'How long does the certification process take?'
                  : 'Berapa lama proses pensijilan mengambil masa?'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'The certification process typically takes 5-9 weeks, depending on the complexity of your application.'
                  : 'Proses pensijilan biasanya mengambil masa 5-9 minggu, bergantung pada kerumitan permohonan anda.'}
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'What documents are required for application?'
                  : 'Apakah dokumen yang diperlukan untuk permohonan?'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'Required documents include company registration, product specifications, manufacturing process flow, and supplier certificates.'
                  : 'Dokumen yang diperlukan termasuk pendaftaran syarikat, spesifikasi produk, aliran proses pembuatan, dan sijil pembekal.'}
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'How much does Halal certification cost?'
                  : 'Berapakah kos pensijilan Halal?'}
              </h4>
              <p className="text-sm text-gray-600">
                {language === 'en'
                  ? 'Certification fees vary based on the type and scope of certification. Please contact us for detailed fee structure.'
                  : 'Yuran pensijilan berbeza berdasarkan jenis dan skop pensijilan. Sila hubungi kami untuk struktur yuran terperinci.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
