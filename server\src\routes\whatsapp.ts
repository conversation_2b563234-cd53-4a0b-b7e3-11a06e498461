import express, { type Request, type Response } from 'express'
import { v4 as uuidv4 } from 'uuid'
import openaiService from '../services/openai'
import whatsappService from '../services/whatsapp'
import type { ChatMessage, ChatSession, WhatsAppWebhookPayload } from '../types'

const router = express.Router()

// Store WhatsApp chat sessions (in production, use a database)
const whatsappSessions = new Map<string, ChatSession>()

// Webhook verification endpoint
router.get('/webhook', (req: Request, res: Response): void => {
  try {
    const mode = req.query['hub.mode'] as string
    const token = req.query['hub.verify_token'] as string
    const challenge = req.query['hub.challenge'] as string

    console.log('Webhook verification request:', { mode, token, challenge })

    const verificationResult = whatsappService.verifyWebhook(
      mode,
      token,
      challenge,
    )

    if (verificationResult) {
      console.log('Webhook verified successfully')
      res.status(200).send(verificationResult)
    } else {
      console.log('Webhook verification failed')
      res.status(403).send('Forbidden')
    }
  } catch (error) {
    console.error('Webhook verification error:', error)
    res.status(500).send('Internal Server Error')
  }
})

// Webhook message handler
router.post('/webhook', async (req: Request, res: Response): Promise<void> => {
  try {
    const payload: WhatsAppWebhookPayload = req.body

    // Verify webhook signature for security (optional but recommended)
    const signature = req.headers['x-hub-signature-256'] as string
    if (signature) {
      const isValid = whatsappService.verifyWebhookSignature(
        JSON.stringify(req.body),
        signature,
      )

      if (!isValid) {
        console.error('Invalid webhook signature')
        res.status(403).send('Forbidden')
        return
      }
    }

    console.log('Received WhatsApp webhook:', JSON.stringify(payload, null, 2))

    // Process incoming messages
    const messages = await whatsappService.processWebhookMessage(payload)

    for (const message of messages) {
      await handleIncomingMessage(message)
    }

    // Always respond with 200 to acknowledge receipt
    res.status(200).send('OK')
    return
  } catch (error) {
    console.error('Webhook processing error:', error)
    res.status(500).send('Internal Server Error')
    return
  }
})

// Handle incoming WhatsApp message and integrate with OpenAI
async function handleIncomingMessage(message: any): Promise<void> {
  try {
    const phoneNumber = message.from
    const messageText = message.content

    console.log(`Processing message from ${phoneNumber}: ${messageText}`)

    // Get or create session for this phone number
    let session = whatsappSessions.get(phoneNumber)
    if (!session) {
      session = {
        id: uuidv4(),
        messages: [
          {
            role: 'system',
            content:
              'You are a helpful assistant for Halal inquiries. You speak in Bahasa Malaysia by default. You may switch between Bahasa Malaysia, Chinese, English, and Tamil. Keep responses concise for WhatsApp.',
          },
        ],
        createdAt: new Date(),
      }
      whatsappSessions.set(phoneNumber, session)
    }

    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: messageText,
      timestamp: new Date(),
    }

    // Handle image messages
    if (message.type === 'image' && message.mediaUrl) {
      userMessage.content = `[Image received] ${messageText}`
      userMessage.imageUrl = message.mediaUrl
    }

    session.messages.push(userMessage)

    // Get response from OpenAI
    let openaiResponse

    if (message.type === 'image' && message.mediaUrl) {
      // Use image analysis for image messages
      openaiResponse = await openaiService.analyzeImage(
        message.mediaUrl,
        messageText ||
          "What's in this image? Please provide a brief description.",
      )
    } else {
      // Prepare messages for OpenAI (exclude timestamps)
      const openaiMessages = session.messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }))

      // Use text chat for text messages
      openaiResponse = await openaiService.sendTextMessage(openaiMessages)
    }

    if (openaiResponse.success && openaiResponse.message) {
      // Add assistant response to session
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: openaiResponse.message,
        timestamp: new Date(),
      }
      session.messages.push(assistantMessage)

      // Send response back to WhatsApp
      const sendResult = await whatsappService.sendTextMessage(
        phoneNumber,
        openaiResponse.message,
      )

      if (!sendResult.success) {
        console.error('Failed to send WhatsApp response:', sendResult.error)
      } else {
        console.log(
          `Sent response to ${phoneNumber}: ${openaiResponse.message}`,
        )
      }
    } else {
      console.error('OpenAI request failed:', openaiResponse.error)

      // Send error message to user
      await whatsappService.sendTextMessage(
        phoneNumber,
        "Maaf, saya menghadapi masalah teknikal. Sila cuba lagi sebentar lagi. (Sorry, I'm experiencing technical issues. Please try again in a moment.)",
      )
    }

    // Store session ID in message for tracking
    message.sessionId = session.id
  } catch (error) {
    console.error('Error handling incoming message:', error)

    // Send generic error message to user
    try {
      await whatsappService.sendTextMessage(
        message.from,
        'Maaf, berlaku ralat. Sila cuba lagi. (Sorry, an error occurred. Please try again.)',
      )
    } catch (sendError) {
      console.error('Failed to send error message:', sendError)
    }
  }
}

// Get session history for a phone number (admin endpoint)
router.get(
  '/sessions/:phoneNumber',
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { phoneNumber } = req.params
      const session = whatsappSessions.get(phoneNumber)

      if (!session) {
        res.status(404).json({
          error: 'Session not found',
          message: 'No chat session found for this phone number',
        })
        return
      }

      res.json({
        session: {
          id: session.id,
          phoneNumber,
          messageCount: session.messages.length,
          createdAt: session.createdAt,
          messages: session.messages.slice(-20), // Return last 20 messages
        },
      })
      return
    } catch (error) {
      console.error('Error getting session:', error)
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get session',
      })
      return
    }
  },
)

// Clear session for a phone number (admin endpoint)
router.delete(
  '/sessions/:phoneNumber',
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { phoneNumber } = req.params
      const deleted = whatsappSessions.delete(phoneNumber)

      if (deleted) {
        res.json({
          success: true,
          message: 'Session cleared successfully',
        })
        return
      }
      res.status(404).json({
        error: 'Session not found',
        message: 'No session found for this phone number',
      })
      return
    } catch (error) {
      console.error('Error clearing session:', error)
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to clear session',
      })
      return
    }
  },
)

// Get all active sessions (admin endpoint)
router.get('/sessions', async (_req: Request, res: Response): Promise<void> => {
  try {
    const sessions = Array.from(whatsappSessions.entries()).map(
      ([phoneNumber, session]) => ({
        phoneNumber,
        sessionId: session.id,
        messageCount: session.messages.length,
        createdAt: session.createdAt,
        lastActivity: session.messages[session.messages.length - 1]?.timestamp,
      }),
    )

    res.json({
      sessions,
      totalSessions: sessions.length,
    })
    return
  } catch (error) {
    console.error('Error getting sessions:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get sessions',
    })
    return
  }
})

export default router
