import { Hono } from 'hono';
import {
  createCollection,
  deleteCollection,
  getCollection,
  getCollections,
  updateCollection,
} from '@/controllers/adminCollectionsController';
import { authenticateAdmin } from '@/middleware/auth';

const collectionsRouter = new Hono();

// Apply authentication middleware to all routes
collectionsRouter.use('*', authenticateAdmin);

collectionsRouter.post('/', createCollection);
collectionsRouter.get('/', getCollections);
collectionsRouter.get('/:id', getCollection);
collectionsRouter.put('/:id', updateCollection);
collectionsRouter.delete('/:id', deleteCollection);

export default collectionsRouter;
