import type {
  ChatMessage,
  ChatSession,
  ConsolidatedMessageRequest,
  ConsolidatedMessageResponse,
  MessageHandlerConfig,
  MessageWithToolCalls,
  ToolCall,
  ToolCallResult,
  ToolMessage,
  WorkerEnv,
} from '../types'
import { logger, PerformanceTracker, RequestTracker } from '../utils/logger'
import DatabaseService from './database'
import HalalKnowledgeService from './halalKnowledge'
import openaiService from './openai'
import { AI_CONFIG } from '../constants'

class MessageHandlerService {
  private sessions = new Map<string, ChatSession>()

  private defaultConfig: MessageHandlerConfig = {
    maxMessageHistory: AI_CONFIG.MAX_MESSAGE_HISTORY,
    maxToolCallIterations: AI_CONFIG.MAX_TOOL_CALL_ITERATIONS,
    defaultModel: AI_CONFIG.FALLBACK,
    enableToolCalling: true,
    platform: 'web',
  }

  // Single consistent message for all technical issues
  private readonly GENERAL_TECHNICAL_ISSUE_MESSAGE =
    '<PERSON><PERSON>, saya menghadapi masalah teknikal dengan pangkalan data pengetahuan halal saya sekarang. Untuk mendapatkan panduan yang tepat mengenai soalan halal anda, sila rujuk kepada ulama yang berkelayakan atau pihak berkuasa Islam tempatan.'

  // Message when halal knowledge search works but finds no data
  private readonly HALAL_NOT_FOUND_MESSAGE =
    'Maaf, saya tidak menjumpai maklumat khusus tentang soalan halal ini dalam pangkalan data saya. Untuk mendapatkan panduan yang tepat mengenai soalan halal anda, sila rujuk kepada ulama yang berkelayakan atau pihak berkuasa Islam tempatan.'

  /**
   * Create a new DatabaseService instance for each request to avoid I/O context issues
   */
  private createDbService(env?: WorkerEnv): DatabaseService {
    return new DatabaseService(env)
  }

  /**
   * Consolidated message handler for all platforms
   */
  async handleIncomingMessage(
    request: ConsolidatedMessageRequest,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    // Initialize logger with environment
    logger.initialize(env)

    // Create request tracker for this message
    const tracker = new RequestTracker(
      request.sessionId,
      request.userId,
      request.platform,
    )
    const context = tracker.getContext()

    try {
      const config = { ...this.defaultConfig, ...request.config }
      const {
        message,
        sessionId,
        platform,
        messageType = 'text',
        mediaUrl,
        userId,
        systemPrompt,
        botSlug,
      } = request

      let botId = request.botId
      // If botId is not provided but botSlug is, query the bot to get the ID
      if (!botId && botSlug) {
        const dbService = this.createDbService(env)
        const bot = await dbService.getBotBySlug(botSlug)
        if (!bot) {
          logger.error('Bot not found', {
            ...context,
            botSlug,
          })
          throw new Error(`Bot not found with slug: ${botSlug}`)
        }
        botId = bot.id
      }

      // Update context with message details
      tracker.updateContext({
        messageType,
        hasMedia: !!mediaUrl,
        botId,
        botSlug,
      })

      logger.info(`Processing ${platform} message from ${userId}`, {
        ...context,
        messagePreview: message.substring(0, 100),
        messageLength: message.length,
        messageType,
        hasMedia: !!mediaUrl,
        botId,
        botSlug,
      })
      if (!botSlug) {
        throw new Error('Bot slug is required for message handling')
      }
      // Check if session is handed over to an agent
      tracker.stage(
        'HANDOVER_CHECK',
        'Checking if session is handed over to agent',
      )
      if (platform === 'web') {
        const dbService = this.createDbService(env)
        const dbSession = await dbService.getChatSession(sessionId)
        if (dbSession?.isHandedOver) {
          // Session is handled by an agent, don't process with AI
          logger.info(
            'Session is handed over to agent, storing message only',
            context,
          )
          await dbService.addChatMessage(sessionId, 'user', message)
          tracker.complete(true, 'Message stored for agent handling')
          return {
            success: true,
            message:
              'Your message has been received. An agent will respond shortly.',
            sessionId,
          }
        }
      }

      // Get or create session
      tracker.stage('SESSION_SETUP', 'Getting or creating chat session')
      const session = await this.getOrCreateSession(
        sessionId,
        userId,
        platform,
        systemPrompt,
        env,
        botId,
      )

      // Add user message to session
      const userMessage: ChatMessage = {
        role: 'user',
        content: message,
        timestamp: new Date(),
        imageUrl: mediaUrl,
      }

      session.messages.push(userMessage)
      logger.debug('Added user message to session', {
        ...context,
        messageCount: session.messages.length,
      })

      // Store user message in database for web and Facebook platforms
      if (platform === 'web' || platform === 'facebook') {
        tracker.stage('DB_STORE', 'Storing user message in database')
        const dbService = this.createDbService(env)
        await dbService.addChatMessage(
          sessionId,
          'user',
          message,
          undefined,
          mediaUrl,
        )

        // Also store in platform-specific table for Facebook
        if (platform === 'facebook') {
          // Generate a unique message ID for Facebook messages
          const messageId = `fb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          await dbService.logFacebookMessage(
            messageId,
            userId || sessionId, // sender ID
            'page', // recipient (our page)
            messageType,
            message,
            'inbound',
            mediaUrl,
            sessionId,
          )
        }

        logger.database('INSERT', 'Stored user message', context)
      }

      // Trim message history to max limit
      this.trimMessageHistory(session, config.maxMessageHistory)
      logger.debug('Trimmed message history', {
        ...context,
        finalMessageCount: session.messages.length,
        maxHistory: config.maxMessageHistory,
      })

      let response: ConsolidatedMessageResponse

      // Handle different message types
      tracker.stage('MESSAGE_PROCESSING', `Processing ${messageType} message`)
      const processingTracker = new PerformanceTracker(
        `${messageType}_message_processing`,
        context,
      )

      if (messageType === 'image' && mediaUrl) {
        logger.info('Processing image message', {
          ...context,
          imageUrl: mediaUrl,
        })
        response = await this.handleImageMessage(
          session,
          mediaUrl,
          message,
          config,
          env,
        )
      } else if (messageType === 'audio' && mediaUrl) {
        logger.info('Processing audio message', {
          ...context,
          audioUrl: mediaUrl,
        })
        response = await this.handleAudioMessage(session, mediaUrl, config, env)
      } else if (messageType === 'document' && mediaUrl) {
        logger.info('Processing document message', {
          ...context,
          documentUrl: mediaUrl,
        })
        response = await this.handleDocumentMessage(
          session,
          mediaUrl,
          message,
          config,
          env,
        )
      } else {
        logger.info('Processing text message', context)
        response = await this.handleTextMessage(session, config, env)
      }

      processingTracker.end()

      // Store assistant response in database for web and Facebook platforms
      if (
        (platform === 'web' || platform === 'facebook') &&
        response.success &&
        response.message
      ) {
        tracker.stage('DB_RESPONSE', 'Storing assistant response in database')
        const dbService = this.createDbService(env)
        await dbService.addChatMessage(sessionId, 'assistant', response.message)

        // Also store in platform-specific table for Facebook
        if (platform === 'facebook') {
          // Generate a unique message ID for Facebook assistant response
          const messageId = `fb_assistant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          await dbService.logFacebookMessage(
            messageId,
            'page', // sender (our page)
            userId || sessionId, // recipient ID
            'text',
            response.message,
            'outbound',
            undefined,
            sessionId,
          )
        }

        logger.database('INSERT', 'Stored assistant response', context)
      }

      // Update session
      this.sessions.set(sessionId, session)
      logger.session('UPDATE', sessionId, context)

      // Log final response
      if (response.success) {
        logger.success('Message processed successfully', {
          ...context,
          responseLength: response.message?.length || 0,
          toolCallsExecuted: response.toolCallsExecuted || 0,
        })
        tracker.complete(true)
      } else {
        logger.error('Message processing failed', {
          ...context,
          error: response.error,
        })
        tracker.complete(false, response.error)
      }

      return {
        ...response,
        sessionId,
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred'
      logger.error('Error in consolidated message handler', context, error)
      tracker.complete(false, errorMessage)

      return {
        success: false,
        error: errorMessage,
        sessionId: request.sessionId,
      }
    }
  }

  /**
   * Handle text messages with tool calling support
   */
  private async handleTextMessage(
    session: ChatSession,
    config: MessageHandlerConfig,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    logger.info('Handling text message', {
      messageCount: session.messages.length,
      message: session.messages,
      session,
      config,
    })
    if (!config.enableToolCalling) {
      // Fallback to regular OpenAI without tools
      return this.handleRegularTextMessage(session, config, env)
    }

    const tools = openaiService.getAvailableTools()
    let toolCallsExecuted = 0
    const currentMessages = [...session.messages]
    // Tool calling loop
    for (
      let iteration = 0;
      iteration < config.maxToolCallIterations;
      iteration++
    ) {
      // Prepare messages for OpenAI (exclude timestamps)
      const openaiMessages = currentMessages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }))

      // Get response from OpenAI with tools
      const response = await openaiService.sendMessageWithTools(
        openaiMessages,
        tools,
        config.defaultModel,
        env,
        config.temperature || 0.7,
      )

      if (!response.success) {
        return {
          success: false,
          error: response.error || 'Failed to get AI response',
          toolCallsExecuted,
          sessionId: session.id,
        }
      }

      // If no tool calls, we have the final response
      if (!response.toolCalls || response.toolCalls.length === 0) {
        if (toolCallsExecuted > 0) {
          console.log(
            `✅ AI Agent generated final response after processing ${toolCallsExecuted} tool call(s)`,
          )
        } else {
          console.log(
            '🧠 AI Agent answered using own knowledge (no tools called)',
          )
        }

        // Add assistant response to session
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.message || '',
          timestamp: new Date(),
        }
        session.messages.push(assistantMessage)

        return {
          success: true,
          message: response.message || '',
          usage: response.usage,
          toolCallsExecuted,
          sessionId: session.id,
        }
      }

      // Process tool calls
      const assistantMessageWithTools: MessageWithToolCalls = {
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        toolCalls: response.toolCalls,
      }

      currentMessages.push(assistantMessageWithTools)

      // Execute each tool call
      for (const toolCall of response.toolCalls) {
        console.log(`🔧 AI Agent called tool: ${toolCall.function.name}`)

        const toolResult = await this.executeToolCall(toolCall, session.id, env)
        toolCallsExecuted++

        // Handle halal knowledge search results - let AI agent process the knowledge
        if (toolCall.function.name === 'search_halal_knowledge') {
          if (!toolResult.success) {
            // Case: Server error - return technical issue message directly
            const responseMessage = this.GENERAL_TECHNICAL_ISSUE_MESSAGE
            const assistantMessage: ChatMessage = {
              role: 'assistant',
              content: responseMessage,
              timestamp: new Date(),
            }
            session.messages.push(assistantMessage)

            return {
              success: true,
              message: responseMessage,
              toolCallsExecuted,
              sessionId: session.id,
            }
          }

          if (!toolResult.result || typeof toolResult.result !== 'string') {
            // Case: R2R worked but no data found - return "not found" message directly
            const responseMessage = this.HALAL_NOT_FOUND_MESSAGE
            const assistantMessage: ChatMessage = {
              role: 'assistant',
              content: responseMessage,
              timestamp: new Date(),
            }
            session.messages.push(assistantMessage)

            return {
              success: true,
              message: responseMessage,
              toolCallsExecuted,
              sessionId: session.id,
            }
          }

          // Case: Found data - let AI agent continue processing with the knowledge
          console.log(
            '📚 R2R search successful - AI agent will process halal knowledge with character limits',
          )
          // Don't return here, let the tool calling loop continue
        }

        const toolMessage: ToolMessage = {
          role: 'tool',
          content: JSON.stringify(toolResult.result),
          timestamp: new Date(),
          toolCallId: toolCall.id,
        }

        currentMessages.push(toolMessage)
      }
    }

    // If we've reached max iterations, provide a fallback response instead of failing
    console.log(
      `Maximum tool call iterations (${config.maxToolCallIterations}) reached, providing technical issue response`,
    )

    // Return technical issue message instead of trying to provide answers
    return {
      success: true,
      message: this.GENERAL_TECHNICAL_ISSUE_MESSAGE,
      toolCallsExecuted,
      sessionId: session.id,
    }
  }

  /**
   * Handle regular text messages without tool calling
   */
  private async handleRegularTextMessage(
    session: ChatSession,
    config: MessageHandlerConfig,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    // Prepare messages for OpenAI (exclude timestamps)
    const openaiMessages = session.messages.map((msg) => ({
      role: msg.role,
      content: msg.content,
    }))

    // Get response from OpenAI
    const response = await openaiService.sendTextMessage(
      openaiMessages,
      config.defaultModel,
      env,
      config.temperature || 0.7,
    )

    if (!response.success) {
      return {
        success: false,
        error: response.error || 'Failed to get AI response',
        sessionId: session.id,
      }
    }

    // Add assistant response to session
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response.message || '',
      timestamp: new Date(),
    }
    session.messages.push(assistantMessage)

    return {
      success: true,
      message: response.message || '',
      usage: response.usage,
      sessionId: session.id,
    }
  }

  /**
   * Handle image messages
   */
  private async handleImageMessage(
    session: ChatSession,
    imageUrl: string,
    prompt: string,
    config: MessageHandlerConfig,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    console.log(`MessageHandler: Processing image from URL: ${imageUrl}`)

    const response = await openaiService.analyzeImage(
      imageUrl,
      prompt || "What's in this image? Please provide a brief description.",
      config.defaultModel,
      env,
    )

    if (!response.success) {
      console.error(`MessageHandler: Image analysis failed: ${response.error}`)
      return {
        success: false,
        error: response.error || 'Failed to analyze image',
        sessionId: session.id,
      }
    }

    console.log('MessageHandler: Image analyzed successfully')

    // Add assistant response to session
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response.message || '',
      timestamp: new Date(),
    }
    session.messages.push(assistantMessage)

    return {
      success: true,
      message: response.message || '',
      usage: response.usage,
      sessionId: session.id,
    }
  }

  /**
   * Handle audio messages
   */
  private async handleAudioMessage(
    session: ChatSession,
    audioUrl: string,
    config: MessageHandlerConfig,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    try {
      console.log(`MessageHandler: Processing audio from URL: ${audioUrl}`)

      // Download audio file (assumes URL is accessible - either S3 or authenticated)
      const audioResponse = await fetch(audioUrl)
      if (!audioResponse.ok) {
        console.error(
          `MessageHandler: Failed to download audio from ${audioUrl}: ${audioResponse.status} ${audioResponse.statusText}`,
        )
        throw new Error(`Failed to download audio: ${audioResponse.statusText}`)
      }

      // Convert to format OpenAI can handle
      const audioBlob = await audioResponse.blob()
      const audioFile = new File([audioBlob], 'audio.mp3', {
        type: 'audio/mpeg',
      })

      console.log(
        'MessageHandler: Audio downloaded successfully, transcribing with OpenAI Whisper',
      )

      // Transcribe audio using OpenAI Whisper
      const transcriptionResponse = await openaiService.transcribeAudio(
        audioFile,
        env,
      )

      if (!transcriptionResponse.success) {
        console.error(
          `MessageHandler: Audio transcription failed: ${transcriptionResponse.error}`,
        )
        return {
          success: false,
          error: transcriptionResponse.error || 'Failed to transcribe audio',
          sessionId: session.id,
        }
      }

      console.log(
        `MessageHandler: Audio transcribed successfully: "${transcriptionResponse.text}"`,
      )

      // Update the user message content with transcribed text
      const lastMessage = session.messages[session.messages.length - 1]
      if (lastMessage && lastMessage.role === 'user') {
        lastMessage.content =
          transcriptionResponse.text || 'Audio message received'
      }

      // Process the transcribed text through normal text handling
      return await this.handleTextMessage(session, config, env)
    } catch (error) {
      console.error('MessageHandler: Error handling audio message:', error)
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to process audio',
        sessionId: session.id,
      }
    }
  }

  /**
   * Handle document messages (PDF, DOC, TXT, etc.)
   *
   * NOTE: OpenAI Vision API currently only supports image formats (PNG, JPEG, GIF, WebP).
   * Document formats like PDF, DOC, TXT, etc. will fail with "unsupported image" error.
   *
   * This method is implemented and ready for when OpenAI adds document support,
   * or can be enhanced with text extraction libraries in the future.
   *
   * Current behavior:
   * - Documents are uploaded to S3 successfully ✅
   * - Document routing works correctly ✅
   * - OpenAI rejects non-image formats ❌
   * - Users get clear error message explaining limitation ✅
   */
  private async handleDocumentMessage(
    session: ChatSession,
    documentUrl: string,
    prompt: string,
    config: MessageHandlerConfig,
    env?: WorkerEnv,
  ): Promise<ConsolidatedMessageResponse> {
    console.log(`MessageHandler: Processing document from URL: ${documentUrl}`)

    // Attempt to pass document URL to OpenAI Vision API
    // NOTE: This will fail for non-image formats due to OpenAI limitations
    const response = await openaiService.analyzeDocument(
      documentUrl,
      prompt ||
        'Please analyze this document and provide a summary of its contents.',
      config.defaultModel,
      env,
    )

    if (!response.success) {
      // Improve error message for document processing
      let errorMessage = response.error || 'Failed to analyze document'

      // If OpenAI says "unsupported image", clarify it's about document format
      if (
        errorMessage.includes('unsupported image') ||
        errorMessage.includes('image has of one')
      ) {
        errorMessage =
          'Document format not supported. OpenAI can only analyze image formats (PNG, JPEG, GIF, WebP), not PDF or other document formats.'
        console.error(
          `MessageHandler: Document analysis failed - OpenAI doesn't support document formats: ${response.error}`,
        )
      } else {
        console.error(
          `MessageHandler: Document analysis failed: ${response.error}`,
        )
      }

      return {
        success: false,
        error: errorMessage,
        sessionId: session.id,
      }
    }

    console.log('MessageHandler: Document analyzed successfully')

    // Add assistant response to session
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response.message || '',
      timestamp: new Date(),
    }
    session.messages.push(assistantMessage)

    return {
      success: true,
      message: response.message || '',
      usage: response.usage,
      sessionId: session.id,
    }
  }

  /**
   * Execute a tool call
   */
  private async executeToolCall(
    toolCall: ToolCall,
    sessionId: string,
    env?: WorkerEnv,
  ): Promise<ToolCallResult> {
    const context = { sessionId, toolCall: toolCall.function.name }
    const tracker = new PerformanceTracker(
      `tool_${toolCall.function.name}`,
      context,
    )

    try {
      logger.toolCall(toolCall.function.name, 'Starting execution', context)

      switch (toolCall.function.name) {
        case 'search_halal_knowledge': {
          const result = await this.executeHalalKnowledgeSearch(
            toolCall,
            sessionId,
            env,
          )
          tracker.end()
          logger.toolCall(
            toolCall.function.name,
            `Completed with ${result.success ? 'success' : 'failure'}`,
            context,
          )
          return result
        }
        default: {
          tracker.end()
          logger.warn(
            `Unknown tool requested: ${toolCall.function.name}`,
            context,
          )
          return {
            toolCallId: toolCall.id,
            success: false,
            result: `Unknown tool: ${toolCall.function.name}`,
            error: `Tool ${toolCall.function.name} is not implemented`,
          }
        }
      }
    } catch (error) {
      tracker.end()
      logger.error(
        `Tool execution failed: ${toolCall.function.name}`,
        context,
        error,
      )

      // Detailed error message for developer debugging
      let errorMessage = `Tool execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      if (toolCall.function.name === 'search_halal_knowledge') {
        errorMessage = `Halal knowledge search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }

      return {
        toolCallId: toolCall.id,
        success: false,
        result: errorMessage,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Execute halal knowledge search tool
   */
  private async executeHalalKnowledgeSearch(
    toolCall: ToolCall,
    sessionId: string,
    env?: WorkerEnv,
  ): Promise<ToolCallResult> {
    try {
      const args = JSON.parse(toolCall.function.arguments)
      const { query, maxResults = 5, minScore = 0.3 } = args

      const halalKnowledgeService = new HalalKnowledgeService()
      const response = await halalKnowledgeService.searchAndAnswer(
        {
          query,
          sessionId,
          maxResults,
          minScore,
          includeContext: true,
        },
        env,
      )

      // Return raw results - let main handler decide what to tell user
      return {
        toolCallId: toolCall.id,
        success: response.success,
        result: response.success ? response.answer : null,
        error: response.error,
      }
    } catch (error) {
      return {
        toolCallId: toolCall.id,
        success: false,
        result: 'Failed to parse tool arguments or execute search',
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Get or create a chat session
   */
  private async getOrCreateSession(
    sessionId: string,
    userId: string,
    platform: string,
    systemPrompt: string | undefined,
    env?: WorkerEnv,
    botId?: number,
  ): Promise<ChatSession> {
    const context = { sessionId, userId, platform }

    // Try to get from memory first
    let session = this.sessions.get(sessionId)

    if (session) {
      // Session exists in memory - ensure it has system prompt
      logger.session('FOUND_MEMORY', sessionId, {
        ...context,
        messageCount: session.messages.length,
      })
      this.ensureSystemPrompt(session, platform, systemPrompt)
      this.sessions.set(sessionId, session)
      return session
    }

    // Session not in memory - try to load from database or create new
    logger.session('NOT_IN_MEMORY', sessionId, context)
    session = await this.loadOrCreateSession(
      sessionId,
      userId,
      platform,
      systemPrompt,
      env,
      botId,
    )

    // Cache the session in memory
    this.sessions.set(sessionId, session)
    logger.session('CACHED', sessionId, {
      ...context,
      messageCount: session.messages.length,
    })

    return session
  }

  /**
   * Load session from database or create a new one
   */
  private async loadOrCreateSession(
    sessionId: string,
    userId: string,
    platform: string,
    systemPrompt: string | undefined,
    env?: WorkerEnv,
    botId?: number,
  ): Promise<ChatSession> {
    // Try to load from database for web and Facebook platforms
    if (platform === 'web' || platform === 'facebook') {
      const dbService = this.createDbService(env)
      const dbSession = await dbService.getChatSession(sessionId)

      if (dbSession) {
        // Ensure system prompt is present
        this.ensureSystemPrompt(dbSession, platform, systemPrompt)
        return dbSession
      }
    }

    // Create new session
    const newSession: ChatSession = {
      id: sessionId,
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt(platform, systemPrompt),
        },
      ],
      createdAt: new Date(),
      userId,
      platform,
      platformId: userId,
      botId, // Store bot ID with session
    }

    // Store in database for web and Facebook platforms
    if (platform === 'web' || platform === 'facebook') {
      const dbService = this.createDbService(env)
      await dbService.createChatSession(
        sessionId,
        platform,
        undefined,
        userId,
        botId,
      )
    }

    return newSession
  }

  /**
   * Ensure session has system prompt at the beginning
   */
  private ensureSystemPrompt(
    session: ChatSession,
    platform: string,
    systemPrompt: string | undefined,
  ): void {
    const prompt = this.getSystemPrompt(platform, systemPrompt)

    // Check if first message is already a system message with current prompt
    const firstMessage = session.messages[0]
    if (firstMessage?.role === 'system' && firstMessage.content === prompt) {
      return // System prompt already present and up-to-date
    }

    // Remove any existing system messages
    session.messages = session.messages.filter((msg) => msg.role !== 'system')

    // Add current system prompt at the beginning
    session.messages.unshift({
      role: 'system',
      content: prompt,
    })
  }

  /**
   * Get system prompt based on platform
   */
  private getSystemPrompt(platform: string, systemPrompt?: string): string {
    if (systemPrompt) {
      return systemPrompt
    }
    console.log(`Missing system prompt for platform: ${platform}`, {
      systemPrompt,
    })
    const basePrompt = `You are a helpful assistant specializing in halal knowledge. Your goal is to provide accurate and helpful information to users. You should be friendly and approachable, and you should always be willing to help users with their questions. You can provide information in a variety of languages, including English, Malay, and Chinese.
`

    switch (platform) {
      case 'whatsapp':
        return `${basePrompt} Keep responses concise for WhatsApp.`
      case 'facebook':
        return `${basePrompt} Keep responses concise for Facebook Messenger.`
      case 'twilio':
        return `${basePrompt}

🚨 CRITICAL WHATSAPP CONSTRAINT 🚨
Your response MUST be under 1500 characters. Messages exceeding this limit will FAIL to send.

MANDATORY RULES:
1. Count every character including spaces and punctuation
2. If your response approaches 1500 characters, STOP and summarize
3. Prioritize the most essential halal ruling only
4. Use short, clear sentences
5. Skip examples if space is limited

REMEMBER: A short helpful answer is better than a long failed message.
CHARACTER LIMIT: 1500 - NO EXCEPTIONS!`
      case 'web':
        return `${basePrompt} You can provide detailed responses for web chat.`
      default:
        // If platform is not recognized, or if it's 'web' (already handled), return basePrompt or a generic one.
        // The previous default case effectively did this.
        return basePrompt
    }
  }

  /**
   * Trim message history to keep only the most recent messages
   */
  private trimMessageHistory(session: ChatSession, maxMessages: number): void {
    if (session.messages.length <= maxMessages) {
      return
    }

    // Keep system message and the most recent user/assistant messages
    const systemMessages = session.messages.filter(
      (msg) => msg.role === 'system',
    )
    const otherMessages = session.messages.filter(
      (msg) => msg.role !== 'system',
    )

    // Keep the most recent messages (excluding system messages from count)
    const recentMessages = otherMessages.slice(
      -(maxMessages - systemMessages.length),
    )

    session.messages = [...systemMessages, ...recentMessages]
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId)
  }

  /**
   * Clear session from memory
   */
  clearSession(sessionId: string): void {
    this.sessions.delete(sessionId)
  }
}

// Create a new instance for each request to avoid I/O context issues in Cloudflare Workers
export function createMessageHandlerService(): MessageHandlerService {
  return new MessageHandlerService()
}

export default MessageHandlerService
