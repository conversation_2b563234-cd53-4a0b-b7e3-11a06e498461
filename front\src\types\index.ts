// Type definitions for Halal Malaysia Portal

export type Language = 'en' | 'bm'

export interface LocalizedText {
  en: string
  bm: string
}

export interface NavItem {
  id: string
  label: string
  labelBM: string
  href: string
  children?: NavItem[]
  external?: boolean
}

export interface QuickLink {
  id: string
  title: string
  titleBM: string
  description: string
  descriptionBM: string
  href: string
  icon: string
  external?: boolean
}

export interface Announcement {
  id: string
  title: string
  titleBM: string
  date: string
  content: string
  contentBM: string
  category: 'announcement' | 'media-statement' | 'withdrawal' | 'general'
  featured?: boolean
  pdfUrl?: string
}

export interface NewsItem {
  id: string
  title: string
  titleBM: string
  date: string
  excerpt: string
  excerptBM: string
  content: string
  contentBM: string
  image?: string
  category: string
  categoryBM: string
}

export interface SliderItem {
  id: string
  title: string
  titleBM: string
  image: string
  href?: string
  description?: string
  descriptionBM?: string
  isExternal?: boolean
}

export interface ContactInfo {
  address: LocalizedText
  phone: string
  fax: string
  email: string
}

export interface OperationHours {
  weekdays: {
    en: {
      morning: string
      afternoon: string
    }
    bm: {
      morning: string
      afternoon: string
    }
  }
  friday: {
    en: {
      morning: string
      afternoon: string
    }
    bm: {
      morning: string
      afternoon: string
    }
  }
  weekend: LocalizedText
}

export interface FooterLink {
  title: string
  titleBM: string
  href: string
  logo?: string
  external?: boolean
}

export interface SocialLink {
  platform: string
  href: string
  icon: string
}

export interface SearchResult {
  id: string
  companyName: string
  productName: string
  certificateNumber: string
  status: 'valid' | 'expired' | 'suspended' | 'revoked'
  expiryDate: string
  category: string
}

export interface HalalCertificate {
  id: string
  certificateNumber: string
  companyName: string
  companyNameBM?: string
  productName: string
  productNameBM?: string
  category: string
  categoryBM?: string
  issueDate: string
  expiryDate: string
  status: 'valid' | 'expired' | 'suspended' | 'revoked'
  certificationBody: string
  country: string
  countryBM?: string
}

export interface Document {
  id: string
  title: string
  titleBM: string
  description?: string
  descriptionBM?: string
  category: string
  categoryBM: string
  fileUrl: string
  fileSize: string
  fileType: 'pdf' | 'doc' | 'docx' | 'xls' | 'xlsx'
  publishDate: string
  language: Language[]
}

export interface FormField {
  id: string
  name: string
  label: string
  labelBM: string
  type:
    | 'text'
    | 'email'
    | 'tel'
    | 'textarea'
    | 'select'
    | 'checkbox'
    | 'radio'
    | 'file'
  required?: boolean
  placeholder?: string
  placeholderBM?: string
  options?: Array<{
    value: string
    label: string
    labelBM: string
  }>
  validation?: {
    pattern?: string
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
  }
}

export interface ContactForm {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  category: string
  language: Language
}

export interface PageMeta {
  title: string
  titleBM: string
  description: string
  descriptionBM: string
  keywords: string[]
  keywordsBM: string[]
  ogImage?: string
  canonical?: string
}

// Admin Panel Specific Types
import { UserRole } from './roles'
export { UserRole }

// Unified User interface
export interface User {
  id: number
  siteId: number
  username: string
  email?: string | null
  firstName?: string | null
  lastName?: string | null
  roles: UserRole[] // Array of roles - users can have multiple roles
  isActive: boolean
  isOnline: boolean
  lastSeenAt?: string | null
  lastLoginAt?: string | null
  createdAt: string
  updatedAt: string
}

// Legacy AdminUser interface for backward compatibility
export interface AdminUser {
  // For displaying user info, e.g., from /me
  id: number
  username: string
  role: UserRole // Keep single role for backward compatibility
  // Add other fields if /me returns them, like createdAt, lastLoginAt
}

export interface AdminUserResponse extends AdminUser {
  // For list/get by id
  email?: string | null
  firstName?: string | null
  lastName?: string | null
  roles: UserRole[] // Multiple roles supported
  isActive: boolean
  createdAt: string // Assuming string from JSON
  lastLoginAt?: string | null
}

export interface AdminUserCreationRequest {
  username: string
  email?: string
  password: string // Required for creation
  firstName?: string
  lastName?: string
  roles: UserRole[] // Multiple roles supported
  isActive?: boolean
}

export interface AdminUserUpdateRequest {
  username?: string
  email?: string
  password?: string // Optional: only if changing
  firstName?: string
  lastName?: string
  roles?: UserRole[] // Multiple roles supported
  isActive?: boolean
}

export interface S3ConfigurationPreset {
  serviceName: string
  endpointUrl?: string | null
  region?: string | null
}

// For S3 Config list/details, EDITOR sees a limited view
export interface S3ConfigurationBase {
  id: number
  serviceName: string
  bucketName: string
}
export interface S3ConfigurationFull extends S3ConfigurationBase {
  accessKeyId: string
  // secretAccessKey is usually not sent back, even to admins, for view.
  // It's write-only or fetched for specific operations if absolutely needed.
  // For this CRUD, assume it's not part of GET responses for existing configs.
  region?: string | null
  endpointUrl?: string | null
  createdAt?: string
  updatedAt?: string
}

// Union type for what API might return for S3 Configs
export type S3ConfigurationResponse = S3ConfigurationBase | S3ConfigurationFull

export interface S3ConfigurationCreationRequest {
  serviceName: string
  accessKeyId: string
  secretAccessKey: string // Required for creation
  bucketName: string
  region?: string | null
  endpointUrl?: string | null
}

export interface S3ConfigurationUpdateRequest
  extends Partial<Omit<S3ConfigurationCreationRequest, 'secretAccessKey'>> {
  secretAccessKey?: string // Optional: only if changing
}

export enum CollectionStatus {
  ACTIVE = 'ACTIVE',
  DISABLED = 'DISABLED',
}

export interface Collection {
  id: number
  name: string
  status: CollectionStatus
  createdAt?: string
  updatedAt?: string
  // _count?: { documents: number }; // If API sends document count
}

export interface CollectionCreateRequest {
  name: string
  status?: CollectionStatus
}
export interface CollectionUpdateRequest
  extends Partial<CollectionCreateRequest> {}

export interface DocumentResponse {
  id: number
  collectionId: number
  s3ConfigurationId: number
  s3Key: string
  filename: string
  filesize?: number | null
  mimetype?: string | null
  createdAt?: string
  updatedAt?: string
  collectionName?: string
  s3ConfigurationServiceName?: string
}

export interface PaginatedResponse<T> {
  items: T[] // Changed from 'documents' to generic 'items'
  total: number
  page: number
  limit: number
}

// Standardized API Response structure
export interface ApiResponse<T = any> {
  success: boolean // Required - indicates if the request was successful
  data?: T // The actual response data
  error?: string // Error message if success is false
  message?: string // Optional additional message
  // Legacy fields for backward compatibility - will be deprecated
  user?: AdminUser // For /me endpoint
  token?: string // For login
  pagination?: {
    // Example from api.ts usage
    total: number
    page: number
    limit: number
  }
}

// Service Types
export enum ServiceType {
  R2R_RAG = 'R2R_RAG',
  SMTP_PROVIDER = 'SMTP_PROVIDER',
  EXTERNAL_API = 'EXTERNAL_API',
}

// Base Service Configuration
export interface ServiceConfiguration {
  id: number
  siteId: number
  name: string
  type: ServiceType
  description?: string
  isActive: boolean
  configuration: string // JSON string for flexible config storage
  createdAt?: string
  updatedAt?: string
}

// R2R RAG Configuration
export interface R2RConfiguration {
  url: string
  username?: string
  password?: string
  collectionId?: string
  searchLimit?: number
  minScore?: number
}

// SMTP Provider Configuration
export interface SMTPConfiguration {
  host: string
  port: number
  secure: boolean // true for 465, false for other ports
  username: string
  password: string
  fromEmail: string
  fromName?: string
}

// External API Configuration
export interface ExternalAPIConfiguration {
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  retryAttempts?: number
}

// Service Creation Request
export interface ServiceCreationRequest {
  name: string
  type: ServiceType
  description?: string
  isActive?: boolean
  configuration: R2RConfiguration | SMTPConfiguration | ExternalAPIConfiguration
}

// Service Update Request
export interface ServiceUpdateRequest {
  name?: string
  description?: string
  isActive?: boolean
  configuration?:
    | R2RConfiguration
    | SMTPConfiguration
    | ExternalAPIConfiguration
}

// Search Parameters Interface
export interface SearchParams {
  query?: string
  status?: string
  category?: string
  country?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}
