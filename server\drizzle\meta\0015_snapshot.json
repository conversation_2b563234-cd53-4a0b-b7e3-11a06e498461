{"id": "b0fc3942-d560-4706-a0b0-30dfb4c6c342", "prevId": "90609b63-3f0b-47f5-8257-ea94ffa4c82d", "version": "7", "dialect": "postgresql", "tables": {"public.agent_messages": {"name": "agent_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "agent_name": {"name": "agent_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bot_usages": {"name": "bot_usages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "bot_id": {"name": "bot_id", "type": "integer", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "input_tokens": {"name": "input_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "output_tokens": {"name": "output_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "audio_tokens": {"name": "audio_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "video_tokens": {"name": "video_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bot_usages_bot_id_bots_id_fk": {"name": "bot_usages_bot_id_bots_id_fk", "tableFrom": "bot_usages", "tableTo": "bots", "columnsFrom": ["bot_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bots": {"name": "bots", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "temperature": {"name": "temperature", "type": "real", "primaryKey": false, "notNull": true, "default": 0.5}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "system_prompt": {"name": "system_prompt", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bots_site_id_sites_id_fk": {"name": "bots_site_id_sites_id_fk", "tableFrom": "bots", "tableTo": "sites", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bots_slug_unique": {"name": "bots_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'web'"}, "platform_id": {"name": "platform_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "is_handed_over": {"name": "is_handed_over", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collections": {"name": "collections", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "collection_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"collections_name_unique": {"name": "collections_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "registration_number": {"name": "registration_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "business_type": {"name": "business_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subcategory": {"name": "subcategory", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "postcode": {"name": "postcode", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "default": "'Malaysia'"}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "fax": {"name": "fax", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "certificate_number": {"name": "certificate_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "certificate_type": {"name": "certificate_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "certificate_status": {"name": "certificate_status", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "issued_date": {"name": "issued_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "page_number": {"name": "page_number", "type": "integer", "primaryKey": false, "notNull": false}, "raw_data": {"name": "raw_data", "type": "text", "primaryKey": false, "notNull": false}, "data_hash": {"name": "data_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_data_hash_unique": {"name": "companies_data_hash_unique", "nullsNotDistinct": false, "columns": ["data_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "collection_id": {"name": "collection_id", "type": "integer", "primaryKey": false, "notNull": true}, "s3_configuration_id": {"name": "s3_configuration_id", "type": "integer", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "filesize": {"name": "filesize", "type": "integer", "primaryKey": false, "notNull": false}, "mimetype": {"name": "mimetype", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_s3_key_unique": {"name": "documents_s3_key_unique", "nullsNotDistinct": false, "columns": ["s3_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facebook_config": {"name": "facebook_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "page_access_token": {"name": "page_access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "page_id": {"name": "page_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "app_secret": {"name": "app_secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "verify_token": {"name": "verify_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facebook_messages": {"name": "facebook_messages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "to": {"name": "to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "media_url": {"name": "media_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.handover_requests": {"name": "handover_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "requested_by": {"name": "requested_by", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'normal'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "assigned_to": {"name": "assigned_to", "type": "integer", "primaryKey": false, "notNull": false}, "requested_at": {"name": "requested_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_name": {"name": "product_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "certificate_number": {"name": "certificate_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "certificate_type": {"name": "certificate_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "issued_date": {"name": "issued_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "subcategory": {"name": "subcategory", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_info": {"name": "contact_info", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "raw_data": {"name": "raw_data", "type": "text", "primaryKey": false, "notNull": false}, "r2r_document_id": {"name": "r2r_document_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "embedding": {"name": "embedding", "type": "vector(3072)", "primaryKey": false, "notNull": false}, "vectorized_at": {"name": "vectorized_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "vectorization_model": {"name": "vectorization_model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "searchable_text": {"name": "searchable_text", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.s3_configurations": {"name": "s3_configurations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "service_name": {"name": "service_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_key_id": {"name": "access_key_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "secret_access_key": {"name": "secret_access_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "bucket_name": {"name": "bucket_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "region": {"name": "region", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "endpoint_url": {"name": "endpoint_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"s3_configurations_site_id_sites_id_fk": {"name": "s3_configurations_site_id_sites_id_fk", "tableFrom": "s3_configurations", "tableTo": "sites", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_analytics": {"name": "search_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "search_query": {"name": "search_query", "type": "text", "primaryKey": false, "notNull": true}, "search_type": {"name": "search_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "results_count": {"name": "results_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "has_results": {"name": "has_results", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "search_filters": {"name": "search_filters", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "service_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "configuration": {"name": "configuration", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"services_site_id_sites_id_fk": {"name": "services_site_id_sites_id_fk", "tableFrom": "services", "tableTo": "sites", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session_assignments": {"name": "session_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'active'"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sites": {"name": "sites", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "domains": {"name": "domains", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sites_code_unique": {"name": "sites_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.twilio_configs": {"name": "twilio_configs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "account_sid": {"name": "account_sid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "auth_token": {"name": "auth_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "webhook_url": {"name": "webhook_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"twilio_configs_site_id_sites_id_fk": {"name": "twilio_configs_site_id_sites_id_fk", "tableFrom": "twilio_configs", "tableTo": "sites", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.twilio_messages": {"name": "twilio_messages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "account_sid": {"name": "account_sid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "to": {"name": "to", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'text'"}, "media_url": {"name": "media_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "media_content_type": {"name": "media_content_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "direction": {"name": "direction", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "error_code": {"name": "error_code", "type": "integer", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"twilio_messages_site_id_sites_id_fk": {"name": "twilio_messages_site_id_sites_id_fk", "tableFrom": "twilio_messages", "tableTo": "sites", "columnsFrom": ["site_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "roles": {"name": "roles", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_online": {"name": "is_online", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_seen_at": {"name": "last_seen_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.whatsapp_config": {"name": "whatsapp_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone_number_id": {"name": "phone_number_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "webhook_verify_token": {"name": "webhook_verify_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "business_account_id": {"name": "business_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.whatsapp_messages": {"name": "whatsapp_messages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "site_id": {"name": "site_id", "type": "integer", "primaryKey": false, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "to": {"name": "to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "media_url": {"name": "media_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.collection_status": {"name": "collection_status", "schema": "public", "values": ["ACTIVE", "DISABLED"]}, "public.service_type": {"name": "service_type", "schema": "public", "values": ["R2R_RAG", "SMTP_PROVIDER", "EXTERNAL_API"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["ADMIN", "EDITOR", "AGENT", "SUPERVISOR"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}