import { type NextRequest, NextResponse } from 'next/server'
import { DocumentManager, documents } from '@/lib/document-manager'
export const runtime = 'edge'
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)

  // Extract filter parameters
  const filter = {
    category: searchParams.get('category') || undefined,
    fileType: searchParams.get('fileType') || undefined,
    language:
      (searchParams.get('language') as 'en' | 'bm' | 'both') || undefined,
    status:
      (searchParams.get('status') as 'published' | 'draft' | 'archived') ||
      'published',
    featured: searchParams.get('featured')
      ? searchParams.get('featured') === 'true'
      : undefined,
    dateFrom: searchParams.get('dateFrom') || undefined,
    dateTo: searchParams.get('dateTo') || undefined,
    search: searchParams.get('search') || undefined,
    tags: searchParams.get('tags')?.split(',').filter(Boolean) || undefined,
    department: searchParams.get('department') || undefined,
    minSize: searchParams.get('minSize')
      ? Number.parseInt(searchParams.get('minSize')!)
      : undefined,
    maxSize: searchParams.get('maxSize')
      ? Number.parseInt(searchParams.get('maxSize')!)
      : undefined,
  }

  // Extract sort parameters
  const sort = {
    field:
      (searchParams.get('sortBy') as
        | 'title'
        | 'uploadDate'
        | 'downloadCount'
        | 'fileSize'
        | 'category') || 'uploadDate',
    order: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
  }

  // Extract pagination parameters
  const pagination = {
    page: Number.parseInt(searchParams.get('page') || '1'),
    limit: Number.parseInt(searchParams.get('limit') || '10'),
  }

  try {
    // Filter and paginate documents
    const result = DocumentManager.filterDocuments(
      documents,
      filter,
      sort,
      pagination
    )

    // Get additional metadata
    const stats = DocumentManager.getDocumentStats()
    const categories = [...new Set(documents.map(doc => doc.category))]
    const fileTypes = [...new Set(documents.map(doc => doc.fileType))]
    const departments = [
      ...new Set(documents.map(doc => doc.department).filter(Boolean)),
    ]

    return NextResponse.json({
      success: true,
      data: {
        documents: result.documents,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
        },
        metadata: {
          categories,
          fileTypes,
          departments,
          stats,
          filters: filter,
          sort,
        },
      },
    })
  } catch (error) {
    console.error('Documents API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

// POST endpoint for document search with advanced filters
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      filters = {},
      sort = { field: 'uploadDate', order: 'desc' },
      pagination = { page: 1, limit: 10 },
      includeStats = false,
    } = body

    // Filter documents
    const result = DocumentManager.filterDocuments(
      documents,
      filters,
      sort,
      pagination
    )

    const response: any = {
      success: true,
      data: {
        documents: result.documents,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
        },
      },
    }

    // Include stats if requested
    if (includeStats) {
      response.data.stats = DocumentManager.getDocumentStats()
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Documents search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    )
  }
}
