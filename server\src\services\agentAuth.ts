import bcrypt from 'bcryptjs'
import type { NextFunction, Request, Response } from 'express'
import jwt from 'jsonwebtoken'
import type {
  AgentJWTPayload,
  AgentLoginRequest,
  AgentLoginResponse,
  AgentUser,
} from '../types'
import databaseService from './database'

class AgentAuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
  private readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '365d'
  private readonly SALT_ROUNDS = 10

  // Hash password
  hashPassword(password: string): string {
    return bcrypt.hashSync(password, this.SALT_ROUNDS)
  }

  // Compare password
  comparePassword(password: string, hash: string): boolean {
    return bcrypt.compareSync(password, hash)
  }

  // Generate JWT token for agent
  generateToken(
    agentId: number,
    username: string,
    role: string,
    env?: any,
  ): string {
    const secret = env?.JWT_SECRET || this.JWT_SECRET
    const payload: Omit<AgentJWTPayload, 'iat' | 'exp'> = {
      agentId,
      username,
      role,
    }

    return jwt.sign(payload, secret, { expiresIn: this.JWT_EXPIRES_IN })
  }

  // Verify JWT token
  verifyToken(token: string, env?: any): AgentJWTPayload | null {
    try {
      const secret = env?.JWT_SECRET || this.JWT_SECRET
      return jwt.verify(token, secret) as AgentJWTPayload
    } catch (error) {
      console.error('Token verification failed:', error)
      return null
    }
  }

  // Login agent user
  async login(
    credentials: AgentLoginRequest,
    dbService?: any,
    env?: any,
    siteId?: string,
  ): Promise<AgentLoginResponse> {
    try {
      const { username, password } = credentials
      const db = dbService || databaseService

      // Get agent user from database with siteId validation
      const agent = await db.getAgentByUsername(username, siteId)
      if (!agent) {
        return {
          success: false,
          error: 'Invalid username or password',
        }
      }

      // Check if agent is active
      if (!agent.isActive) {
        return {
          success: false,
          error: 'Account is deactivated. Please contact your administrator.',
        }
      }

      // Verify password
      if (!this.comparePassword(password, agent.passwordHash)) {
        return {
          success: false,
          error: 'Invalid username or password',
        }
      }

      // Update last seen time and set online status
      await db.updateAgentLastSeen(agent.id)
      await db.updateAgentOnlineStatus(agent.id, true)

      // Generate token using the agent's primary role
      const token = this.generateToken(
        agent.id,
        agent.username,
        agent.role,
        env,
      )

      return {
        success: true,
        token,
        agent: {
          id: agent.id,
          username: agent.username,
          email: agent.email,
          firstName: agent.firstName,
          lastName: agent.lastName,
          role: agent.role,
          isActive: agent.isActive,
          isOnline: true,
          lastSeenAt: new Date(),
          createdAt: agent.createdAt,
          updatedAt: agent.updatedAt,
        },
      }
    } catch (error) {
      console.error('Agent login error:', error)
      return {
        success: false,
        error: 'Login failed. Please try again.',
      }
    }
  }

  // Logout agent (set offline status)
  async logout(agentId: number, dbService?: any): Promise<void> {
    try {
      const db = dbService || databaseService
      await db.updateAgentOnlineStatus(agentId, false)
      await db.updateAgentLastSeen(agentId)
    } catch (error) {
      console.error('Agent logout error:', error)
    }
  }

  // Middleware to authenticate agent requests
  authenticateAgent = (
    req: Request,
    res: Response,
    next: NextFunction,
  ): void => {
    try {
      const authHeader = req.headers.authorization

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          error: 'Access denied',
          message: 'No token provided',
        })
        return
      }

      const token = authHeader.substring(7) // Remove 'Bearer ' prefix
      const decoded = this.verifyToken(token)

      if (!decoded) {
        res.status(401).json({
          error: 'Access denied',
          message: 'Invalid token',
        })
        return
      }

      req.agent = decoded
      next()
    } catch (error) {
      console.error('Agent authentication error:', error)
      res.status(500).json({
        error: 'Internal server error',
        message: 'Authentication failed',
      })
    }
  }

  // Middleware to check agent role
  requireRole = (roles: string[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.agent) {
        res.status(401).json({
          error: 'Access denied',
          message: 'Authentication required',
        })
        return
      }

      if (!roles.includes(req.agent.role)) {
        res.status(403).json({
          error: 'Access denied',
          message: 'Insufficient permissions',
        })
        return
      }

      next()
    }
  }

  // Check if agent is authenticated
  isAuthenticated(req: Request): boolean {
    return !!req.agent
  }

  // Get current agent info
  getCurrentAgent(req: Request): AgentJWTPayload | null {
    return req.agent || null
  }

  // Update agent online status
  async updateOnlineStatus(
    agentId: number,
    isOnline: boolean,
    dbService?: any,
  ): Promise<void> {
    try {
      const db = dbService || databaseService
      await db.updateAgentOnlineStatus(agentId, isOnline)
      if (!isOnline) {
        await db.updateAgentLastSeen(agentId)
      }
    } catch (error) {
      console.error('Update online status error:', error)
    }
  }

  // Get agent profile
  async getAgentProfile(
    agentId: number,
    dbService?: any,
  ): Promise<AgentUser | null> {
    try {
      const db = dbService || databaseService
      return await db.getAgentById(agentId)
    } catch (error) {
      console.error('Get agent profile error:', error)
      return null
    }
  }
}

// Extend Express Request interface to include agent
declare global {
  namespace Express {
    interface Request {
      agent?: AgentJWTPayload
    }
  }
}

export default new AgentAuthService()
