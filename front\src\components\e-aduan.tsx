'use client'

import { ExternalLink, MessageSquare, Monitor, QrCode } from 'lucide-react'
import Image from 'next/image'
import { eAduanInfo } from '@/data/content'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'

interface EAduanProps {
  variant?: 'default' | 'compact' | 'card'
  showQR?: boolean
  showDescription?: boolean
  className?: string
}

export function EAduan({
  variant = 'default',
  showQR = true,
  showDescription = true,
  className,
}: EAduanProps) {
  const { language } = useLanguage()

  const title = language === 'bm' ? eAduanInfo.title.bm : eAduanInfo.title.en
  const description =
    language === 'bm' ? eAduanInfo.description.bm : eAduanInfo.description.en
  const buttonText =
    language === 'bm' ? eAduanInfo.buttonText.bm : eAduanInfo.buttonText.en

  if (variant === 'compact') {
    return (
      <div
        className={cn(
          'flex items-center gap-4 p-4 bg-white border border-gray-200 rounded-lg',
          className
        )}
      >
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-primary-green bg-opacity-10 rounded-lg flex items-center justify-center">
            <MessageSquare className="w-6 h-6 text-primary-green" />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
          <p className="text-sm text-gray-600 mb-2">
            {language === 'en'
              ? 'Submit complaints online'
              : 'Hantar aduan secara dalam talian'}
          </p>
          <a
            href={eAduanInfo.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 text-sm text-primary-green hover:text-primary-green-dark font-medium"
          >
            {buttonText}
            <ExternalLink className="w-3 h-3" />
          </a>
        </div>
      </div>
    )
  }

  if (variant === 'card') {
    return (
      <div className={cn('card text-center', className)}>
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-primary-green bg-opacity-10 rounded-xl flex items-center justify-center">
            <MessageSquare className="w-8 h-8 text-primary-green" />
          </div>
        </div>

        <h3 className="text-xl font-bold text-gray-900 mb-3">{title}</h3>

        {showDescription && <p className="text-gray-600 mb-6">{description}</p>}

        {showQR && (
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-gray-50 rounded-lg">
              <Image
                src={eAduanInfo.qrImage}
                alt={
                  language === 'en'
                    ? 'QR Code for E-Aduan'
                    : 'Kod QR untuk E-Aduan'
                }
                width={120}
                height={120}
                className="w-30 h-30"
              />
              <p className="text-xs text-gray-500 mt-2 flex items-center justify-center gap-1">
                <QrCode className="w-3 h-3" />
                {language === 'en' ? 'Scan QR Code' : 'Imbas Kod QR'}
              </p>
            </div>
          </div>
        )}

        <a
          href={eAduanInfo.url}
          target="_blank"
          rel="noopener noreferrer"
          className="btn-primary inline-flex items-center gap-2"
        >
          <Monitor className="w-4 h-4" />
          {buttonText}
          <ExternalLink className="w-4 h-4" />
        </a>
      </div>
    )
  }

  return (
    <div
      className={cn(
        'bg-gradient-to-br from-primary-green to-islamic-green text-white rounded-xl p-6 md:p-8',
        className
      )}
    >
      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="flex-1 text-center md:text-left">
          <div className="flex items-center justify-center md:justify-start gap-3 mb-4">
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold">{title}</h3>
          </div>

          {showDescription && (
            <p className="text-white text-opacity-90 mb-6 text-lg">
              {description}
            </p>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
            <a
              href={eAduanInfo.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-white text-primary-green font-semibold rounded-lg hover:bg-gray-100 transition-colors"
            >
              <Monitor className="w-5 h-5" />
              {buttonText}
              <ExternalLink className="w-4 h-4" />
            </a>
          </div>
        </div>

        {showQR && (
          <div className="flex-shrink-0">
            <div className="p-4 bg-white bg-opacity-10 rounded-xl backdrop-blur-sm">
              <Image
                src={eAduanInfo.qrImage}
                alt={
                  language === 'en'
                    ? 'QR Code for E-Aduan'
                    : 'Kod QR untuk E-Aduan'
                }
                width={120}
                height={120}
                className="w-30 h-30 rounded-lg"
              />
              <p className="text-white text-opacity-80 text-sm mt-3 text-center flex items-center justify-center gap-1">
                <QrCode className="w-4 h-4" />
                {language === 'en' ? 'Scan to complain' : 'Imbas untuk aduan'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// System access component for MYeHALAL
export function SystemAccess({ className }: { className?: string }) {
  const { language } = useLanguage()

  const systems = [
    {
      id: 'domestic',
      title: language === 'en' ? 'MYeHALAL Domestic' : 'MYeHALAL Domestik',
      description:
        language === 'en'
          ? 'Application system for domestic organizations'
          : 'Sistem permohonan untuk organisasi domestik',
      url: 'https://myehalal.halal.gov.my/domestik/v1/',
      icon: '/images/icons/monitor.png',
    },
    {
      id: 'international',
      title:
        language === 'en' ? 'MYeHALAL International' : 'MYeHALAL Antarabangsa',
      description:
        language === 'en'
          ? 'Application system for international organizations'
          : 'Sistem permohonan untuk organisasi antarabangsa',
      url: 'https://myehalal.halal.gov.my/international/v1/pemohon/',
      icon: '/images/icons/monitor.png',
    },
  ]

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-6', className)}>
      {systems.map(system => (
        <div
          key={system.id}
          className="card group hover:shadow-lg transition-all duration-200"
        >
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 w-12 h-12 p-2 bg-primary-green bg-opacity-10 rounded-lg group-hover:bg-opacity-20 transition-colors">
              <Image
                src={system.icon}
                alt=""
                width={32}
                height={32}
                className="w-full h-full object-contain"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 group-hover:text-primary-green transition-colors mb-2">
                {system.title}
              </h3>
              <p className="text-gray-600 text-sm mb-4">{system.description}</p>
              <a
                href={system.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-primary-green hover:text-primary-green-dark font-medium text-sm"
              >
                <Monitor className="w-4 h-4" />
                {language === 'en' ? 'Access System' : 'Akses Sistem'}
                <ExternalLink className="w-3 h-3" />
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
