'use client';

export const runtime = 'edge';

import { Plus } from 'lucide-react';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Link } from '@/i18n/navigation';
import { useCollectionsStore } from '@/stores/collections';
import { createColumns } from './columns';
import { CollectionsDataTable } from './data-table';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function CollectionsPage() {
  const { collections, isLoading, error, fetchCollections, clearError } = useCollectionsStore();

  // Auth guard
  useAdminAuthGuard();

  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  // Create columns with refresh callback
  const columns = createColumns(() => {
    fetchCollections(); // Refresh the collections list after deletion
  });

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={() => fetchCollections()}>Try Again</Button>
              <Button variant="outline" onClick={clearError}>
                Clear Error
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Document Collections</h1>
          <p className="text-gray-600">
            Manage document collections and organize your files
          </p>
        </div>
        <Link href="/admin/collections/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Collection
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Collections</CardTitle>
          <CardDescription>
            A list of all document collections in your system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <CollectionsDataTable columns={columns} data={collections} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
