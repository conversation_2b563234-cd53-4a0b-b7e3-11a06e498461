#!/usr/bin/env python3
"""
Example usage of QdrantManager for pushing files and querying collections
"""

import logging
from qdrant_manager import QdrantManager, push_output_files_to_qdrant, query_qdrant_collection

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Example usage of Qdrant functions"""
    
    # Configuration
    collection_name = "halal_documents"
    output_dir = "output"
    qdrant_url = "http://localhost:6333"
    
    print("=== Qdrant Manager Example ===\n")
    
    # Example 1: Push files to collection using convenience function
    print("1. Pushing files to Qdrant collection...")
    stats = push_output_files_to_qdrant(
        collection_name=collection_name,
        output_dir=output_dir,
        qdrant_url=qdrant_url
    )
    
    print(f"Push results: {stats}")
    print()
    
    # Example 2: Query the collection
    print("2. Querying the collection...")
    
    # Basic query
    results = query_qdrant_collection(
        collection_name=collection_name,
        query_text="halal certification requirements",
        limit=5,
        qdrant_url=qdrant_url
    )
    
    print(f"Found {len(results)} results for 'halal certification requirements':")
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result['file_name']} (score: {result['score']:.4f})")
        print(f"     Path: {result['file_path']}")
        print(f"     Content preview: {result['content'][:100]}...")
        print()
    
    # Example 3: Query with filters
    print("3. Querying with file type filter...")
    pdf_results = query_qdrant_collection(
        collection_name=collection_name,
        query_text="certification process",
        limit=3,
        file_type_filter=".pdf",
        qdrant_url=qdrant_url
    )
    
    print(f"Found {len(pdf_results)} PDF results for 'certification process':")
    for i, result in enumerate(pdf_results, 1):
        print(f"  {i}. {result['file_name']} (score: {result['score']:.4f})")
    print()
    
    # Example 4: Using QdrantManager class directly for more control
    print("4. Using QdrantManager class directly...")
    manager = QdrantManager(qdrant_url=qdrant_url)
    
    # Query with custom parameters
    advanced_results = manager.query_collection(
        collection_name=collection_name,
        query_text="food safety standards",
        limit=10,
        score_threshold=0.3,  # Only results with score > 0.3
        file_name_filter=None
    )
    
    print(f"Found {len(advanced_results)} results with score > 0.3 for 'food safety standards':")
    for i, result in enumerate(advanced_results, 1):
        print(f"  {i}. {result['file_name']} (score: {result['score']:.4f}, type: {result['file_type']})")
    print()
    
    print("=== Example completed ===")

if __name__ == "__main__":
    main()
