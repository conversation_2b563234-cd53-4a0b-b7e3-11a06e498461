.PHONY: deploy
include .env

close:
	lsof -ti:16000 | xargs kill -9 || true
	lsof -ti:16001 | xargs kill -9 || true
	lsof -ti:16005 | xargs kill -9 || true

live-push:
	rsync -avzP --exclude-from '.dockerignore' ./*.* $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/
	rsync -avzP --exclude-from '.dockerignore' ./admin $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/
	rsync -avzP --exclude-from '.dockerignore' ./front $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/
	rsync -avzP --exclude-from '.dockerignore' ./selangor $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/
	rsync -avzP --exclude-from '.dockerignore' ./server $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/
	# rsync -avzP ./server/.env.production $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH)/server/.env

live-deploy: live-push
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose up -d --build"
	make live-db-migrate
	echo "Done!"
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker system prune -f"
	make live-logs

live-restart:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH)/halal && docker compose restart"

live-logs:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose logs -f"
live-ssh:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && bash -l"

live-db-migrate: live-push
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose run --rm migration"
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose run --rm migration bun run db:seed"

live-product-vector: live-push
	# ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose run --rm -it server sh"
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose build server"
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose run --rm -it server bun run vectorize --force"

live-psql:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) -t  "cd $(REMOTE_PATH) && docker compose exec postgres psql -U halal_user -d halal_chat"

