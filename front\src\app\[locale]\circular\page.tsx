'use client'

export const runtime = 'edge'

import { Calendar, Download, FileText } from 'lucide-react'
import type { BreadcrumbItem } from '@/components/breadcrumb'
import { PageWrapper } from '@/components/page-wrapper'
import { useLanguage } from '@/lib/language-context'

export default function CircularPage() {
  const { language } = useLanguage()

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Circular' : 'Pekeliling',
      href: '/circular',
    },
  ]

  const circulars = [
    {
      id: 'PKL-001-2024',
      title: 'Guidelines for Halal Certification of Food Products',
      titleBM: 'Garis Panduan Pensijilan Halal Produk Makanan',
      date: '2024-01-15',
      category: 'Food Products',
      categoryBM: 'Produk Makanan',
      description:
        'Updated guidelines for food product certification procedures',
      descriptionBM:
        'Garis panduan terkini untuk prosedur pensijilan produk makanan',
      pdfUrl: '/documents/circulars/PKL-001-2024.pdf',
      featured: true,
    },
    {
      id: 'PKL-002-2024',
      title: 'Cosmetics and Personal Care Products Certification',
      titleBM: 'Pensijilan Produk Kosmetik dan Penjagaan Diri',
      date: '2024-02-20',
      category: 'Cosmetics',
      categoryBM: 'Kosmetik',
      description: 'New requirements for cosmetics and personal care products',
      descriptionBM: 'Keperluan baru untuk produk kosmetik dan penjagaan diri',
      pdfUrl: '/documents/circulars/PKL-002-2024.pdf',
      featured: true,
    },
    {
      id: 'PKL-003-2024',
      title: 'Pharmaceutical Products Halal Certification',
      titleBM: 'Pensijilan Halal Produk Farmaseutikal',
      date: '2024-03-10',
      category: 'Pharmaceuticals',
      categoryBM: 'Farmaseutikal',
      description: 'Guidelines for pharmaceutical product certification',
      descriptionBM: 'Garis panduan untuk pensijilan produk farmaseutikal',
      pdfUrl: '/documents/circulars/PKL-003-2024.pdf',
      featured: false,
    },
    {
      id: 'PKL-004-2023',
      title: 'Food Service Establishment Certification',
      titleBM: 'Pensijilan Pertubuhan Perkhidmatan Makanan',
      date: '2023-12-05',
      category: 'Food Service',
      categoryBM: 'Perkhidmatan Makanan',
      description: 'Requirements for food service establishment certification',
      descriptionBM:
        'Keperluan untuk pensijilan pertubuhan perkhidmatan makanan',
      pdfUrl: '/documents/circulars/PKL-004-2023.pdf',
      featured: false,
    },
    {
      id: 'PKL-005-2023',
      title: 'International Halal Certification Recognition',
      titleBM: 'Pengiktirafan Pensijilan Halal Antarabangsa',
      date: '2023-11-18',
      category: 'International',
      categoryBM: 'Antarabangsa',
      description:
        'Guidelines for international Halal certification recognition',
      descriptionBM:
        'Garis panduan untuk pengiktirafan pensijilan Halal antarabangsa',
      pdfUrl: '/documents/circulars/PKL-005-2023.pdf',
      featured: false,
    },
  ]

  const categories = [
    { id: 'all', label: 'All Categories', labelBM: 'Semua Kategori' },
    { id: 'food', label: 'Food Products', labelBM: 'Produk Makanan' },
    { id: 'cosmetics', label: 'Cosmetics', labelBM: 'Kosmetik' },
    {
      id: 'pharmaceuticals',
      label: 'Pharmaceuticals',
      labelBM: 'Farmaseutikal',
    },
    { id: 'service', label: 'Food Service', labelBM: 'Perkhidmatan Makanan' },
    { id: 'international', label: 'International', labelBM: 'Antarabangsa' },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(language === 'en' ? 'en-US' : 'ms-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <PageWrapper
      title="Circular"
      titleBM="Pekeliling"
      description="Official circulars and documents related to Halal certification procedures, guidelines, and updates."
      descriptionBM="Pekeliling dan dokumen rasmi berkaitan prosedur pensijilan Halal, garis panduan, dan kemas kini."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Official Circulars' : 'Pekeliling Rasmi'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'This section contains official circulars issued by JAKIM regarding Halal certification procedures, guidelines, policy updates, and important announcements. These documents provide detailed information for industry stakeholders and certification applicants.'
              : 'Bahagian ini mengandungi pekeliling rasmi yang dikeluarkan oleh JAKIM mengenai prosedur pensijilan Halal, garis panduan, kemas kini dasar, dan pengumuman penting. Dokumen-dokumen ini menyediakan maklumat terperinci untuk pihak berkepentingan industri dan pemohon pensijilan.'}
          </p>
        </div>

        {/* Filter Categories */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">
            {language === 'en'
              ? 'Filter by Category'
              : 'Tapis mengikut Kategori'}
          </h3>
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <button
                key={category.id}
                className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-colors"
              >
                {language === 'bm' ? category.labelBM : category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Featured Circulars */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Latest Circulars' : 'Pekeliling Terkini'}
          </h3>
          <div className="space-y-4">
            {circulars
              .filter(circular => circular.featured)
              .map(circular => (
                <div
                  key={circular.id}
                  className="p-6 border border-gray-200 rounded-lg hover:border-primary-green hover:shadow-md transition-all"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-green text-white">
                          {circular.id}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {language === 'bm'
                            ? circular.categoryBM
                            : circular.category}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        {language === 'bm' ? circular.titleBM : circular.title}
                      </h4>
                      <p className="text-gray-600 text-sm mb-3">
                        {language === 'bm'
                          ? circular.descriptionBM
                          : circular.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(circular.date)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <FileText className="w-4 h-4" />
                          <span>PDF</span>
                        </div>
                      </div>
                    </div>
                    <div className="ml-4">
                      <a
                        href={circular.pdfUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 px-4 py-2 bg-primary-green text-white text-sm rounded-lg hover:bg-primary-green-dark transition-colors"
                      >
                        <Download className="w-4 h-4" />
                        {language === 'en' ? 'Download' : 'Muat Turun'}
                      </a>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* All Circulars */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'All Circulars' : 'Semua Pekeliling'}
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Circular ID' : 'ID Pekeliling'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Title' : 'Tajuk'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Category' : 'Kategori'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Date' : 'Tarikh'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Action' : 'Tindakan'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {circulars.map(circular => (
                  <tr
                    key={circular.id}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="py-3 px-4">
                      <span className="font-mono text-sm text-primary-green">
                        {circular.id}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <h5 className="font-medium text-gray-900 text-sm">
                          {language === 'bm'
                            ? circular.titleBM
                            : circular.title}
                        </h5>
                        <p className="text-xs text-gray-500 mt-1">
                          {language === 'bm'
                            ? circular.descriptionBM
                            : circular.description}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {language === 'bm'
                          ? circular.categoryBM
                          : circular.category}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">
                      {formatDate(circular.date)}
                    </td>
                    <td className="py-3 px-4">
                      <a
                        href={circular.pdfUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-primary-green hover:text-primary-green-dark text-sm"
                      >
                        <Download className="w-4 h-4" />
                        {language === 'en' ? 'Download' : 'Muat Turun'}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Subscription Notice */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Stay Updated' : 'Kekal Terkini'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'Subscribe to our notifications to receive updates on new circulars and important announcements related to Halal certification.'
              : 'Langgan pemberitahuan kami untuk menerima kemas kini mengenai pekeliling baru dan pengumuman penting berkaitan pensijilan Halal.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Email:' : 'E-mel:'}
              </span>{' '}
              <EMAIL>
            </div>
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Phone:' : 'Telefon:'}
              </span>{' '}
              03-8892 5000
            </div>
          </div>
        </div>

        {/* Important Notice */}
        <div className="card border-l-4 border-yellow-400 bg-yellow-50">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg
                className="w-5 h-5 text-yellow-600 mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-yellow-800 mb-2">
                {language === 'en' ? 'Important Notice' : 'Notis Penting'}
              </h4>
              <p className="text-yellow-700 text-sm">
                {language === 'en'
                  ? 'All circulars are official documents. Please ensure you are referring to the latest version. For any clarification, contact JAKIM directly.'
                  : 'Semua pekeliling adalah dokumen rasmi. Sila pastikan anda merujuk kepada versi terkini. Untuk sebarang penjelasan, hubungi JAKIM secara langsung.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
