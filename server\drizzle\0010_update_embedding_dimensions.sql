-- Update embedding column to support 3072 dimensions for text-embedding-3-large model
-- This migration will:
-- 1. Drop existing embedding column if it exists
-- 2. Recreate it with 3072 dimensions
-- 3. Clear vectorized_at timestamps so products will be re-vectorized

-- Clear vectorization timestamps so products will be re-vectorized with new dimensions
UPDATE products SET vectorized_at = NULL, vectorization_model = NULL WHERE vectorized_at IS NOT NULL;

-- Drop the existing embedding column
ALTER TABLE products DROP COLUMN IF EXISTS embedding;

-- Add new embedding column with 3072 dimensions
ALTER TABLE products ADD COLUMN embedding vector(3072);
