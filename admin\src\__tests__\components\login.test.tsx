import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import LoginPage from '@/app/login/page'
import { useAuthStore } from '@/stores/auth'

// Mock the auth store
jest.mock('@/stores/auth')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<
  typeof useAuthStore
>

// Mock next/navigation
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('LoginPage', () => {
  const mockLogin = jest.fn()
  const mockClearError = jest.fn()

  beforeEach(() => {
    mockLogin.mockClear()
    mockClearError.mockClear()
    mockPush.mockClear()

    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      user: null,
      token: null,
      logout: jest.fn(),
      setLoading: jest.fn(),
      refreshUser: jest.fn(),
    })
  })

  it('renders login form', () => {
    render(<LoginPage />)

    expect(screen.getByText('Halal Admin')).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: 'Sign In' })).toBeInTheDocument()
    expect(screen.getByLabelText('Username')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
  })

  it('handles form submission with valid credentials', async () => {
    const user = userEvent.setup()
    mockLogin.mockResolvedValue(true)

    render(<LoginPage />)

    await user.type(screen.getByLabelText('Username'), 'superadmin')
    await user.type(screen.getByLabelText('Password'), 'password')
    await user.click(screen.getByRole('button', { name: 'Sign In' }))

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: 'superadmin',
        password: 'password',
      })
    })

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  it('displays error message on login failure', () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      isLoading: false,
      error: 'Invalid credentials',
      clearError: mockClearError,
      user: null,
      token: null,
      logout: jest.fn(),
      setLoading: jest.fn(),
      refreshUser: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
  })

  it('disables form during loading', () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      isLoading: true,
      error: null,
      clearError: mockClearError,
      user: null,
      token: null,
      logout: jest.fn(),
      setLoading: jest.fn(),
      refreshUser: jest.fn(),
    })

    render(<LoginPage />)

    expect(screen.getByLabelText('Username')).toBeDisabled()
    expect(screen.getByLabelText('Password')).toBeDisabled()
    expect(screen.getByRole('button', { name: 'Signing in...' })).toBeDisabled()
  })

  it('redirects authenticated users to dashboard', () => {
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isAuthenticated: true,
      isLoading: false,
      error: null,
      clearError: mockClearError,
      user: { id: 1, username: 'superadmin', roles: ['SUPERADMIN'] } as any,
      token: 'token',
      logout: jest.fn(),
      setLoading: jest.fn(),
      refreshUser: jest.fn(),
    })

    render(<LoginPage />)

    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  it('clears error when user starts typing', async () => {
    const user = userEvent.setup()
    mockUseAuthStore.mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      isLoading: false,
      error: 'Some error',
      clearError: mockClearError,
      user: null,
      token: null,
      logout: jest.fn(),
      setLoading: jest.fn(),
      refreshUser: jest.fn(),
    })

    render(<LoginPage />)

    await user.type(screen.getByLabelText('Username'), 'a')

    expect(mockClearError).toHaveBeenCalled()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<LoginPage />)

    const submitButton = screen.getByRole('button', { name: 'Sign In' })
    expect(submitButton).toBeDisabled()

    await user.type(screen.getByLabelText('Username'), 'test')
    expect(submitButton).toBeDisabled()

    await user.type(screen.getByLabelText('Password'), 'password')
    expect(submitButton).not.toBeDisabled()
  })
})
