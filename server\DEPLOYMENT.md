# Deployment Guide

This guide explains how to deploy the Halal Chat Server to Cloudflare Workers with automated database migrations and proper schema management.

## Overview

The deployment process includes:

1. **Pre-deployment**: Check database status and run migrations
2. **Deployment**: Deploy the application to Cloudflare Workers
3. **Post-deployment**: Verify deployment and database state

## Database Management Strategy

### 🎯 **Recommended Approach: Migration-First**

1. **Initial Setup** (First time): Use migrations + production seed
2. **Schema Changes**: Always use migrations (never direct SQL)
3. **Data Changes**: Use migration scripts for structural data changes
4. **Rollbacks**: Keep migration rollback scripts ready

## Prerequisites

### 1. Production Environment Setup

Ensure you have a `.env.production` file with your production database credentials:

```bash
# Copy from example and fill in production values
cp .env.example .env.production
```

Required variables in `.env.production`:

- `DATABASE_URL` - Production PostgreSQL connection string
- `OPENAI_API_KEY` - Production OpenAI API key
- `JWT_SECRET` - Production JWT secret
- `FRONTEND_URL` - Production frontend URL
- Other production-specific variables

### 2. Cloudflare Workers Secrets

Set up sensitive environment variables as Cloudflare Workers secrets:

```bash
# Set production secrets (these override .env.production for the deployed worker)
npx wrangler secret put DATABASE_URL --env production
npx wrangler secret put OPENAI_API_KEY --env production
npx wrangler secret put JWT_SECRET --env production

# Or use the automated script
./scripts/setup-secrets.sh
```

## Database Deployment Workflows

### 🚀 **First Time Production Setup**

For a **brand new production database**:

```bash
cd server

# 1. Check database status
pnpm db:status:prod

# 2. Initialize database (migrations + essential data)
pnpm db:init:prod

# 3. Deploy application
cd .. && pnpm deploy:server
```

### 🔄 **Regular Deployments** (Schema Changes)

For **ongoing deployments with schema changes**:

```bash
cd server

# 1. Generate new migration (after schema changes)
pnpm drizzle:generate

# 2. Review generated migration file
cat drizzle/XXXX_*.sql

# 3. Deploy (auto-runs migrations)
cd .. && pnpm deploy:server
```

### 📊 **Database Management Commands**

```bash
# Check database and migration status
pnpm db:status:prod

# Backup current schema
pnpm db:backup:prod

# Health check (returns exit code)
pnpm db:check:prod

# Manual migration (if needed)
pnpm db:migrate:prod

# Production seed (safe - won't overwrite)
pnpm db:seed:prod
```

## Deployment Commands

### Quick Deployment (Recommended)

Deploy both server and frontend with automated migrations:

```bash
# From project root
pnpm deploy
```

This runs:

1. `pnpm deploy:server` - Migrates database + deploys server
2. `pnpm deploy:front` - Deploys frontend

### Server Only Deployment

Deploy just the server with database migration:

```bash
# From project root
pnpm deploy:server

# Or from server directory
cd server
pnpm db:migrate:prod && wrangler deploy -e production
```

### Manual Migration (if needed)

Run database migrations manually against production:

```bash
cd server

# Run migrations using production environment
pnpm db:migrate:prod

# Or push schema changes (for development/prototyping)
pnpm db:push:prod
```

## Environment Configuration

### Local Development vs Production

| Environment           | Database              | Config File       | Wrangler Env |
| --------------------- | --------------------- | ----------------- | ------------ |
| Local Dev             | Local PostgreSQL      | `.env`            | N/A          |
| Production Migration  | Production PostgreSQL | `.env.production` | N/A          |
| Production Deployment | Production PostgreSQL | Wrangler Secrets  | `production` |

### Key Points

1. **Migration Environment**: Uses `.env.production` to connect to production database locally
2. **Runtime Environment**: Uses Cloudflare Workers secrets for the deployed application
3. **Security**: Sensitive data is stored in Wrangler secrets, not in `wrangler.toml`

## Troubleshooting

### Migration Fails

```bash
# Check database connectivity
cd server
dotenv -e .env.production -- npx drizzle-kit studio

# Verify migration files
ls -la drizzle/

# Run migration with verbose output
dotenv -e .env.production -- npx drizzle-kit migrate --verbose
```

### Deployment Fails

```bash
# Check Wrangler configuration
npx wrangler whoami
npx wrangler secret list --env production

# Deploy with verbose output
wrangler deploy -e production --verbose
```

### Database Connection Issues

1. Verify `DATABASE_URL` in `.env.production`
2. Check database server accessibility
3. Ensure SSL mode is configured correctly for your database provider

## Best Practices

1. **Always test migrations** in a staging environment first
2. **Backup production database** before running migrations
3. **Monitor deployment** logs after deployment
4. **Verify functionality** with a quick smoke test
5. **Keep secrets secure** - never commit `.env.production` to git

## Rollback Strategy

If deployment fails:

1. **Revert application**: Deploy previous working version
2. **Rollback database**: Use database backup or reverse migration
3. **Check logs**: Review Cloudflare Workers logs for errors

```bash
# View production logs
pnpm server:logs

# Or directly
cd server && wrangler -e production tail -f
```
