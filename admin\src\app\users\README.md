# User Management

This directory contains the complete CRUD (Create, Read, Update, Delete) functionality for user management in the admin dashboard.

## Features

- **List Users**: View all users with search and filtering capabilities
- **Create User**: Add new users with role assignment and profile information
- **Edit User**: Update existing user details, roles, and status
- **Delete User**: Remove users from the system with confirmation

## File Structure

```
users/
├── page.tsx                    # Main users list page
├── new/
│   └── page.tsx               # Create new user page
├── [id]/
│   └── edit/
│       └── page.tsx           # Edit user page
└── README.md                  # This file
```

## Components Used

- **UserForm**: Reusable form component for creating and editing users
- **RoleSelector**: Component for selecting user roles with descriptions
- **UserStatusBadge**: Badge components for displaying user status and roles

## Store Integration

The pages use the `useUsersStore` Zustand store which provides:

- `fetchUsers()`: Load all users
- `createUser(data)`: Create a new user
- `updateUser(id, data)`: Update an existing user
- `deleteUser(id)`: Delete a user
- `fetchUserById(id)`: Get a specific user

## User Roles

The system supports multiple roles per user:

- **SUPERADMIN**: Full system access and site management
- **ADMIN**: Administrative access to site features
- **EDITOR**: Content editing and management
- **AGENT**: Customer support and chat handling
- **SUPERVISOR**: Agent supervision and management

## API Endpoints

The user management interfaces with these backend endpoints:

- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create a new user
- `GET /api/admin/users/:id` - Get user by ID
- `PUT /api/admin/users/:id` - Update user
- `DELETE /api/admin/users/:id` - Delete user

## Usage

1. **Viewing Users**: Navigate to `/users` to see all users
2. **Creating Users**: Click "Add User" button or navigate to `/users/new`
3. **Editing Users**: Click "Edit" button on any user or navigate to `/users/:id/edit`
4. **Deleting Users**: Click "Delete" button and confirm the action

## Form Validation

- Username is required for all operations
- Password is required for user creation
- At least one role must be selected
- Email validation when provided
- Active status can be toggled

## Security

- All operations require admin authentication
- Users cannot delete themselves
- Role changes are validated on the backend
- Passwords are hashed before storage
