import { type NextRequest, NextResponse } from 'next/server'
export const runtime = 'edge'
export const dynamic = 'force-dynamic'

// Mock database - replace with real database in production
const mockCertificates = [
  {
    id: '1',
    certificateNumber: 'JAKIM-HM-001234',
    companyName: 'ABC Food Industries Sdn Bhd',
    companyNameBM: 'ABC Food Industries Sdn Bhd',
    productName: 'Halal Chicken Nuggets',
    productNameBM: 'Nugget Ayam Halal',
    category: 'Food Manufacturing',
    categoryBM: 'Pembuatan Makanan',
    issueDate: '2023-01-15',
    expiryDate: '2025-01-14',
    status: 'valid' as const,
    certificationBody: 'JAKIM',
    country: 'Malaysia',
    countryBM: 'Malaysia',
  },
  {
    id: '2',
    certificateNumber: 'JAKIM-HP-005678',
    companyName: 'XYZ Beverages Sdn Bhd',
    companyNameBM: 'XYZ Beverages Sdn Bhd',
    productName: 'Halal Energy Drink',
    productNameBM: 'Minuman Tenaga Halal',
    category: 'Beverage Manufacturing',
    categoryBM: 'Pembuatan Minuman',
    issueDate: '2023-06-01',
    expiryDate: '2025-05-31',
    status: 'valid' as const,
    certificationBody: 'JAKIM',
    country: 'Malaysia',
    countryBM: 'Malaysia',
  },
  {
    id: '3',
    certificateNumber: 'JAKIM-HM-009876',
    companyName: 'DEF Cosmetics Sdn Bhd',
    companyNameBM: 'DEF Cosmetics Sdn Bhd',
    productName: 'Halal Skincare Products',
    productNameBM: 'Produk Penjagaan Kulit Halal',
    category: 'Cosmetics Manufacturing',
    categoryBM: 'Pembuatan Kosmetik',
    issueDate: '2022-12-01',
    expiryDate: '2024-11-30',
    status: 'expired' as const,
    certificationBody: 'JAKIM',
    country: 'Malaysia',
    countryBM: 'Malaysia',
  },
  {
    id: '4',
    certificateNumber: 'JAKIM-HL-012345',
    companyName: 'GHI Logistics Sdn Bhd',
    companyNameBM: 'GHI Logistics Sdn Bhd',
    productName: 'Halal Transportation Services',
    productNameBM: 'Perkhidmatan Pengangkutan Halal',
    category: 'Logistics Services',
    categoryBM: 'Perkhidmatan Logistik',
    issueDate: '2023-03-15',
    expiryDate: '2025-03-14',
    status: 'suspended' as const,
    certificationBody: 'JAKIM',
    country: 'Malaysia',
    countryBM: 'Malaysia',
  },
  {
    id: '5',
    certificateNumber: 'FHCB-ID-001122',
    companyName: 'Indonesian Halal Foods Ltd',
    companyNameBM: 'Indonesian Halal Foods Ltd',
    productName: 'Halal Instant Noodles',
    productNameBM: 'Mi Segera Halal',
    category: 'Food Manufacturing',
    categoryBM: 'Pembuatan Makanan',
    issueDate: '2023-02-01',
    expiryDate: '2025-01-31',
    status: 'valid' as const,
    certificationBody: 'LPPOM MUI',
    country: 'Indonesia',
    countryBM: 'Indonesia',
  },
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')?.toLowerCase() || ''
    const status = searchParams.get('status')
    const category = searchParams.get('category')
    const country = searchParams.get('country')
    const page = Number.parseInt(searchParams.get('page') || '1')
    const limit = Number.parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'companyName'
    const sortOrder = searchParams.get('sortOrder') || 'asc'

    // Filter results based on query
    const filteredResults = mockCertificates.filter(cert => {
      const matchesQuery =
        !query ||
        cert.companyName.toLowerCase().includes(query) ||
        cert.companyNameBM.toLowerCase().includes(query) ||
        cert.productName.toLowerCase().includes(query) ||
        cert.productNameBM.toLowerCase().includes(query) ||
        cert.certificateNumber.toLowerCase().includes(query)

      const matchesStatus = !status || cert.status === status
      const matchesCategory =
        !category ||
        cert.category.toLowerCase().includes(category.toLowerCase()) ||
        cert.categoryBM.toLowerCase().includes(category.toLowerCase())
      const matchesCountry =
        !country ||
        cert.country.toLowerCase().includes(country.toLowerCase()) ||
        cert.countryBM.toLowerCase().includes(country.toLowerCase())

      return matchesQuery && matchesStatus && matchesCategory && matchesCountry
    })

    // Sort results
    filteredResults.sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a] || ''
      const bValue = b[sortBy as keyof typeof b] || ''

      if (sortOrder === 'desc') {
        return bValue.toString().localeCompare(aValue.toString())
      }
      return aValue.toString().localeCompare(bValue.toString())
    })

    // Paginate results
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedResults = filteredResults.slice(startIndex, endIndex)

    // Transform results for API response
    const searchResults = paginatedResults.map(cert => ({
      id: cert.id,
      type: 'certificate' as const,
      title: cert.companyName,
      subtitle: cert.productName,
      status: cert.status,
      certificateNumber: cert.certificateNumber,
      expiryDate: cert.expiryDate,
      category: cert.category,
      certificationBody: cert.certificationBody,
      country: cert.country,
    }))

    return NextResponse.json({
      success: true,
      data: searchResults,
      pagination: {
        page,
        limit,
        total: filteredResults.length,
        totalPages: Math.ceil(filteredResults.length / limit),
      },
    })
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to perform search',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, filters = {} } = body

    // Enhanced search with filters
    const filteredResults = mockCertificates.filter(cert => {
      const matchesQuery =
        !query ||
        cert.companyName.toLowerCase().includes(query.toLowerCase()) ||
        cert.productName.toLowerCase().includes(query.toLowerCase()) ||
        cert.certificateNumber.toLowerCase().includes(query.toLowerCase())

      // Apply additional filters
      const matchesFilters = Object.entries(filters).every(([key, value]) => {
        if (!value) {
          return true
        }

        switch (key) {
          case 'status':
            return cert.status === value
          case 'category':
            return cert.category
              .toLowerCase()
              .includes((value as string).toLowerCase())
          case 'country':
            return cert.country
              .toLowerCase()
              .includes((value as string).toLowerCase())
          case 'certificationBody':
            return cert.certificationBody
              .toLowerCase()
              .includes((value as string).toLowerCase())
          case 'dateRange': {
            const { start, end } = value as { start: string; end: string }
            const issueDate = new Date(cert.issueDate)
            return (
              (!start || issueDate >= new Date(start)) &&
              (!end || issueDate <= new Date(end))
            )
          }
          default:
            return true
        }
      })

      return matchesQuery && matchesFilters
    })

    // Group results by type for better organization
    const groupedResults = {
      companies: filteredResults.map(cert => ({
        id: cert.id,
        type: 'company' as const,
        title: cert.companyName,
        subtitle: `${cert.category} - ${cert.country}`,
        status: cert.status,
        certificateNumber: cert.certificateNumber,
        expiryDate: cert.expiryDate,
      })),
      products: filteredResults.map(cert => ({
        id: cert.id,
        type: 'product' as const,
        title: cert.productName,
        subtitle: cert.companyName,
        status: cert.status,
        certificateNumber: cert.certificateNumber,
        expiryDate: cert.expiryDate,
      })),
      certificates: filteredResults.map(cert => ({
        id: cert.id,
        type: 'certificate' as const,
        title: `Certificate ${cert.certificateNumber}`,
        subtitle: `${cert.companyName} - ${cert.productName}`,
        status: cert.status,
        certificateNumber: cert.certificateNumber,
        expiryDate: cert.expiryDate,
      })),
    }

    return NextResponse.json({
      success: true,
      data: groupedResults,
      total: filteredResults.length,
    })
  } catch (error) {
    console.error('Search POST API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to perform advanced search',
      },
      { status: 500 }
    )
  }
}
