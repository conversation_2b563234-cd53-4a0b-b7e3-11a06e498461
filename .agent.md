# Halal Malaysia AI Chat System - Developer Guide

## Project Overview

A comprehensive AI-powered chat system for Halal Malaysia with multi-platform integration including web chat, WhatsApp Business API, and Facebook Messenger. The system includes both a public portal (clone of the official JAKIM Halal portal) and an AI chat system with admin/agent management capabilities.

### Key Technologies

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS 4, React 19
- **Backend**: Cloudflare Workers (with local Express.js fallback)
- **Database**: PostgreSQL with Drizzle ORM
- **AI Integration**: OpenAI with image analysis and voice transcription
- **Authentication**: JWT-based with multi-role support
- **Deployment**: Cloudflare Pages (frontend) + Cloudflare Workers (backend)

## Project Structure

```
halal/
├── front/                    # Next.js frontend application
│   ├── src/
│   │   ├── app/             # App router pages (Next.js 15)
│   │   ├── components/      # Reusable React components
│   │   ├── lib/            # Utilities and configurations
│   │   ├── hooks/          # Custom React hooks
│   │   └── types/          # TypeScript type definitions
├── server/                  # Cloudflare Workers backend
│   ├── src/
│   │   ├── services/       # Business logic services
│   │   ├── routes/         # API route handlers (legacy Express-style)
│   │   ├── middleware/     # Authentication and validation
│   │   ├── lib/           # Database and utility functions
│   │   └── types/         # TypeScript interfaces
│   └── drizzle/            # Database schema and migrations
├── crawler/               # Web crawling and indexing tools
└── docs/                  # Additional documentation
```

## Development Setup

### Prerequisites

- Node.js 18+ or Bun
- PostgreSQL database
- OpenAI API key
- Cloudflare account (for deployment)

### Quick Start

```bash
# Install dependencies
bun install

# Set up environment variables
cp front/.env.example front/.env
cp server/.env.example server/.env

# Set up database
cd server
bun run db:migrate
bun run db:seed

# Start development servers
cd ..
bun dev  # Starts both frontend (port 16000) and backend (port 8787)
```

### Environment Configuration

#### Frontend (.env)

```bash
NEXT_PUBLIC_API_BASE_URL=http://localhost:16001
NEXT_PUBLIC_ADMIN_ENABLED=true
```

#### Server (.env)

```bash
# OpenAI
OPENAI_API_KEY=your_openai_key

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/halal_chat

# JWT
JWT_SECRET=your_secret_key
JWT_EXPIRES_IN=365d

# Admin
ADMIN_DEFAULT_PASSWORD=admin123

# CORS
FRONTEND_URL=http://localhost:16000
```

## Architecture Overview

### Frontend Architecture

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4 with custom design system
- **State Management**: React Context + localStorage
- **API Communication**: Axios with direct server calls
- **Testing**: Jest + React Testing Library

### Backend Architecture

- **Runtime**: Cloudflare Workers (production) / Node.js (development)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with role-based access control
- **API Design**: RESTful endpoints with CORS support
- **File Handling**: S3-compatible storage integration

### Database Schema

- **Unified User System**: Single `users` table with multi-role support
- **Chat Management**: Sessions, messages, handover requests
- **Platform Integration**: WhatsApp and Facebook message logging
- **Content Management**: Collections, documents, S3 configurations

## Key Features

### AI Chat System

- Multi-language support (Bahasa Malaysia, English, Chinese, Tamil)
- Image analysis with gpt-4.1 Vision
- Voice message transcription
- Context-aware conversations
- Agent handover capabilities

### Multi-Platform Integration

- Web chat interface with floating widget
- WhatsApp Business API integration
- Facebook Messenger support
- Admin dashboard for configuration
- Real-time message handling

### Multi-Role User System

- **ADMIN**: Full system access, user management
- **EDITOR**: Content management, limited admin access
- **AGENT**: Chat support, session handling
- **SUPERVISOR**: Agent oversight, advanced features
- **Multi-role support**: Users can have multiple roles simultaneously

### Portal Features

- Halal certification information
- News and announcements
- Document management
- QR code scanning
- Bilingual support (EN/BM)

## Development Guidelines

### Code Organization

- Use TypeScript for all new code
- Follow Next.js App Router conventions
- Implement proper error boundaries
- Use drizzle for all database operations
- Maintain separation between frontend and backend

### Component Structure

```typescript
// Component naming: PascalCase
export function ComponentName({ prop }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState();

  // Event handlers
  const handleEvent = () => {};

  // Render
  return <div>...</div>;
}
```

### API Route Patterns

```typescript
// Server route handler
export async function GET(request: Request, env: any): Promise<Response> {
  try {
    // Authentication check
    // Business logic
    // Return response
  } catch (error) {
    // Error handling
  }
}
```

### Database Operations

```typescript
// Use database service
const dbService = new DatabaseService(env);
const users = await dbService.getAllUsers();
```

## Testing Strategy

### Frontend Testing

```bash
# Unit tests
bun test:unit

# Integration tests
bun test:integration

# E2E tests
bun test:e2e

# All tests
bun test:all
```

### Backend Testing

```bash
cd server
bun test
```

### Test Structure

- **Unit Tests**: Individual components and functions
- **Integration Tests**: API endpoints and database operations
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Load testing and optimization

## Deployment

### Frontend (Cloudflare Pages)

```bash
cd front
bun pages:build
bun deploy
```

### Backend (Cloudflare Workers)

```bash
cd server
bun dev  # Development
wrangler deploy  # Production
```

### Database Migrations

```bash
cd server
bun drizzle-kit migrate
```

## Common Development Tasks

### Adding New API Endpoints

1. Create route handler in `server/src/index.ts`
2. Add authentication if needed
3. Implement business logic in services
4. Add TypeScript types
5. Test the endpoint

### Adding New Frontend Pages

1. Create page in `front/src/app/`
2. Add to navigation if needed
3. Implement responsive design
4. Add error handling
5. Write tests

### Database Schema Changes

1. Modify `server/src/db/schema.ts`
2. Run `pnpm drizzle:generate` to create migration
3. Run `pnpm db:push` (development) or `pnpm db:migrate` (production)
4. Update TypeScript types
5. Update seed data if needed

### Adding New User Roles

1. Add role to `UserRole` enum in schema
2. Update authentication middleware
3. Add role-based UI components
4. Update permission checks
5. Test role combinations

## Troubleshooting

### Common Issues

#### Database Connection Issues

- Check DATABASE_URL in environment
- Ensure PostgreSQL is running
- Run `pnpm db:push` to sync schema

#### CORS Issues

- Verify FRONTEND_URL in server environment
- Check API base URL in frontend
- Ensure proper headers in requests

#### Authentication Issues

- Check JWT_SECRET configuration
- Verify token storage in localStorage
- Ensure proper role assignments

#### Build Issues

- Clear Next.js cache: `rm -rf .next`
- Regenerate Drizzle types: `pnpm drizzle:generate`
- Check TypeScript errors: `bun type-check`

### Development vs Production

#### Development Mode

- Frontend: `http://localhost:16000`
- Backend: `http://localhost:8787` (Cloudflare Workers) or `http://localhost:16001` (local Express)
- Database: Local PostgreSQL
- Hot reloading enabled

#### Production Mode

- Frontend: Cloudflare Pages
- Backend: Cloudflare Workers
- Database: Production PostgreSQL
- Optimized builds

## Security Considerations

### Authentication

- JWT tokens with expiration
- Role-based access control
- Secure password hashing with bcrypt
- Environment variable protection

### API Security

- CORS configuration
- Rate limiting (planned)
- Input validation
- SQL injection prevention (Drizzle ORM)

### Data Protection

- Sensitive data in environment variables
- Database connection encryption
- File upload restrictions
- XSS prevention

## Performance Optimization

### Frontend

- Next.js App Router for optimal loading
- Image optimization with Next.js
- Lazy loading for components
- Bundle analysis available

### Backend

- Cloudflare Workers for global distribution
- Database connection pooling
- Efficient Drizzle ORM queries
- Response caching strategies

### Database

- Proper indexing on frequently queried fields
- Connection pooling
- Query optimization
- Regular maintenance

## Monitoring and Logging

### Frontend

- Error boundaries for crash prevention
- Console logging for development
- Performance monitoring planned
- admin url prefix: http://localhost:16000/[locale]/admin
- agent url prefix: http://localhost:16000/[locale]/agent

### Backend

- Structured logging with console
- Error tracking and reporting
- API response time monitoring
- Database query performance

## Contributing Guidelines

### Code Style

- Use TypeScript for type safety
- Follow ESLint configuration
- Use Prettier for formatting
- Write meaningful commit messages

### Pull Request Process

1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Submit PR with clear description
5. Address review feedback

### Testing Requirements

- Unit tests for new functions
- Integration tests for API changes
- E2E tests for user workflows
- Performance tests for critical paths

## Resources

### Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [Drizzle ORM Documentation](https://orm.drizzle.team/docs/overview)
- [Cloudflare Workers](https://developers.cloudflare.com/workers)
- [OpenAI API](https://platform.openai.com/docs)

### Project-Specific Docs

- `CHAT_SETUP.md` - Detailed setup instructions
- `MULTI_ROLE_USER_SYSTEM.md` - User management system
- `WHATSAPP_SETUP.md` - WhatsApp integration
- `front/assets/PROJECT_COMPLETION_SUMMARY.md` - Project status

### Development Tools

- Drizzle Studio: `pnpm db:studio`
- Database migrations: `pnpm db:migrate`
- Type checking: `bun type-check`
- Bundle analysis: `bun analyze`

## Support and Maintenance

### Regular Tasks

- Database backups
- Dependency updates
- Security patches
- Performance monitoring
- Log rotation

### Emergency Procedures

- Database recovery
- Service rollback
- Error investigation
- Performance debugging
- Security incident response

---

_This guide is maintained by the development team. Please update it when making significant changes to the project structure or development workflow._
