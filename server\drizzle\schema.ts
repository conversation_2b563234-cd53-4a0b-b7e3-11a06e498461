import { sql } from 'drizzle-orm'
import {
  boolean,
  foreignKey,
  integer,
  pgEnum,
  pgTable,
  serial,
  text,
  timestamp,
  unique,
  varchar,
  vector,
} from 'drizzle-orm/pg-core'

export const collectionStatus = pgEnum('collection_status', [
  'ACTIVE',
  'DISABLED',
])
export const userRole = pgEnum('user_role', [
  'ADMIN',
  'EDITOR',
  'AGENT',
  'SUPERVISOR',
])

export const facebookConfig = pgTable('facebook_config', {
  id: serial().primaryKey().notNull(),
  pageAccessToken: varchar('page_access_token', { length: 255 }).notNull(),
  pageId: varchar('page_id', { length: 255 }).notNull(),
  appSecret: varchar('app_secret', { length: 255 }).notNull(),
  verifyToken: varchar('verify_token', { length: 255 }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  siteId: integer('site_id').notNull(),
})

export const whatsappConfig = pgTable('whatsapp_config', {
  id: serial().primaryKey().notNull(),
  accessToken: varchar('access_token', { length: 255 }).notNull(),
  phoneNumberId: varchar('phone_number_id', { length: 255 }).notNull(),
  webhookVerifyToken: varchar('webhook_verify_token', {
    length: 255,
  }).notNull(),
  businessAccountId: varchar('business_account_id', { length: 255 }),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  siteId: integer('site_id').notNull(),
})

export const products = pgTable('products', {
  id: serial().primaryKey().notNull(),
  siteId: integer('site_id').notNull(),
  productName: varchar('product_name', { length: 500 }).notNull(),
  companyName: varchar('company_name', { length: 500 }).notNull(),
  certificateNumber: varchar('certificate_number', { length: 255 }),
  certificateType: varchar('certificate_type', { length: 255 }),
  issuedDate: varchar('issued_date', { length: 255 }),
  expiryDate: varchar('expiry_date', { length: 255 }),
  status: varchar({ length: 100 }),
  category: varchar({ length: 255 }),
  subcategory: varchar({ length: 255 }),
  address: text(),
  state: varchar({ length: 255 }),
  country: varchar({ length: 255 }),
  contactInfo: text('contact_info'),
  website: varchar({ length: 500 }),
  sourceUrl: varchar('source_url', { length: 1000 }),
  rawData: text('raw_data'),
  embedding: vector('embedding', { dimensions: 3072 }),
  vectorizedAt: timestamp('vectorized_at', { mode: 'string' }),
  vectorizationModel: varchar('vectorization_model', { length: 100 }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
})

export const twilioConfigs = pgTable(
  'twilio_configs',
  {
    id: serial().primaryKey().notNull(),
    siteId: integer('site_id').notNull(),
    accountSid: varchar('account_sid', { length: 255 }).notNull(),
    authToken: varchar('auth_token', { length: 255 }).notNull(),
    phoneNumber: varchar('phone_number', { length: 50 }).notNull(),
    webhookUrl: varchar('webhook_url', { length: 255 }),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sites.id],
      name: 'twilio_configs_site_id_sites_id_fk',
    }).onDelete('cascade'),
  ],
)

export const twilioMessages = pgTable(
  'twilio_messages',
  {
    id: varchar({ length: 255 }).primaryKey().notNull(),
    siteId: integer('site_id').notNull(),
    accountSid: varchar('account_sid', { length: 255 }),
    from: varchar({ length: 50 }).notNull(),
    to: varchar({ length: 50 }).notNull(),
    body: text(),
    type: varchar({ length: 50 }).default('text').notNull(),
    mediaUrl: varchar('media_url', { length: 255 }),
    mediaContentType: varchar('media_content_type', { length: 100 }),
    status: varchar({ length: 50 }),
    direction: varchar({ length: 10 }).notNull(),
    errorCode: integer('error_code'),
    errorMessage: text('error_message'),
    timestamp: timestamp({ mode: 'string' }).notNull(),
    sessionId: varchar('session_id', { length: 255 }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.siteId],
      foreignColumns: [sites.id],
      name: 'twilio_messages_site_id_sites_id_fk',
    }).onDelete('cascade'),
  ],
)

export const agentMessages = pgTable('agent_messages', {
  id: serial().primaryKey().notNull(),
  messageId: varchar('message_id', { length: 255 }).notNull(),
  agentId: integer('agent_id').notNull(),
  agentName: varchar('agent_name', { length: 255 }).notNull(),
  sentAt: timestamp('sent_at', { mode: 'string' }).defaultNow().notNull(),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
})

export const companies = pgTable(
  'companies',
  {
    id: serial().primaryKey().notNull(),
    siteId: integer('site_id').notNull(),
    companyName: varchar('company_name', { length: 500 }).notNull(),
    registrationNumber: varchar('registration_number', { length: 255 }),
    businessType: varchar('business_type', { length: 255 }),
    category: varchar({ length: 255 }),
    subcategory: varchar({ length: 255 }),
    address: text(),
    state: varchar({ length: 255 }),
    postcode: varchar({ length: 20 }),
    city: varchar({ length: 255 }),
    country: varchar({ length: 255 }).default('Malaysia'),
    phone: varchar({ length: 50 }),
    fax: varchar({ length: 50 }),
    email: varchar({ length: 255 }),
    website: varchar({ length: 500 }),
    contactPerson: varchar('contact_person', { length: 255 }),
    certificateNumber: varchar('certificate_number', { length: 255 }),
    certificateType: varchar('certificate_type', { length: 255 }),
    certificateStatus: varchar('certificate_status', { length: 100 }),
    issuedDate: varchar('issued_date', { length: 255 }),
    expiryDate: varchar('expiry_date', { length: 255 }),
    sourceUrl: varchar('source_url', { length: 1000 }),
    pageNumber: integer('page_number'),
    rawData: text('raw_data'),
    dataHash: varchar('data_hash', { length: 64 }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => [unique('companies_data_hash_unique').on(table.dataHash)],
)

export const chatMessages = pgTable('chat_messages', {
  id: varchar({ length: 255 }).primaryKey().notNull(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  role: varchar({ length: 50 }).notNull(),
  content: text().notNull(),
  imageUrl: varchar('image_url', { length: 255 }),
  audioUrl: varchar('audio_url', { length: 255 }),
  fileUrl: varchar('file_url', { length: 255 }),
  fileName: varchar('file_name', { length: 255 }),
  timestamp: timestamp({ mode: 'string' }).defaultNow().notNull(),
  agentId: integer('agent_id'),
  siteId: integer('site_id').notNull(),
})

export const documents = pgTable(
  'documents',
  {
    id: serial().primaryKey().notNull(),
    collectionId: integer('collection_id').notNull(),
    s3ConfigurationId: integer('s3_configuration_id').notNull(),
    s3Key: varchar('s3_key', { length: 255 }).notNull(),
    filename: varchar({ length: 255 }).notNull(),
    filesize: integer(),
    mimetype: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => [unique('documents_s3_key_unique').on(table.s3Key)],
)

export const collections = pgTable(
  'collections',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    status: collectionStatus().default('ACTIVE').notNull(),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    siteId: integer('site_id').notNull(),
  },
  (table) => [unique('collections_name_unique').on(table.name)],
)

export const handoverRequests = pgTable('handover_requests', {
  id: serial().primaryKey().notNull(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  requestedBy: varchar('requested_by', { length: 50 }).notNull(),
  reason: text(),
  priority: varchar({ length: 50 }).default('normal').notNull(),
  status: varchar({ length: 50 }).default('pending').notNull(),
  assignedTo: integer('assigned_to'),
  requestedAt: timestamp('requested_at', { mode: 'string' })
    .defaultNow()
    .notNull(),
  assignedAt: timestamp('assigned_at', { mode: 'string' }),
  completedAt: timestamp('completed_at', { mode: 'string' }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
})

export const s3Configurations = pgTable('s3_configurations', {
  id: serial().primaryKey().notNull(),
  serviceName: varchar('service_name', { length: 255 }).notNull(),
  accessKeyId: varchar('access_key_id', { length: 255 }).notNull(),
  secretAccessKey: varchar('secret_access_key', { length: 255 }).notNull(),
  bucketName: varchar('bucket_name', { length: 255 }).notNull(),
  region: varchar({ length: 255 }),
  endpointUrl: varchar('endpoint_url', { length: 255 }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
})

export const sessionAssignments = pgTable('session_assignments', {
  id: serial().primaryKey().notNull(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  agentId: integer('agent_id').notNull(),
  assignedAt: timestamp('assigned_at', { mode: 'string' })
    .defaultNow()
    .notNull(),
  status: varchar({ length: 50 }).default('active').notNull(),
  completedAt: timestamp('completed_at', { mode: 'string' }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
})

export const facebookMessages = pgTable('facebook_messages', {
  id: varchar({ length: 255 }).primaryKey().notNull(),
  from: varchar({ length: 255 }).notNull(),
  to: varchar({ length: 255 }).notNull(),
  type: varchar({ length: 50 }).notNull(),
  content: text().notNull(),
  mediaUrl: varchar('media_url', { length: 255 }),
  timestamp: timestamp({ mode: 'string' }).notNull(),
  sessionId: varchar('session_id', { length: 255 }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  siteId: integer('site_id').notNull(),
})

export const users = pgTable(
  'users',
  {
    id: serial().primaryKey().notNull(),
    username: varchar({ length: 255 }).notNull(),
    email: varchar({ length: 255 }),
    passwordHash: varchar('password_hash', { length: 255 }).notNull(),
    firstName: varchar('first_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }),
    roles: text().array().default(['']).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isOnline: boolean('is_online').default(false).notNull(),
    lastSeenAt: timestamp('last_seen_at', { mode: 'string' }),
    lastLoginAt: timestamp('last_login_at', { mode: 'string' }),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    siteId: integer('site_id').notNull(),
  },
  (table) => [
    unique('users_username_unique').on(table.username),
    unique('users_email_unique').on(table.email),
  ],
)

export const whatsappMessages = pgTable('whatsapp_messages', {
  id: varchar({ length: 255 }).primaryKey().notNull(),
  from: varchar({ length: 255 }).notNull(),
  to: varchar({ length: 255 }).notNull(),
  type: varchar({ length: 50 }).notNull(),
  content: text().notNull(),
  mediaUrl: varchar('media_url', { length: 255 }),
  timestamp: timestamp({ mode: 'string' }).notNull(),
  sessionId: varchar('session_id', { length: 255 }),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  siteId: integer('site_id').notNull(),
})

export const sites = pgTable(
  'sites',
  {
    id: serial().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    code: varchar({ length: 50 }).notNull(),
    domains: text().array().default(['']).notNull(),
    status: boolean().default(true).notNull(),
    createdAt: timestamp('created_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string' })
      .defaultNow()
      .notNull(),
  },
  (table) => [unique('sites_code_unique').on(table.code)],
)

export const chatSessions = pgTable('chat_sessions', {
  id: varchar({ length: 255 }).primaryKey().notNull(),
  userId: varchar('user_id', { length: 255 }),
  platform: varchar({ length: 50 }).default('web').notNull(),
  platformId: varchar('platform_id', { length: 255 }),
  status: varchar({ length: 50 }).default('active').notNull(),
  isHandedOver: boolean('is_handed_over').default(false).notNull(),
  createdAt: timestamp('created_at', { mode: 'string' }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { mode: 'string' }).defaultNow().notNull(),
  lastMessageAt: timestamp('last_message_at', { mode: 'string' }),
  siteId: integer('site_id').notNull(),
})
