import { relations, sql } from 'drizzle-orm'
import {
  boolean,
  integer,
  pgEnum,
  pgTable,
  real,
  serial,
  text,
  timestamp,
  unique,
  varchar,
  vector,
} from 'drizzle-orm/pg-core'

// Enums
export const userRoleEnum = pgEnum('user_role', [
  'ADMIN',
  'EDITOR',
  'AGENT',
  'SUPERVISOR',
])
export const collectionStatusEnum = pgEnum('collection_status', [
  'ACTIVE',
  'DISABLED',
])

// Sites table
export const sites = pgTable('sites', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  domains: text('domains')
    .array()
    .notNull()
    .default(sql`'{}'::text[]`), // Array of domains
  status: boolean('status').notNull().default(true), // 1 = active, 0 = inactive
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Users table (unified from admin_users and agent_users)
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  username: varchar('username', { length: 255 }).unique().notNull(),
  email: varchar('email', { length: 255 }).unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  firstName: varchar('first_name', { length: 255 }),
  lastName: varchar('last_name', { length: 255 }),
  roles: text('roles')
    .array()
    .notNull()
    .default(sql`'{}'::text[]`), // Array of roles for multi-role support
  isActive: boolean('is_active').notNull().default(true),
  isOnline: boolean('is_online').notNull().default(false),
  lastSeenAt: timestamp('last_seen_at'),
  lastLoginAt: timestamp('last_login_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Collections table
export const collections = pgTable('collections', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  name: varchar('name', { length: 255 }).unique().notNull(),
  status: collectionStatusEnum('status').notNull().default('ACTIVE'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// S3 Configuration table
export const s3Configurations = pgTable('s3_configurations', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id')
    .notNull()
    .references(() => sites.id, { onDelete: 'cascade' }),
  serviceName: varchar('service_name', { length: 255 }).notNull(),
  accessKeyId: varchar('access_key_id', { length: 255 }).notNull(),
  secretAccessKey: varchar('secret_access_key', { length: 255 }).notNull(),
  bucketName: varchar('bucket_name', { length: 255 }).notNull(),
  region: varchar('region', { length: 255 }),
  endpointUrl: varchar('endpoint_url', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Service Types Enum
export const serviceTypeEnum = pgEnum('service_type', [
  'R2R_RAG',
  'SMTP_PROVIDER',
  'EXTERNAL_API',
])

// Services Configuration table
export const services = pgTable('services', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id')
    .notNull()
    .references(() => sites.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 255 }).notNull(),
  type: serviceTypeEnum('type').notNull(),
  description: text('description'),
  isActive: boolean('is_active').notNull().default(true),
  configuration: text('configuration').notNull(), // JSON string for flexible config storage
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Documents table
export const documents = pgTable('documents', {
  id: serial('id').primaryKey(),
  collectionId: integer('collection_id').notNull(),
  s3ConfigurationId: integer('s3_configuration_id').notNull(),
  s3Key: varchar('s3_key', { length: 255 }).unique().notNull(),
  filename: varchar('filename', { length: 255 }).notNull(),
  filesize: integer('filesize'),
  mimetype: varchar('mimetype', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// WhatsApp Configuration table
export const whatsappConfigs = pgTable('whatsapp_config', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  accessToken: varchar('access_token', { length: 255 }).notNull(),
  phoneNumberId: varchar('phone_number_id', { length: 255 }).notNull(),
  webhookVerifyToken: varchar('webhook_verify_token', {
    length: 255,
  }).notNull(),
  businessAccountId: varchar('business_account_id', { length: 255 }),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Twilio Configuration table
export const twilioConfigs = pgTable('twilio_configs', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id')
    .notNull()
    .references(() => sites.id, { onDelete: 'cascade' }),
  accountSid: varchar('account_sid', { length: 255 }).notNull(),
  authToken: varchar('auth_token', { length: 255 }).notNull(), // Consider encrypting this
  phoneNumber: varchar('phone_number', { length: 50 }).notNull(), // Twilio phone number (e.g., whatsapp:+*********** or just the E.164 number)
  webhookUrl: varchar('webhook_url', { length: 255 }), // Optional: if you want to store the configured webhook URL
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// WhatsApp Messages table
export const whatsappMessages = pgTable('whatsapp_messages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  siteId: integer('site_id').notNull(),
  from: varchar('from', { length: 255 }).notNull(),
  to: varchar('to', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(),
  content: text('content').notNull(),
  mediaUrl: varchar('media_url', { length: 255 }),
  timestamp: timestamp('timestamp').notNull(),
  sessionId: varchar('session_id', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Twilio Messages table
export const twilioMessages = pgTable('twilio_messages', {
  id: varchar('id', { length: 255 }).primaryKey(), // Twilio Message SID
  siteId: integer('site_id')
    .notNull()
    .references(() => sites.id, { onDelete: 'cascade' }),
  accountSid: varchar('account_sid', { length: 255 }), // Can be useful for records
  from: varchar('from', { length: 50 }).notNull(), // User's number
  to: varchar('to', { length: 50 }).notNull(), // Your Twilio number
  body: text('body'), // Message content
  type: varchar('type', { length: 50 }).notNull().default('text'), // text, image, audio, video, document etc.
  mediaUrl: varchar('media_url', { length: 255 }),
  mediaContentType: varchar('media_content_type', { length: 100 }),
  status: varchar('status', { length: 50 }), // e.g., sent, delivered, read, failed
  direction: varchar('direction', { length: 10 }).notNull(), // 'inbound' or 'outbound'
  errorCode: integer('error_code'),
  errorMessage: text('error_message'),
  timestamp: timestamp('timestamp').notNull(), // When the message event occurred
  sessionId: varchar('session_id', { length: 255 }), // Link to chat_sessions.id
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Facebook Configuration table
export const facebookConfigs = pgTable('facebook_config', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  pageAccessToken: varchar('page_access_token', { length: 255 }).notNull(),
  pageId: varchar('page_id', { length: 255 }).notNull(),
  appSecret: varchar('app_secret', { length: 255 }).notNull(),
  verifyToken: varchar('verify_token', { length: 255 }).notNull(),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Facebook Messages table
export const facebookMessages = pgTable('facebook_messages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  siteId: integer('site_id').notNull(),
  from: varchar('from', { length: 255 }).notNull(),
  to: varchar('to', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(),
  content: text('content').notNull(),
  mediaUrl: varchar('media_url', { length: 255 }),
  timestamp: timestamp('timestamp').notNull(),
  sessionId: varchar('session_id', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Chat Sessions table
export const chatSessions = pgTable('chat_sessions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  siteId: integer('site_id').notNull(),
  userId: varchar('user_id', { length: 255 }),
  platform: varchar('platform', { length: 50 }).notNull().default('web'),
  platformId: varchar('platform_id', { length: 255 }),
  status: varchar('status', { length: 50 }).notNull().default('active'),
  isHandedOver: boolean('is_handed_over').notNull().default(false),
  botId: integer('bot_id'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  lastMessageAt: timestamp('last_message_at'),
})

// Chat Messages table
export const chatMessages = pgTable('chat_messages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  siteId: integer('site_id').notNull(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  content: text('content').notNull(),
  imageUrl: varchar('image_url', { length: 255 }),
  audioUrl: varchar('audio_url', { length: 255 }),
  fileUrl: varchar('file_url', { length: 255 }),
  fileName: varchar('file_name', { length: 255 }),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  agentId: integer('agent_id'),
})

// Session Assignments table
export const sessionAssignments = pgTable('session_assignments', {
  id: serial('id').primaryKey(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  agentId: integer('agent_id').notNull(),
  assignedAt: timestamp('assigned_at').notNull().defaultNow(),
  status: varchar('status', { length: 50 }).notNull().default('active'),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Handover Requests table
export const handoverRequests = pgTable('handover_requests', {
  id: serial('id').primaryKey(),
  sessionId: varchar('session_id', { length: 255 }).notNull(),
  requestedBy: varchar('requested_by', { length: 50 }).notNull(),
  reason: text('reason'),
  priority: varchar('priority', { length: 50 }).notNull().default('normal'),
  status: varchar('status', { length: 50 }).notNull().default('pending'),
  assignedTo: integer('assigned_to'),
  requestedAt: timestamp('requested_at').notNull().defaultNow(),
  assignedAt: timestamp('assigned_at'),
  completedAt: timestamp('completed_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Agent Messages table
export const agentMessages = pgTable('agent_messages', {
  id: serial('id').primaryKey(),
  messageId: varchar('message_id', { length: 255 }).notNull(),
  agentId: integer('agent_id').notNull(),
  agentName: varchar('agent_name', { length: 255 }).notNull(),
  sentAt: timestamp('sent_at').notNull().defaultNow(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Products table for halal product directory
export const products = pgTable('products', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  productName: varchar('product_name', { length: 500 }).notNull(),
  companyName: varchar('company_name', { length: 500 }).notNull(),
  certificateNumber: varchar('certificate_number', { length: 255 }),
  certificateType: varchar('certificate_type', { length: 255 }),
  issuedDate: varchar('issued_date', { length: 255 }),
  expiryDate: varchar('expiry_date', { length: 255 }),
  status: varchar('status', { length: 100 }),
  category: varchar('category', { length: 255 }),
  subcategory: varchar('subcategory', { length: 255 }),
  address: text('address'),
  state: varchar('state', { length: 255 }),
  country: varchar('country', { length: 255 }),
  contactInfo: text('contact_info'),
  website: varchar('website', { length: 500 }),
  sourceUrl: varchar('source_url', { length: 1000 }),
  rawData: text('raw_data'), // Store original scraped data as JSON
  r2rDocumentId: varchar('r2r_document_id', { length: 255 }),
  embedding: vector('embedding', { dimensions: 3072 }),
  vectorizedAt: timestamp('vectorized_at'),
  vectorizationModel: varchar('vectorization_model', { length: 100 }),
  searchableText: text('searchable_text'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Companies table for halal companies directory
export const companies = pgTable('companies', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  companyName: varchar('company_name', { length: 500 }).notNull(),
  registrationNumber: varchar('registration_number', { length: 255 }),
  businessType: varchar('business_type', { length: 255 }),
  category: varchar('category', { length: 255 }),
  subcategory: varchar('subcategory', { length: 255 }),
  address: text('address'),
  state: varchar('state', { length: 255 }),
  postcode: varchar('postcode', { length: 20 }),
  city: varchar('city', { length: 255 }),
  country: varchar('country', { length: 255 }).default('Malaysia'),
  phone: varchar('phone', { length: 50 }),
  fax: varchar('fax', { length: 50 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 500 }),
  contactPerson: varchar('contact_person', { length: 255 }),
  certificateNumber: varchar('certificate_number', { length: 255 }),
  certificateType: varchar('certificate_type', { length: 255 }),
  certificateStatus: varchar('certificate_status', { length: 100 }),
  issuedDate: varchar('issued_date', { length: 255 }),
  expiryDate: varchar('expiry_date', { length: 255 }),
  sourceUrl: varchar('source_url', { length: 1000 }),
  pageNumber: integer('page_number'),
  rawData: text('raw_data'),
  dataHash: varchar('data_hash', { length: 64 }).unique(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Search Analytics table for tracking user search activities
export const searchAnalytics = pgTable('search_analytics', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  searchQuery: text('search_query').notNull(),
  searchType: varchar('search_type', { length: 50 }).notNull(), // 'web', 'products', 'companies', 'semantic'
  resultsCount: integer('results_count').notNull().default(0),
  userAgent: text('user_agent'),
  ipAddress: varchar('ip_address', { length: 45 }), // IPv6 compatible
  sessionId: varchar('session_id', { length: 255 }),
  userId: integer('user_id'), // Optional - for logged in users
  responseTime: integer('response_time'), // Response time in milliseconds
  hasResults: boolean('has_results').notNull().default(false),
  searchFilters: text('search_filters'), // JSON string of applied filters
  createdAt: timestamp('created_at').notNull().defaultNow(),
})

// Contacts table for admin contact management
export const contacts = pgTable('contacts', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  title: varchar('title', { length: 255 }),
  department: varchar('department', { length: 255 }),
  type: varchar('type', { length: 50 }).notNull().default('general'), // 'general', 'support', 'sales', 'technical', 'emergency'
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 50 }),
  mobile: varchar('mobile', { length: 50 }),
  fax: varchar('fax', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 255 }),
  state: varchar('state', { length: 255 }),
  postcode: varchar('postcode', { length: 20 }),
  country: varchar('country', { length: 255 }).default('Malaysia'),
  website: varchar('website', { length: 500 }),
  workingHours: text('working_hours'),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Categories table for product and company categorization
export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  categoryName: varchar('category_name', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Product-Category junction table (many-to-many relationship)
export const productCategories = pgTable(
  'product_categories',
  {
    id: serial('id').primaryKey(),
    productId: integer('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    categoryId: integer('category_id')
      .notNull()
      .references(() => categories.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  (table) => ({
    // Ensure unique product-category combinations
    uniqueProductCategory: unique('unique_product_category').on(
      table.productId,
      table.categoryId,
    ),
  }),
)

// Company-Category junction table (many-to-many relationship)
export const companyCategories = pgTable(
  'company_categories',
  {
    id: serial('id').primaryKey(),
    companyId: integer('company_id')
      .notNull()
      .references(() => companies.id, { onDelete: 'cascade' }),
    categoryId: integer('category_id')
      .notNull()
      .references(() => categories.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
  },
  (table) => ({
    // Ensure unique company-category combinations
    uniqueCompanyCategory: unique('unique_company_category').on(
      table.companyId,
      table.categoryId,
    ),
  }),
)

// Relations
export const sitesRelations = relations(sites, ({ many }) => ({
  users: many(users),
  collections: many(collections),
  chatSessions: many(chatSessions),
  chatMessages: many(chatMessages),
  whatsappConfigs: many(whatsappConfigs),
  whatsappMessages: many(whatsappMessages),
  facebookConfigs: many(facebookConfigs),
  facebookMessages: many(facebookMessages),
  twilioConfigs: many(twilioConfigs),
  twilioMessages: many(twilioMessages),
  services: many(services),
  products: many(products),
  companies: many(companies),
  searchAnalytics: many(searchAnalytics),
  s3Configurations: many(s3Configurations),
  contacts: many(contacts),
}))

export const usersRelations = relations(users, ({ many, one }) => ({
  site: one(sites, {
    fields: [users.siteId],
    references: [sites.id],
  }),
  sessionAssignments: many(sessionAssignments),
  agentMessages: many(agentMessages),
  sentMessages: many(chatMessages),
  handoverRequests: many(handoverRequests),
  searchAnalytics: many(searchAnalytics),
}))

export const collectionsRelations = relations(collections, ({ many, one }) => ({
  site: one(sites, {
    fields: [collections.siteId],
    references: [sites.id],
  }),
  documents: many(documents),
}))

export const s3ConfigurationsRelations = relations(
  s3Configurations,
  ({ many, one }) => ({
    documents: many(documents),
    site: one(sites, {
      fields: [s3Configurations.siteId],
      references: [sites.id],
    }),
  }),
)

export const documentsRelations = relations(documents, ({ one }) => ({
  collection: one(collections, {
    fields: [documents.collectionId],
    references: [collections.id],
  }),
  s3Configuration: one(s3Configurations, {
    fields: [documents.s3ConfigurationId],
    references: [s3Configurations.id],
  }),
}))

export const chatSessionsRelations = relations(
  chatSessions,
  ({ many, one }) => ({
    site: one(sites, {
      fields: [chatSessions.siteId],
      references: [sites.id],
    }),
    messages: many(chatMessages),
    sessionAssignment: one(sessionAssignments),
    handoverRequests: many(handoverRequests),
  }),
)

export const chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  site: one(sites, {
    fields: [chatMessages.siteId],
    references: [sites.id],
  }),
  session: one(chatSessions, {
    fields: [chatMessages.sessionId],
    references: [chatSessions.id],
  }),
  agent: one(users, {
    fields: [chatMessages.agentId],
    references: [users.id],
  }),
  agentMetadata: one(agentMessages),
}))

export const sessionAssignmentsRelations = relations(
  sessionAssignments,
  ({ one }) => ({
    session: one(chatSessions, {
      fields: [sessionAssignments.sessionId],
      references: [chatSessions.id],
    }),
    agent: one(users, {
      fields: [sessionAssignments.agentId],
      references: [users.id],
    }),
  }),
)

export const handoverRequestsRelations = relations(
  handoverRequests,
  ({ one }) => ({
    session: one(chatSessions, {
      fields: [handoverRequests.sessionId],
      references: [chatSessions.id],
    }),
    agent: one(users, {
      fields: [handoverRequests.assignedTo],
      references: [users.id],
    }),
  }),
)

export const agentMessagesRelations = relations(agentMessages, ({ one }) => ({
  agent: one(users, {
    fields: [agentMessages.agentId],
    references: [users.id],
  }),
  message: one(chatMessages, {
    fields: [agentMessages.messageId],
    references: [chatMessages.id],
  }),
}))

export const whatsappConfigsRelations = relations(
  whatsappConfigs,
  ({ one }) => ({
    site: one(sites, {
      fields: [whatsappConfigs.siteId],
      references: [sites.id],
    }),
  }),
)

export const productsRelations = relations(products, ({ one, many }) => ({
  site: one(sites, {
    fields: [products.siteId],
    references: [sites.id],
  }),
  productCategories: many(productCategories),
}))

export const facebookConfigsRelations = relations(
  facebookConfigs,
  ({ one }) => ({
    site: one(sites, {
      fields: [facebookConfigs.siteId],
      references: [sites.id],
    }),
  }),
)

export const twilioConfigsRelations = relations(twilioConfigs, ({ one }) => ({
  site: one(sites, {
    fields: [twilioConfigs.siteId],
    references: [sites.id],
  }),
}))

export const twilioMessagesRelations = relations(twilioMessages, ({ one }) => ({
  site: one(sites, {
    fields: [twilioMessages.siteId],
    references: [sites.id],
  }),
}))
export const companiesRelations = relations(companies, ({ one, many }) => ({
  site: one(sites, {
    fields: [companies.siteId],
    references: [sites.id],
  }),
  companyCategories: many(companyCategories),
}))

export const categoriesRelations = relations(categories, ({ many }) => ({
  productCategories: many(productCategories),
  companyCategories: many(companyCategories),
}))

export const productCategoriesRelations = relations(
  productCategories,
  ({ one }) => ({
    product: one(products, {
      fields: [productCategories.productId],
      references: [products.id],
    }),
    category: one(categories, {
      fields: [productCategories.categoryId],
      references: [categories.id],
    }),
  }),
)

export const companyCategoriesRelations = relations(
  companyCategories,
  ({ one }) => ({
    company: one(companies, {
      fields: [companyCategories.companyId],
      references: [companies.id],
    }),
    category: one(categories, {
      fields: [companyCategories.categoryId],
      references: [categories.id],
    }),
  }),
)

export const searchAnalyticsRelations = relations(
  searchAnalytics,
  ({ one }) => ({
    site: one(sites, {
      fields: [searchAnalytics.siteId],
      references: [sites.id],
    }),
    user: one(users, {
      fields: [searchAnalytics.userId],
      references: [users.id],
    }),
  }),
)

export const servicesRelations = relations(services, ({ one }) => ({
  site: one(sites, {
    fields: [services.siteId],
    references: [sites.id],
  }),
}))

// Bots table
export const bots = pgTable('bots', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id')
    .notNull()
    .references(() => sites.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 50 }).unique().notNull(),
  provider: varchar('provider', { length: 100 }).notNull(),
  model: varchar('model', { length: 100 }).notNull(),
  temperature: real('temperature').notNull().default(0.5),
  isDefault: boolean('is_default').notNull().default(false),
  systemPrompt: text('system_prompt'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
})

// Bot Usages table
export const botUsages = pgTable('bot_usages', {
  id: serial('id').primaryKey(),
  botId: integer('bot_id')
    .notNull()
    .references(() => bots.id, { onDelete: 'cascade' }),
  chatId: varchar('chat_id', { length: 255 }).notNull(),
  messageId: varchar('message_id', { length: 255 }).notNull(),
  inputTokens: integer('input_tokens').notNull(),
  outputTokens: integer('output_tokens').notNull(),
  audioTokens: integer('audio_tokens'),
  videoTokens: integer('video_tokens'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
})

// Relations for bots and bot_usages
export const botsRelations = relations(bots, ({ many, one }) => ({
  usages: many(botUsages),
  site: one(sites, {
    fields: [bots.siteId],
    references: [sites.id],
  }),
}))

export const botUsagesRelations = relations(botUsages, ({ one }) => ({
  bot: one(bots, {
    fields: [botUsages.botId],
    references: [bots.id],
  }),
}))

export const contactsRelations = relations(contacts, ({ one }) => ({
  site: one(sites, {
    fields: [contacts.siteId],
    references: [sites.id],
  }),
}))
