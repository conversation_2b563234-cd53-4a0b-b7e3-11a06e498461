#!/usr/bin/env ts-node

import { sql } from 'drizzle-orm'
import {
  initializeDatabase,
  initializeDatabaseLocal,
} from '../src/db/connection'

/**
 * Drop all tables and enums from the database
 * This is a destructive operation - use with caution!
 */
async function dropAllTablesAndEnums(useProduction = false) {
  console.log('🗑️  Starting database cleanup...')
  console.log('⚠️  WARNING: This will drop ALL tables and enums!')

  if (useProduction) {
    console.log('🌐 Using PRODUCTION database connection')
  } else {
    console.log('🏠 Using LOCAL database connection')
  }

  // Additional confirmation prompt
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout,
  })

  const confirmation = await new Promise((resolve) => {
    readline.question(
      '⚠️  Type "YES" to confirm this destructive operation: ',
      (answer) => {
        readline.close()
        resolve(answer.trim().toUpperCase() === 'YES')
      },
    )
  })

  if (!confirmation) {
    console.log('❌ Operation cancelled by user')
    process.exit(1)
  }

  const db = useProduction ? initializeDatabase() : initializeDatabaseLocal()

  try {
    // First, drop all tables (with CASCADE to handle foreign key constraints)
    console.log('📋 Getting list of all tables...')
    const tablesResult = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `)

    const tables = tablesResult.map((row: any) => row.table_name)
    console.log(`   Found ${tables.length} tables:`, tables.join(', '))

    if (tables.length > 0) {
      console.log('🗑️  Dropping all tables...')
      for (const tableName of tables) {
        console.log(`   Dropping table: ${tableName}`)
        await db.execute(
          sql.raw(`DROP TABLE IF EXISTS "${tableName}" CASCADE;`),
        )
      }
      console.log('✅ All tables dropped successfully')
    } else {
      console.log('ℹ️  No tables found to drop')
    }

    // Then, drop all enums
    console.log('📋 Getting list of all enums...')
    const enumsResult = await db.execute(sql`
      SELECT t.typname as enum_name
      FROM pg_type t 
      JOIN pg_enum e ON t.oid = e.enumtypid  
      WHERE t.typnamespace = (
        SELECT oid FROM pg_namespace WHERE nspname = 'public'
      )
      GROUP BY t.typname
      ORDER BY t.typname;
    `)

    const enums = enumsResult.map((row: any) => row.enum_name)
    console.log(`   Found ${enums.length} enums:`, enums.join(', '))

    if (enums.length > 0) {
      console.log('🗑️  Dropping all enums...')
      for (const enumName of enums) {
        console.log(`   Dropping enum: ${enumName}`)
        await db.execute(sql.raw(`DROP TYPE IF EXISTS "${enumName}" CASCADE;`))
      }
      console.log('✅ All enums dropped successfully')
    } else {
      console.log('ℹ️  No enums found to drop')
    }

    // Drop the Drizzle migrations table if it exists
    console.log('🗑️  Dropping Drizzle migrations table...')
    await db.execute(sql`DROP TABLE IF EXISTS "__drizzle_migrations" CASCADE;`)
    console.log('✅ Drizzle migrations table dropped')

    // Verify cleanup
    console.log('🔍 Verifying cleanup...')
    const remainingTablesResult = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE';
    `)

    const remainingEnumsResult = await db.execute(sql`
      SELECT t.typname as enum_name
      FROM pg_type t 
      JOIN pg_enum e ON t.oid = e.enumtypid  
      WHERE t.typnamespace = (
        SELECT oid FROM pg_namespace WHERE nspname = 'public'
      )
      GROUP BY t.typname;
    `)

    const remainingTables = remainingTablesResult.map(
      (row: any) => row.table_name,
    )
    const remainingEnums = remainingEnumsResult.map((row: any) => row.enum_name)

    if (remainingTables.length === 0 && remainingEnums.length === 0) {
      console.log('✅ Database cleanup completed successfully!')
      console.log('📊 Final status:')
      console.log('   - Tables: 0')
      console.log('   - Enums: 0')
      console.log('   - Database is now empty and ready for fresh migrations')
    } else {
      console.log('⚠️  Some objects may still remain:')
      if (remainingTables.length > 0) {
        console.log(`   - Remaining tables: ${remainingTables.join(', ')}`)
      }
      if (remainingEnums.length > 0) {
        console.log(`   - Remaining enums: ${remainingEnums.join(', ')}`)
      }
    }
  } catch (error) {
    console.error('❌ Error during database cleanup:', error)
    throw error
  }

  db.$client.end()
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)
  const force = args.includes('--force') || args.includes('-f')
  const useProduction =
    process.env.NODE_ENV === 'production' ||
    process.env.DATABASE_URL?.includes('prod') ||
    args.includes('--production')

  if (!force) {
    console.log(
      '⚠️  This operation will permanently delete ALL tables and enums!',
    )
    console.log('💡 To proceed, run with --force flag:')
    console.log('   npm run db:drop-all -- --force (for local)')
    console.log('   npm run db:drop-all:prod -- --force (for production)')
    console.log('   or')
    console.log('   npx ts-node scripts/drop-all-tables.ts --force')
    process.exit(1)
  }

  await dropAllTablesAndEnums(useProduction)
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error)
    process.exit(1)
  })
}
