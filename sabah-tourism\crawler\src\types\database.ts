export interface SocialPost {
  id?: number;
  platform: string;
  post_id: string;
  url: string;
  title?: string;
  content?: string;
  author_username?: string;
  author_display_name?: string;
  author_avatar_url?: string;
  posted_at?: Date;
  crawled_at?: Date;
  tags?: string; // JSON array of hashtags
  likes_count?: number;
  comments_count?: number;
  shares_count?: number;
  views_count?: number;
  metadata?: string; // JSON metadata specific to platform
}

export interface MediaFile {
  id?: number;
  post_id: number;
  media_type: "image" | "video" | "audio";
  file_name: string;
  file_path: string;
  original_url?: string;
  file_size?: number;
  duration?: number; // For video/audio (seconds)
  width?: number; // For images/videos
  height?: number; // For images/videos
  downloaded_at?: Date;
}

export interface CrawlSession {
  id?: number;
  platform: string;
  keywords: string; // JSON array of search keywords
  status: "running" | "completed" | "failed" | "paused";
  started_at?: Date;
  completed_at?: Date;
  total_posts?: number;
  successful_posts?: number;
  failed_posts?: number;
  error_message?: string;
  config?: string; // JSON crawl configuration
}

export interface CrawlAttempt {
  id?: number;
  session_id: number;
  post_url: string;
  keyword?: string;
  status: "pending" | "success" | "failed" | "skipped";
  error_message?: string;
  attempted_at?: Date;
  completed_at?: Date;
}

export interface SearchKeyword {
  id?: number;
  keyword: string;
  platform: string;
  last_crawled?: Date;
  total_posts_found?: number;
  created_at?: Date;
}

export interface DatabaseConfig {
  path: string;
  enableWAL?: boolean;
  timeout?: number;
  verbose?: boolean;
}

export interface DatabaseStats {
  totalPosts: number;
  totalMediaFiles: number;
  totalSessions: number;
  activeSessions: number;
  platformBreakdown: Record<string, number>;
  recentActivity: {
    postsToday: number;
    postsThisWeek: number;
    postsThisMonth: number;
  };
}
