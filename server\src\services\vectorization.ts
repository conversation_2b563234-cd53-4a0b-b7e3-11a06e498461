/**
 * Product Vectorization Service
 *
 * This service handles automatic vectorization of products when they are
 * inserted or updated in the database.
 */

import { sql } from 'drizzle-orm'
import { products } from '../db/schema'
import { DatabaseService } from './database'

export interface ProductVectorizationData {
  productName: string
  companyName: string
  category?: string | null
  subcategory?: string | null
  certificateType?: string | null
  status?: string | null
  address?: string | null
}

export interface EmbeddingResult {
  embedding: number[]
  model: string
  usage?: {
    prompt_tokens: number
    total_tokens: number
  }
}

export class ProductVectorizationService {
  private apiKey: string
  private defaultModel = 'text-embedding-ada-002'
  private dbService: DatabaseService

  constructor(apiKey?: string, dbService?: DatabaseService) {
    this.apiKey = apiKey || process.env.OPENAI_API_KEY || ''
    if (!this.apiKey) {
      throw new Error('OpenAI API key is required for vectorization service')
    }
    this.dbService = dbService || new DatabaseService()
  }

  /**
   * Create searchable text from product data
   */
  private createProductSearchableText(
    product: ProductVectorizationData,
  ): string {
    const parts: string[] = []

    // Add product name (most important)
    if (product.productName) {
      parts.push(product.productName)
    }

    // Add company name
    if (product.companyName) {
      parts.push(product.companyName)
    }

    // Add category information
    if (product.category) {
      parts.push(product.category)
    }
    if (product.subcategory) {
      parts.push(product.subcategory)
    }

    // Add status
    if (product.status) {
      parts.push(product.status)
    }

    // Add address (less important, so at the end)
    if (product.address) {
      parts.push(product.address)
    }

    return parts.join(' ').trim()
  }

  /**
   * Generate embedding for text using OpenAI API
   */
  private async generateEmbedding(
    text: string,
    model?: string,
  ): Promise<EmbeddingResult> {
    const modelToUse = model || this.defaultModel

    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty for embedding generation')
    }

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: text,
          model: modelToUse,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenAI API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ''
          }`,
        )
      }

      const data = await response.json()

      return {
        embedding: data.data[0].embedding,
        model: modelToUse,
        usage: data.usage,
      }
    } catch (error) {
      console.error('Error generating embedding:', error)
      throw error
    }
  }

  /**
   * Vectorize a product and update it in the database
   */
  async vectorizeProduct(
    productId: number,
    productData: ProductVectorizationData,
  ): Promise<boolean> {
    try {
      // Create searchable text from product data
      const searchableText = this.createProductSearchableText(productData)

      if (!searchableText) {
        console.warn(`No searchable text generated for product ${productId}`)
        return false
      }

      // Generate embedding
      const embeddingResult = await this.generateEmbedding(searchableText)

      // Update product with embedding in database
      if (!this.dbService.db) {
        console.warn('Database not available - cannot update product embedding')
        return false
      }

      await this.dbService.db
        .update(products)
        .set({
          embedding: embeddingResult.embedding,
          vectorizedAt: sql`NOW()`,
          vectorizationModel: embeddingResult.model,
          searchableText: searchableText,
        })
        .where(sql`id = ${productId}`)

      console.log(`Successfully vectorized product ${productId}`)
      return true
    } catch (error) {
      console.error(`Error vectorizing product ${productId}:`, error)
      return false
    }
  }

  /**
   * Vectorize a product asynchronously (fire and forget)
   * This is useful for insert/update operations where we don't want to block
   */
  async vectorizeProductAsync(
    productId: number,
    productData: ProductVectorizationData,
  ): Promise<void> {
    // Run vectorization in the background without blocking
    setImmediate(async () => {
      try {
        await this.vectorizeProduct(productId, productData)
      } catch (error) {
        console.error(
          `Background vectorization failed for product ${productId}:`,
          error,
        )
      }
    })
  }

  /**
   * Check if a product needs vectorization
   */
  async needsVectorization(productId: number): Promise<boolean> {
    if (!this.dbService.db) {
      return false
    }

    try {
      const result = await this.dbService.db
        .select({
          vectorizedAt: products.vectorizedAt,
          updatedAt: products.updatedAt,
        })
        .from(products)
        .where(sql`id = ${productId}`)
        .limit(1)

      if (result.length === 0) {
        return false // Product doesn't exist
      }

      const product = result[0]

      // Needs vectorization if:
      // 1. Never been vectorized (vectorizedAt is null)
      // 2. Product was updated after last vectorization
      return (
        !product.vectorizedAt ||
        (product.updatedAt &&
          product.vectorizedAt &&
          new Date(product.updatedAt) > new Date(product.vectorizedAt))
      )
    } catch (error) {
      console.error(
        `Error checking vectorization status for product ${productId}:`,
        error,
      )
      return false
    }
  }

  /**
   * Batch vectorize multiple products
   */
  async vectorizeProducts(
    productIds: number[],
  ): Promise<{ success: number; failed: number }> {
    let success = 0
    let failed = 0

    for (const productId of productIds) {
      try {
        // Get product data
        if (!this.dbService.db) {
          failed++
          continue
        }

        const result = await this.dbService.db
          .select({
            productName: products.productName,
            companyName: products.companyName,
            category: products.category,
            subcategory: products.subcategory,
            certificateType: products.certificateType,
            status: products.status,
            address: products.address,
          })
          .from(products)
          .where(sql`id = ${productId}`)
          .limit(1)

        if (result.length === 0) {
          failed++
          continue
        }

        const productData = result[0]
        const vectorized = await this.vectorizeProduct(productId, productData)

        if (vectorized) {
          success++
        } else {
          failed++
        }

        // Add small delay to avoid rate limits
        await new Promise((resolve) => setTimeout(resolve, 100))
      } catch (error) {
        console.error(
          `Error in batch vectorization for product ${productId}:`,
          error,
        )
        failed++
      }
    }

    return { success, failed }
  }
}

// Export a default instance
export const productVectorizationService = new ProductVectorizationService()
