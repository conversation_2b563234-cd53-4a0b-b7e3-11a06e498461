'use client'

export const runtime = 'edge'

import {
  ArrowLeft,
  Edit3,
  PlusCircle,
  Server,
  Settings,
  Trash2,
  Zap,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { Link, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'
import type { AdminUser, ServiceConfiguration } from '@/types'
import { UserRole } from '@/types/roles'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function ServicesPage() {
  const [services, setServices] = useState<ServiceConfiguration[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const _router = useRouter()
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null)

  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true)
      try {
        const meResponse = await api.admin.getMe()
        // Type assertion for user object within ApiResponse
        const userFromApi = meResponse.user as AdminUser | undefined

        if (
          !userFromApi ||
          (userFromApi.role !== UserRole.ADMIN &&
            userFromApi.role !== UserRole.EDITOR)
        ) {
          setError(
            'Access Denied: You do not have permission to view this page.'
          )
          setLoading(false)
          return
        }
        setCurrentUser(userFromApi)

        const response = await api.admin.listServices()
        const servicesData = response.data || response

        if (Array.isArray(servicesData)) {
          setServices(servicesData)
        } else {
          console.warn(
            'Unexpected response structure for listServices:',
            response
          )
          setServices([])
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch services.')
        console.error(err)
      } finally {
        setLoading(false)
      }
    }
    fetchInitialData()
  }, [])

  const handleDelete = async (serviceId: number, serviceName: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete service "${serviceName}"? This action cannot be undone.`
      )
    ) {
      try {
        await api.admin.deleteService(serviceId)
        setServices(prevServices =>
          prevServices.filter(service => service.id !== serviceId)
        )
      } catch (err: any) {
        setError(
          err.response?.data?.error ||
            err.message ||
            'Failed to delete service.'
        )
        console.error(err)
      }
    }
  }

  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'R2R_RAG':
        return <Zap size={16} className="text-blue-500" />
      case 'SMTP_PROVIDER':
        return <Server size={16} className="text-green-500" />
      case 'EXTERNAL_API':
        return <Settings size={16} className="text-purple-500" />
      default:
        return <Server size={16} className="text-gray-500" />
    }
  }

  const getServiceTypeLabel = (type: string) => {
    switch (type) {
      case 'R2R_RAG':
        return 'R2R RAG'
      case 'SMTP_PROVIDER':
        return 'SMTP Provider'
      case 'EXTERNAL_API':
        return 'External API'
      default:
        return type
    }
  }

  const canPerformWriteActions = currentUser?.role === UserRole.ADMIN

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  if (
    !currentUser ||
    (currentUser.role !== UserRole.ADMIN &&
      currentUser.role !== UserRole.EDITOR)
  ) {
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative"
          role="alert"
        >
          Access Denied. You do not have permission to view this page.
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          Services
        </h1>
        {canPerformWriteActions && (
          <Link
            href="/admin/services/new"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out"
          >
            <PlusCircle size={20} className="mr-2" />
            Add New Service
          </Link>
        )}
      </div>

      {services.length === 0 ? (
        <div className="text-center py-10">
          <Server size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500 text-lg">No services found.</p>
          {canPerformWriteActions && (
            <p className="text-gray-400 mt-1">
              Add one to start managing services.
            </p>
          )}
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Service
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Description
                </th>
                {canPerformWriteActions && (
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {services.map(service => (
                <tr
                  key={service.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getServiceIcon(service.type)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {service.name}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {getServiceTypeLabel(service.type)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        service.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {service.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">
                    {service.description || '-'}
                  </td>
                  {canPerformWriteActions && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/services/${service.id}`}
                        className="text-indigo-600 hover:text-indigo-900 mr-3 transition duration-150"
                      >
                        <Edit3 size={18} className="inline-block" />
                      </Link>
                      <button
                        onClick={() => handleDelete(service.id, service.name)}
                        className="text-red-600 hover:text-red-900 transition duration-150"
                        title="Delete Service"
                      >
                        <Trash2 size={18} className="inline-block" />
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <div className="mt-6">
        <Link
          href="/admin/dashboard"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to Dashboard
        </Link>
      </div>
    </div>
  )
}
