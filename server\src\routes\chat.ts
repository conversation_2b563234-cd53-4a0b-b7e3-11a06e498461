import express, { type Request, type Response } from 'express'
import { v4 as uuidv4 } from 'uuid'
import {
  asyncError<PERSON><PERSON><PERSON>,
  ErrorType,
  enhancedErrorHandler,
  SecurityError,
} from '../middleware/errorHandling'
import {
  chatMessageSchema,
  imageAnalysisSchema,
  validateBody,
} from '../middleware/inputValidation'
import {
  chatLimiter,
  secureErrorHandler,
  uploadLimiter,
} from '../middleware/security'
import { BotService } from '../services/botService'
import DatabaseService from '../services/database'
import MessageHandlerService from '../services/messageHandler'
import openaiService from '../services/openai'
import whatsappService from '../services/whatsapp'
import type {
  ChatMessage,
  ChatSession,
  ConsolidatedMessageRequest,
  ErrorResponse,
  SessionResponse,
} from '../types'
import { sessionManager } from '../utils/sessionManager'
import { AI_CONFIG } from '../constants'

const DEFAULT_MODEL = AI_CONFIG.DEFAULT_MODEL
const router: express.Router = express.Router()
const messageHandler = new MessageHandlerService()

// Create a new chat session
router.post(
  '/session',
  asyncErrorHandler(async (req: Request, res: Response<SessionResponse>) => {
    const { userId } = req.user || {}
    const { sessionId } = sessionManager.createSession(
      userId?.toString(),
      req.ip,
      req.get('User-Agent'),
    )

    // Immediately create database session for web platform
    const dbService = new DatabaseService()
    await dbService.createChatSession(
      sessionId,
      'web',
      undefined,
      userId?.toString(),
    )

    res.json({ sessionId })
  }),
)

// Get chat session
router.get(
  '/session/:sessionId',
  asyncErrorHandler(
    async (req: Request, res: Response<ChatSession | ErrorResponse>) => {
      const { sessionId } = req.params

      // Validate session ID format
      if (
        !sessionId ||
        !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
          sessionId,
        )
      ) {
        throw new SecurityError(
          'Invalid session ID',
          ErrorType.VALIDATION_ERROR,
          400,
          true,
        )
      }

      const session = sessionManager.getSession(sessionId)

      if (!session) {
        throw new SecurityError(
          'Chat session not found or has expired',
          ErrorType.NOT_FOUND_ERROR,
          404,
          true,
        )
      }

      res.json(session)
    },
  ),
)

// Send a text message
router.post(
  '/message',
  chatLimiter,
  validateBody(chatMessageSchema),
  asyncErrorHandler(async (req: Request, res: Response) => {
    const { sessionId, message, model, botSlug } = req.body

    // Get session using session manager
    const session = sessionManager.getSession(sessionId)
    if (!session) {
      throw new SecurityError(
        'Chat session not found or has expired',
        ErrorType.NOT_FOUND_ERROR,
        404,
        true,
        { sessionId: sessionId.substring(0, 20) },
      )
    }

    // Get bot configuration
    const dbService = new DatabaseService()
    const botService = new BotService(dbService.db)
    const siteId = Number.parseInt(process.env.DEFAULT_SITE_ID || '1')

    const botConfig = await botService.getBotConfigForChat(siteId, botSlug)
    console.log('botConfig', { siteId, botSlug, botConfig })
    if (botSlug && !botConfig) {
      throw new SecurityError(
        'Bot configuration not found',
        ErrorType.NOT_FOUND_ERROR,
        404,
        true,
        { botSlug },
      )
    }
    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
    }

    // Update session with new message
    const updated = sessionManager.updateSession(sessionId, userMessage)
    if (!updated) {
      throw new SecurityError(
        'Failed to update chat session',
        ErrorType.INTERNAL_ERROR,
        500,
        true,
        { sessionId: sessionId.substring(0, 20) },
      )
    }

    // Use messageHandler to process the message with halal knowledge tools
    const request: ConsolidatedMessageRequest = {
      message,
      sessionId,
      platform: 'web',
      messageType: 'text',
      userId: sessionId, // Use sessionId as userId for web platform
      systemPrompt: botConfig?.systemPrompt,
      botId: botConfig?.id,
      botSlug: botConfig?.slug,
      config: {
        maxMessageHistory: 10,
        maxToolCallIterations: 5,
        defaultModel: botConfig?.model || model || DEFAULT_MODEL,
        temperature: botConfig?.temperature || 0.7,
        enableToolCalling: true,
        platform: 'web',
      },
    }

    const response = await messageHandler.handleIncomingMessage(request)

    if (!response.success) {
      throw new SecurityError(
        response.error || 'Failed to get AI response',
        ErrorType.EXTERNAL_SERVICE_ERROR,
        503,
        true,
        { service: 'MessageHandler', model: model || 'default' },
      )
    }

    res.json({
      message: response.message || '',
      answer: response.answer, // Include answer field for halal knowledge responses
      sources: response.sources, // Include sources for halal knowledge responses
      sessionId,
      usage: response.usage,
    })
  }),
)

// Analyze image with optional text prompt
router.post(
  '/image',
  uploadLimiter,
  validateBody(imageAnalysisSchema),
  asyncErrorHandler(async (req: Request, res: Response) => {
    const { sessionId, imageUrl, prompt, model } = req.body

    // Get session using session manager
    const session = sessionManager.getSession(sessionId)
    if (!session) {
      throw new SecurityError(
        'Session not found',
        ErrorType.NOT_FOUND_ERROR,
        404,
        true,
        { sessionId: sessionId.substring(0, 20) },
      )
    }

    // Add user message with image to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: `[Image uploaded] ${prompt || "What's in this image?"}`,
      imageUrl,
      timestamp: new Date(),
    }

    // Update session with new message
    const updated = sessionManager.updateSession(sessionId, userMessage)
    if (!updated) {
      throw new SecurityError(
        'Session not found',
        ErrorType.NOT_FOUND_ERROR,
        404,
        true,
        { sessionId: sessionId.substring(0, 20) },
      )
    }

    // Get response from OpenAI
    const response = await openaiService.analyzeImage(imageUrl, prompt, model)

    if (!response.success) {
      throw new SecurityError(
        response.error || 'Image analysis failed',
        ErrorType.EXTERNAL_SERVICE_ERROR,
        500,
        true,
        { service: 'OpenAI', model: model || 'default' },
      )
    }

    // Add assistant message to session
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response.message || '',
      timestamp: new Date(),
    }

    // Update session with assistant message
    sessionManager.updateSession(sessionId, assistantMessage)

    res.json({
      message: response.message || '',
      sessionId,
      usage: response.usage,
    })
  }),
)

// Clear chat session
router.delete(
  '/session/:sessionId',
  asyncErrorHandler(async (req: Request, res: Response<SessionResponse>) => {
    const { sessionId } = req.params

    // Validate session ID format
    if (
      !sessionId ||
      !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
        sessionId,
      )
    ) {
      throw new SecurityError(
        'Invalid session ID',
        ErrorType.VALIDATION_ERROR,
        400,
        true,
      )
    }

    const deleted = sessionManager.deleteSession(sessionId)

    if (!deleted) {
      throw new SecurityError(
        'Chat session not found or already deleted',
        ErrorType.NOT_FOUND_ERROR,
        404,
        true,
      )
    }

    res.json({
      message: 'Session cleared successfully',
      sessionId,
    })
  }),
)

// Get WhatsApp integration status
router.get(
  '/whatsapp-status',
  asyncErrorHandler(async (_req: Request, res: Response) => {
    const isConfigured = whatsappService.isConfigured()
    const config = whatsappService.getConfig()

    res.json({
      whatsappEnabled: isConfigured,
      phoneNumber: config?.phoneNumberId ? `+${config.phoneNumberId}` : null,
      message: isConfigured
        ? 'WhatsApp integration is active. You can also message us on WhatsApp!'
        : 'WhatsApp integration is not configured.',
    })
  }),
)

// Add session statistics endpoint (for monitoring)
router.get(
  '/stats',
  asyncErrorHandler(async (req: Request, res: Response) => {
    // Only allow authenticated users to view stats
    if (!req.user) {
      throw new SecurityError(
        'Authentication required',
        ErrorType.AUTHENTICATION_ERROR,
        401,
        true,
      )
    }

    const stats = sessionManager.getStats()
    res.json(stats)
  }),
)

// Apply security error handler
router.use(secureErrorHandler)

export default router
