import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { cleanupMocks, mockLanguageContext } from '@/test-utils'
import { ContactForm } from '../contact-form'

// Mock the language context
jest.mock('@/lib/language-context', () => ({
  useLanguage: () => mockLanguageContext,
}))

describe('ContactForm', () => {
  const mockOnSubmit = jest.fn()

  beforeEach(() => {
    cleanupMocks()
    mockOnSubmit.mockClear()
  })

  const defaultProps = {
    onSubmit: mockOnSubmit,
  }

  it('should render all form fields', () => {
    render(<ContactForm {...defaultProps} />)

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/company/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/inquiry category/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/subject/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/message/i)).toBeInTheDocument()
  })

  it('should render compact variant with fewer fields', () => {
    render(<ContactForm {...defaultProps} variant="compact" />)

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.queryByLabelText(/phone number/i)).not.toBeInTheDocument()
    expect(screen.queryByLabelText(/company/i)).not.toBeInTheDocument()
  })

  it('should show validation errors for required fields', async () => {
    const user = userEvent.setup()
    render(<ContactForm {...defaultProps} />)

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/subject is required/i)).toBeInTheDocument()
      expect(screen.getByText(/category is required/i)).toBeInTheDocument()
      expect(screen.getByText(/message is required/i)).toBeInTheDocument()
    })
  })

  it('should validate email format', async () => {
    const user = userEvent.setup()
    render(<ContactForm {...defaultProps} />)

    const emailInput = screen.getByLabelText(/email address/i)
    await user.type(emailInput, 'invalid-email')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument()
    })
  })

  it('should validate message length', async () => {
    const user = userEvent.setup()
    render(<ContactForm {...defaultProps} />)

    const messageInput = screen.getByLabelText(/message/i)
    await user.type(messageInput, 'short')

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(
        screen.getByText(/message must be at least 10 characters/i)
      ).toBeInTheDocument()
    })
  })

  it('should clear validation errors when user starts typing', async () => {
    const user = userEvent.setup()
    render(<ContactForm {...defaultProps} />)

    // Trigger validation errors
    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument()
    })

    // Start typing in name field
    const nameInput = screen.getByLabelText(/full name/i)
    await user.type(nameInput, 'John')

    await waitFor(() => {
      expect(screen.queryByText(/name is required/i)).not.toBeInTheDocument()
    })
  })

  it('should submit form with valid data', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValueOnce(undefined)

    render(<ContactForm {...defaultProps} />)

    // Fill out form
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/phone number/i), '************')
    await user.type(screen.getByLabelText(/company/i), 'Test Company')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '************',
        company: 'Test Company',
        subject: 'Test Subject',
        category: 'general',
        message: 'This is a test message with enough characters.',
      })
    })
  })

  it('should show loading state during submission', async () => {
    const user = userEvent.setup()
    // Mock a delayed submission
    mockOnSubmit.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    )

    render(<ContactForm {...defaultProps} />)

    // Fill out required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    // Check loading state
    expect(screen.getByText(/sending/i)).toBeInTheDocument()
    expect(submitButton).toBeDisabled()

    // Wait for submission to complete
    await waitFor(() => {
      expect(screen.queryByText(/sending/i)).not.toBeInTheDocument()
    })
  })

  it('should show success message after successful submission', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValueOnce(undefined)

    render(<ContactForm {...defaultProps} />)

    // Fill out and submit form
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/message sent successfully/i)).toBeInTheDocument()
    })
  })

  it('should show error message on submission failure', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockRejectedValueOnce(new Error('Submission failed'))

    render(<ContactForm {...defaultProps} />)

    // Fill out and submit form
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/submission failed/i)).toBeInTheDocument()
    })
  })

  it('should reset form after successful submission', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValueOnce(undefined)

    render(<ContactForm {...defaultProps} />)

    const nameInput = screen.getByLabelText(/full name/i)
    const emailInput = screen.getByLabelText(/email address/i)

    // Fill out and submit form
    await user.type(nameInput, 'John Doe')
    await user.type(emailInput, '<EMAIL>')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(nameInput).toHaveValue('')
      expect(emailInput).toHaveValue('')
    })
  })

  it('should show character count for message field', async () => {
    const user = userEvent.setup()
    render(<ContactForm {...defaultProps} />)

    const messageInput = screen.getByLabelText(/message/i)
    await user.type(messageInput, 'Hello')

    expect(screen.getByText('5/500 characters')).toBeInTheDocument()
  })

  it('should handle default submission when no onSubmit prop provided', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)

    // Fill out required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.selectOptions(
      screen.getByLabelText(/inquiry category/i),
      'general'
    )
    await user.type(screen.getByLabelText(/subject/i), 'Test Subject')
    await user.type(
      screen.getByLabelText(/message/i),
      'This is a test message with enough characters.'
    )

    const submitButton = screen.getByRole('button', { name: /send message/i })
    await user.click(submitButton)

    // Should show loading state and then success
    expect(screen.getByText(/sending/i)).toBeInTheDocument()

    await waitFor(
      () => {
        expect(
          screen.getByText(/message sent successfully/i)
        ).toBeInTheDocument()
      },
      { timeout: 3000 }
    )
  })
})
