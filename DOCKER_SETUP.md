# Docker Compose Setup for HalalMono

This Docker Compose configuration provides a complete containerized environment for the HalalMono project, including all services and dependencies.

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers
- Ports 15433, 16000-16005, 16333-16334, 16379 available

### 1. Environment Setup

Create environment files for sensitive data:

```bash
# Create server environment file
cp server/.env.example server/.env.docker

# Edit the file with your API keys
nano server/.env.docker
```

Add your OpenAI API key and other secrets to `server/.env.docker`:

```env
OPENAI_API_KEY=your_openai_api_key_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
```

### 2. Start All Services

```bash
# Start all core services (excludes crawler)
docker-compose up -d

# Or start with logs visible
docker-compose up
```

### 3. Initialize Database

Wait for PostgreSQL to be ready, then run migrations:

```bash
# Check if database is ready
docker-compose logs postgres

# Run database migrations (if needed)
docker-compose exec server npm run db:migrate
docker-compose exec server npm run db:seed
```

## 📋 Services Overview

| Service        | Port  | Description             | URL                    |
| -------------- | ----- | ----------------------- | ---------------------- |
| **Frontend**   | 16000 | Main user interface     | http://localhost:16000 |
| **Server**     | 16001 | API backend             | http://localhost:16001 |
| **Admin**      | 16005 | Admin panel             | http://localhost:16005 |
| **Selangor**   | 16002 | Selangor portal         | http://localhost:16002 |
| **PostgreSQL** | 15433 | Database                | localhost:15433        |
| **Qdrant**     | 16333 | Vector database         | http://localhost:16333 |
| **Redis**      | 16379 | Cache & sessions        | localhost:16379        |
| **Crawler**    | -     | Web crawler (on-demand) | -                      |

## 🔧 Port Configuration

All ports are configured to avoid conflicts with other Docker Compose setups:

- **Standard ports avoided**: 3000, 5432, 6333, 6379
- **Custom port range**: 15000-16999
- **Database ports**: 15433 (PostgreSQL)
- **Application ports**: 16000-16005
- **Service ports**: 16333 (Qdrant), 16379 (Redis)

## 🛠️ Common Commands

### Service Management

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart a specific service
docker-compose restart server

# View logs
docker-compose logs -f server
docker-compose logs --tail=100 frontend

# Check service status
docker-compose ps
```

### Database Operations

```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U halal_user -d halal_chat

# Run database migrations
docker-compose exec server npm run db:migrate

# Seed database with test data
docker-compose exec server npm run db:seed

# Database backup
docker-compose exec postgres pg_dump -U halal_user halal_chat > backup.sql
```

### Crawler Operations

```bash
# Start crawler (runs once and stops)
docker-compose --profile crawler up crawler

# Run crawler with custom URL
docker-compose run --rm crawler python crawl_website.py "https://example.com" 3 100

# Push crawled data to Qdrant
docker-compose run --rm crawler python push_to_qdrant.py "my_collection" "http://qdrant:6333" "output"
```

## 🔍 Monitoring & Health Checks

All services include health checks. Monitor service health:

```bash
# Check all service health
docker-compose ps

# View health check logs
docker inspect halalmono-server --format='{{json .State.Health}}'
```

Health check endpoints:

- Server: http://localhost:16001/health
- Frontend: http://localhost:16000
- Admin: http://localhost:16005
- Qdrant: http://localhost:16333/health

## 🗄️ Data Persistence

Data is persisted in Docker volumes:

```bash
# List volumes
docker volume ls | grep halalmono

# Backup volumes
docker run --rm -v halalmono_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .

# Remove all data (⚠️ DESTRUCTIVE)
docker-compose down -v
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**

   ```bash
   # Check what's using a port
   lsof -i :16001

   # Kill process using port
   kill -9 $(lsof -t -i:16001)
   ```

2. **Database connection issues**

   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres

   # Test database connection
   docker-compose exec postgres pg_isready -U halal_user
   ```

3. **Build failures**

   ```bash
   # Rebuild specific service
   docker-compose build --no-cache server

   # Clean build
   docker system prune -a
   docker-compose build --no-cache
   ```

4. **Memory issues**

   ```bash
   # Check container resource usage
   docker stats

   # Increase Docker memory limit in Docker Desktop
   ```

### Service-Specific Debugging

```bash
# Server debugging
docker-compose exec server npm run dev:local

# Check environment variables
docker-compose exec server env | grep DATABASE

# Frontend debugging
docker-compose exec frontend npm run dev

# View container filesystem
docker-compose exec server sh
```

## 🔒 Security Notes

- Default passwords are used for development
- Change all passwords in production
- API keys are loaded from environment files
- Services communicate through internal Docker network
- Only necessary ports are exposed to host

## 📦 Production Deployment

For production deployment:

1. Update environment variables
2. Use production-ready secrets management
3. Configure reverse proxy (nginx)
4. Set up SSL certificates
5. Configure monitoring and logging
6. Set up automated backups

```bash
# Production environment
cp docker-compose.yml docker-compose.prod.yml
# Edit docker-compose.prod.yml for production settings
```

## 🤝 Contributing

When adding new services:

1. Use ports in range 16000-16999
2. Add health checks
3. Use the halalmono-network
4. Add service to this documentation
5. Include environment variables in .env.example files
