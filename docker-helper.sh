#!/bin/bash

# HalalMono Docker Helper Script
# Provides convenient commands for managing the Docker Compose setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Check if Docker and Docker Compose are available
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available"
        exit 1
    fi
}

# Show help
show_help() {
    echo "HalalMono Docker Helper Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  status          Show service status"
    echo "  logs [service]  Show logs (optionally for specific service)"
    echo "  build           Build all images"
    echo "  clean           Clean up containers and images"
    echo "  db-init         Initialize database with migrations and seed data"
    echo "  db-reset        Reset database (⚠️  DESTRUCTIVE)"
    echo "  crawler         Run the web crawler"
    echo "  backup          Backup database and volumes"
    echo "  health          Check health of all services"
    echo "  ports           Show port mappings"
    echo "  env-check       Check environment configuration"
    echo "  help            Show this help message"
}

# Start services
start_services() {
    print_header "Starting HalalMono Services"
    print_info "Starting core services (excluding crawler)..."
    docker compose up -d
    print_success "Services started successfully!"
    print_info "Access URLs:"
    echo "  Frontend:  http://localhost:16000"
    echo "  Server:    http://localhost:16001"
    echo "  Admin:     http://localhost:16005"
    echo "  Selangor:  http://localhost:16002"
    echo "  Qdrant:    http://localhost:16333"
}

# Stop services
stop_services() {
    print_header "Stopping HalalMono Services"
    docker compose down
    print_success "Services stopped successfully!"
}

# Restart services
restart_services() {
    print_header "Restarting HalalMono Services"
    docker compose restart
    print_success "Services restarted successfully!"
}

# Show service status
show_status() {
    print_header "Service Status"
    docker compose ps
}

# Show logs
show_logs() {
    local service=$1
    if [ -n "$service" ]; then
        print_header "Logs for $service"
        docker compose logs -f "$service"
    else
        print_header "All Service Logs"
        docker compose logs -f
    fi
}

# Build images
build_images() {
    print_header "Building Docker Images"
    print_info "This may take several minutes..."
    print_info "Building services with correct build commands:"
    print_info "  - Server: tsc (TypeScript compilation)"
    print_info "  - Frontend: vercel-build (Next.js for Vercel)"
    print_info "  - Admin: build (Next.js standard)"
    print_info "  - Selangor: build (Next.js standard)"
    docker compose build --no-cache
    print_success "Images built successfully!"
}

# Clean up
clean_up() {
    print_header "Cleaning Up Docker Resources"
    print_warning "This will remove stopped containers and unused images"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker compose down
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled"
    fi
}

# Initialize database
init_database() {
    print_header "Initializing Database"
    print_info "Running database migrations and seeding data..."
    
    # Wait for database to be ready
    print_info "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    docker compose exec server npm run db:migrate || print_warning "Migration failed or already applied"
    
    # Seed database
    docker compose exec server npm run db:seed || print_warning "Seeding failed or already done"
    
    print_success "Database initialization completed!"
}

# Reset database
reset_database() {
    print_header "Resetting Database"
    print_error "⚠️  WARNING: This will delete ALL data in the database!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker compose down
        docker volume rm halalmono_postgres_data 2>/dev/null || true
        docker compose up -d postgres
        sleep 15
        init_database
        print_success "Database reset completed!"
    else
        print_info "Database reset cancelled"
    fi
}

# Run crawler
run_crawler() {
    print_header "Running Web Crawler"
    print_info "Starting crawler with default settings..."
    docker compose --profile crawler up crawler
    print_success "Crawler completed!"
}

# Backup data
backup_data() {
    print_header "Backing Up Data"
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    print_info "Creating database backup..."
    docker compose exec postgres pg_dump -U halal_user halal_chat > "$backup_dir/database.sql"
    
    print_info "Creating volume backups..."
    docker run --rm -v halalmono_postgres_data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data .
    docker run --rm -v halalmono_server_uploads:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/server_uploads.tar.gz -C /data .
    
    print_success "Backup completed in $backup_dir"
}

# Check health
check_health() {
    print_header "Health Check"
    print_info "Checking service health..."
    
    services=("frontend:16000" "server:16001/health" "admin:16005" "selangor:16002" "qdrant:16333/health")
    
    for service in "${services[@]}"; do
        IFS=':' read -r name endpoint <<< "$service"
        if curl -f -s "http://localhost:$endpoint" > /dev/null; then
            print_success "$name is healthy"
        else
            print_error "$name is not responding"
        fi
    done
}

# Show port mappings
show_ports() {
    print_header "Port Mappings"
    echo "Service        | Host Port | Container Port | URL"
    echo "---------------|-----------|----------------|--------------------"
    echo "Frontend       | 16000     | 3000          | http://localhost:16000"
    echo "Server         | 16001     | 16001         | http://localhost:16001"
    echo "Admin          | 16005     | 3000          | http://localhost:16005"
    echo "Selangor       | 16002     | 3000          | http://localhost:16002"
    echo "PostgreSQL     | 15433     | 5432          | localhost:15433"
    echo "Qdrant         | 16333     | 6333          | http://localhost:16333"
    echo "Redis          | 16379     | 6379          | localhost:16379"
}

# Check environment
check_environment() {
    print_header "Environment Check"
    
    # Check if environment files exist
    if [ -f "server/.env" ]; then
        print_success "server/.env exists"
    else
        print_warning "server/.env not found - copy from server/.env.example"
    fi
    
    # Check Docker resources
    print_info "Docker system info:"
    docker system df
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        build)
            build_images
            ;;
        clean)
            clean_up
            ;;
        db-init)
            init_database
            ;;
        db-reset)
            reset_database
            ;;
        crawler)
            run_crawler
            ;;
        backup)
            backup_data
            ;;
        health)
            check_health
            ;;
        ports)
            show_ports
            ;;
        env-check)
            check_environment
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
