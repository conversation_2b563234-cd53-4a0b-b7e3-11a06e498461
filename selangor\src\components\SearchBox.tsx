'use client'

import { Loader2, <PERSON>, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'

interface SearchBoxProps {
  className?: string
  placeholder?: string
  onSearch?: (query: string) => void
  onChange?: (query: string) => void
  autoFocus?: boolean
  showButton?: boolean
  size?: 'sm' | 'md' | 'lg'
  value?: string
  defaultValue?: string
}

export function SearchBox({
  className = '',
  placeholder = 'Search Halal Selangor...',
  onSearch,
  onChange,
  autoFocus = false,
  showButton = true,
  size = 'md',
  value,
  defaultValue = '',
}: SearchBoxProps) {
  const [internalQuery, setInternalQuery] = useState(defaultValue)
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Use controlled value if provided, otherwise use internal state
  const query = value !== undefined ? value : internalQuery
  const setQuery = value !== undefined ? () => {} : setInternalQuery

  const sizeClasses = {
    sm: 'h-8 text-sm',
    md: 'h-10 text-base',
    lg: 'h-12 text-lg',
  }

  const buttonSizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
  }

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  const handleSearch = async (searchQuery: string = query) => {
    setIsLoading(true)

    try {
      if (onSearch) {
        onSearch(searchQuery)
      } else {
        // Navigate to search results page
        // Allow empty queries to list all products and companies
        const encodedQuery = encodeURIComponent(searchQuery.trim())
        router.push(`/search?q=${encodedQuery}`)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearch()
    }
    if (e.key === 'Escape') {
      setQuery('')
      inputRef.current?.blur()
    }
  }

  const clearSearch = () => {
    if (value === undefined) {
      setInternalQuery('')
    } else if (onChange) {
      onChange('')
    }
    inputRef.current?.focus()

    // Trigger search with empty query to list all products and companies
    if (onSearch) {
      setTimeout(() => {
        onSearch('')
      }, 0)
    } else {
      setTimeout(() => {
        handleSearch('')
      }, 0)
    }
  }

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={e => {
            const newValue = e.target.value
            if (value === undefined) {
              setInternalQuery(newValue)
            } else if (onChange) {
              onChange(newValue)
            }
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`
            block w-full pl-4 pr-12 border border-gray-300 rounded-lg
            focus:ring-2 focus:ring-green-500 focus:border-green-500
            placeholder-gray-500 bg-white
            ${sizeClasses[size]}
            ${className}
          `}
          disabled={isLoading}
        />

        {/* Clear button */}
        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-12 flex items-center pr-2 z-10"
            disabled={isLoading}
          >
            <X
              className={`text-gray-300 hover:text-gray-600 ${buttonSizeClasses.sm}`}
            />
          </button>
        )}

        {/* Search button */}
        {showButton && (
          <button
            type="submit"
            disabled={isLoading}
            className={`
              absolute inset-y-0 right-0 flex items-center pr-3
              text-green-600 hover:text-green-700 disabled:text-gray-400
              disabled:cursor-not-allowed
            `}
          >
            {isLoading ? (
              <Loader2 className={`animate-spin ${buttonSizeClasses[size]}`} />
            ) : (
              <Search className={buttonSizeClasses[size]} />
            )}
          </button>
        )}
      </div>
    </form>
  )
}
