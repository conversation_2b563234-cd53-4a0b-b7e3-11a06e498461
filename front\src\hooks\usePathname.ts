'use client'

import { useEffect, useState } from 'react'

export function usePathname() {
  const [pathname, setPathname] = useState('')

  useEffect(() => {
    // Set initial pathname
    setPathname(window.location.pathname)

    // Listen for navigation changes
    const handlePopState = () => {
      setPathname(window.location.pathname)
    }

    window.addEventListener('popstate', handlePopState)

    // For Next.js navigation, we need to observe DOM changes
    // This is a simple approach that works for most cases
    const observer = new MutationObserver(() => {
      const currentPathname = window.location.pathname
      setPathname(prevPathname => {
        if (currentPathname !== prevPathname) {
          return currentPathname
        }
        return prevPathname
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => {
      window.removeEventListener('popstate', handlePopState)
      observer.disconnect()
    }
  }, []) // Remove pathname dependency to avoid infinite loop

  return pathname
}
