# Twilio Integration Tests

Clean, organized testing suite for the complete Twilio integration pipeline.

## 🧪 Available Tests

### **Complete Integration Test**

```bash
# Test the entire pipeline: Environment → Database → R2R → OpenAI → Twilio
pnpm test:twilio
# or
node src/tests/test-hono-twilio-integration.mjs
```

**What it tests:**

- ✅ Environment configuration (all required variables)
- ✅ Database connectivity and Twilio tables
- ✅ R2R knowledge base search functionality
- ✅ OpenAI tool calling with halal knowledge
- ✅ Twilio webhook processing
- ✅ WhatsApp character limit compliance (1500 chars)
- ✅ AI agent intelligent response sizing
- ✅ Complete end-to-end message flow

### **Database Inspection**

```bash
# Quick database inspection tool
pnpm test:db
# or
node src/tests/inspect-database.mjs
```

**What it shows:**

- 📞 Twilio configurations
- ☁️ S3 configurations
- 📨 Recent Twilio messages
- 📊 Summary statistics

## 🎯 Test Requirements

### **Server Must Be Running**

```bash
# Start your Hono.js server first
pnpm dev
```

### **Environment Variables Required**

```env
# Twilio
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=whatsapp:+your_number

# OpenAI
OPENAI_API_KEY=your_openai_key

# R2R Knowledge Base
R2R_URL=your_r2r_url
R2R_USERNAME=your_username
R2R_PASSWORD=your_password
R2R_COLLECTION_ID=your_collection_id

# Database
DATABASE_URL=your_database_url

# S3 (Optional)
S3_ACCESS_KEY_ID=your_s3_key
S3_SECRET_ACCESS_KEY=your_s3_secret
S3_BUCKET_NAME=your_bucket
```

## 🔧 Troubleshooting

### **Common Issues**

1. **"Server not responding"**

   ```bash
   # Make sure server is running
   pnpm dev
   ```

2. **"R2R health check failed"**
   - Check R2R_URL is accessible
   - Verify R2R credentials
   - Ensure R2R service is running

3. **"OpenAI tool calling failed"**
   - Check OPENAI_API_KEY is valid
   - Verify API credits/rate limits
   - Check network connectivity

4. **"Database connection failed"**

   ```bash
   # Check database status
   pnpm db:status:prod
   ```

5. **"No Twilio configurations found"**
   ```bash
   # Run the migration seeder
   pnpm db:seed:twilio
   ```

## 📊 Expected Results

### **All Tests Passing:**

```
🎉 ALL TESTS PASSED!
🚀 Your complete Twilio → OpenAI → R2R → S3 pipeline is working perfectly!

📋 What was tested:
   ✅ Environment configuration
   ✅ Database connectivity and Twilio tables
   ✅ R2R knowledge base search
   ✅ OpenAI tool calling with halal knowledge
   ✅ Twilio webhook processing
   ✅ WhatsApp character limit compliance (1500 chars)
   ✅ AI agent intelligent response sizing
   ✅ Complete end-to-end message flow

🎯 Ready for production deployment!
```

### **Typical AI Response:**

When testing with halal questions, you should get responses like:

```
🤖 AI Response: Wa alaykum assalam! Yes, chicken is generally considered halal in Islam, provided it meets certain conditions. The chicken must be slaughtered according to Islamic guidelines (zabiha)...
```

**Character Limit Compliance:**

- ✅ All responses are under 1500 characters for WhatsApp
- ✅ AI agent intelligently processes R2R knowledge and sizes responses appropriately
- ✅ No message delivery failures due to character limits

## 🚀 Quick Start

```bash
# 1. Start your server
pnpm dev

# 2. Run complete test suite
pnpm test:twilio

# 3. Check database (optional)
pnpm test:db
```

## 📁 Test Files

- `twilio-integration-test.mjs` - Complete integration test suite
- `inspect-database.mjs` - Database inspection utility
- `README.md` - This documentation

## 🎯 Production Readiness

When all tests pass, your Twilio integration is ready for:

- ✅ Production deployment
- ✅ Real Twilio webhook processing
- ✅ Live WhatsApp message handling
- ✅ AI-powered halal knowledge responses
- ✅ Media processing with S3 storage
