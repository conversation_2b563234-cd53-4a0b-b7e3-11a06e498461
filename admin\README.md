# Halal Admin Dashboard

A React TypeScript admin dashboard for managing the Halal Malaysia Portal system. This dashboard is restricted to SUPERADMIN users only and provides comprehensive site management capabilities.

## Features

- **Authentication**: SUPERADMIN-only access with JWT authentication
- **Site Management**: Full CRUD operations for sites (create, read, update, delete)
- **Data Viewing**: View collections, users, WhatsApp configs, Messenger configs, and S3 configs
- **Responsive Design**: Built with shadcn/ui and Tailwind CSS
- **State Management**: Zustand with Pinia-like patterns
- **Cloudflare Pages**: Optimized for Cloudflare hosting

## Prerequisites

- Node.js 18+ and pnpm
- Access to the Halal backend API
- SUPERADMIN user credentials

## Getting Started

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Environment Setup

Copy the environment file and configure it:

```bash
cp .env.example .env.local
```

Update `.env.local` with your API base URL:

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8787  # For development
# NEXT_PUBLIC_API_BASE_URL=https://hapi.primalcom.com  # For production
```

### 3. Development

Start the development server:

```bash
pnpm dev
```

The application will be available at `http://localhost:3001`

### 4. Login

Use your SUPERADMIN credentials to log in. The default seeded SUPERADMIN user is:

- Username: `superadmin`
- Password: (check server logs for the generated password when running `npm run db:seed`)

## Building for Production

```bash
pnpm build
```

This creates an optimized static build in the `out` directory.

## Deployment

### Cloudflare Pages

1. Build the project:

```bash
pnpm build
```

2. Deploy to Cloudflare Pages:

```bash
pnpm deploy
```

Or use the Cloudflare dashboard to connect your repository and set:

- Build command: `pnpm build`
- Build output directory: `out`
- Environment variables: `NEXT_PUBLIC_API_BASE_URL`

## Project Structure

```
admin/
├── src/
│   ├── app/                 # Next.js app router pages
│   │   ├── dashboard/       # Main dashboard
│   │   ├── login/          # Authentication
│   │   ├── sites/          # Site management
│   │   ├── users/          # User management (CRUD)
│   │   ├── collections/    # Collection viewing
│   │   └── configs/        # Configuration viewing
│   ├── components/         # Reusable components
│   │   ├── ui/            # shadcn/ui components
│   │   ├── layout/        # Layout components
│   │   └── forms/         # Form components
│   ├── stores/            # Zustand stores
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility functions
│   └── types/             # TypeScript type definitions
├── public/                # Static assets
└── out/                   # Build output (generated)
```

## Key Features

### Site Management

- Create new sites with domains and configuration
- Edit existing site details
- Delete sites (with safety checks)
- View all sites with search and filtering

### User Management

- **Users**: Complete CRUD operations for user management
  - Create new users with role assignment
  - Edit existing user details and roles
  - Delete users with confirmation
  - View all users with search and filtering

### Data Viewing

- **Collections**: Browse document collections
- **WhatsApp Configs**: View WhatsApp Business API configurations
- **Messenger Configs**: View Facebook Messenger configurations
- **S3 Configs**: View S3 storage configurations

### Security

- SUPERADMIN role requirement
- JWT token authentication
- Automatic token refresh
- Route protection

## API Integration

The dashboard integrates with the Halal backend API endpoints:

- `POST /api/admin/login` - Authentication
- `GET /api/admin/sites` - List sites
- `POST /api/admin/sites` - Create site
- `PUT /api/admin/sites/:id` - Update site
- `DELETE /api/admin/sites/:id` - Delete site
- `GET /api/admin/users` - List users
- `POST /api/admin/users` - Create user
- `GET /api/admin/users/:id` - Get user by ID
- `PUT /api/admin/users/:id` - Update user
- `DELETE /api/admin/users/:id` - Delete user
- `GET /api/admin/collections` - List collections
- `GET /api/admin/whatsapp-configs` - List WhatsApp configs
- `GET /api/admin/facebook-configs` - List Messenger configs
- `GET /api/admin/s3-configurations` - List S3 configs

## Technologies Used

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand
- **Authentication**: JWT
- **Deployment**: Cloudflare Pages
- **Icons**: Lucide React

## Development Notes

- The app uses static export for Cloudflare Pages compatibility
- All API calls are made client-side with proper authentication headers
- Error handling and loading states are implemented throughout
- The design is responsive and follows modern UI patterns

## Support

For issues or questions, please refer to the main project documentation or contact the development team.
