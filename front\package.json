{"name": "front", "version": "0.1.0", "private": true, "scripts": {"vercel-build": "next build", "dev": "next dev --turbopack --port 16000", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy", "deploy:production": "npm run pages:build && wrangler pages deploy --branch main", "preview:production": "npm run pages:build && wrangler pages dev --branch main", "lint": "eslint . --fix", "build": "next build && npm run build:copy-assets", "build:copy-assets": "cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "start": "npm run start:standalone", "start:standalone": "cd .next/standalone && PORT=16000 node server.js", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "node scripts/test-runner.js unit", "test:integration": "node scripts/test-runner.js integration", "test:e2e": "node scripts/test-runner.js e2e", "test:performance": "node scripts/test-runner.js performance", "test:all": "node scripts/test-runner.js all", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "next": "15.3.3", "next-intl": "^4.3.4", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1", "use-callback-ref": "^1.3.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.0.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}