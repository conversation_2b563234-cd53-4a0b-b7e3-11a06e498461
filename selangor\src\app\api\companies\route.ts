import { count, desc, eq } from 'drizzle-orm'
import { type NextRequest } from 'next/server'
import { getHalalSelangorSiteId } from '@/lib/analytics'
import { db } from '@/lib/db'
import { companies } from '@/lib/db/schema'
import {
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-response'

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = Number.parseInt(searchParams.get('page') || '1')
  const limit = Number.parseInt(searchParams.get('limit') || '20')

  try {
    // Get total count for pagination
    const totalResults = await db.select({ count: count() }).from(companies)

    const total = totalResults[0]?.count || 0

    // Calculate pagination
    const offset = (page - 1) * limit
    const hasMore = offset + limit < total

    // Get paginated results
    const companyList = await db
      .select({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
        businessType: companies.businessType,
        category: companies.category,
        subcategory: companies.subcategory,
        address: companies.address,
        state: companies.state,
        postcode: companies.postcode,
        city: companies.city,
        country: companies.country,
        phone: companies.phone,
        fax: companies.fax,
        email: companies.email,
        website: companies.website,
        contactPerson: companies.contactPerson,
        certificateNumber: companies.certificateNumber,
        certificateType: companies.certificateType,
        certificateStatus: companies.certificateStatus,
        issuedDate: companies.issuedDate,
        expiryDate: companies.expiryDate,
        sourceUrl: companies.sourceUrl,
        createdAt: companies.createdAt,
        updatedAt: companies.updatedAt,
      })
      .from(companies)
      .limit(limit)
      .offset(offset)
      .orderBy(desc(companies.createdAt))

    console.log(
      `Companies list found ${total} results, returning ${companyList.length} for page ${page}`
    )

    return createSuccessResponse({
      companies: companyList.map((company: any) => ({
        id: company.id,
        name: company.companyName,
        registrationNumber: company.registrationNumber,
        businessType: company.businessType,
        category: company.category,
        subcategory: company.subcategory,
        address: company.address,
        state: company.state,
        postcode: company.postcode,
        city: company.city,
        country: company.country,
        phone: company.phone,
        fax: company.fax,
        email: company.email,
        website: company.website,
        contactPerson: company.contactPerson,
        certificateNumber: company.certificateNumber,
        certificateType: company.certificateType,
        certificateStatus: company.certificateStatus,
        issuedDate: company.issuedDate,
        expiryDate: company.expiryDate,
        sourceUrl: company.sourceUrl,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
      })),
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    return handleApiError(error, 'Failed to fetch companies')
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.companyName) {
      return createErrorResponse('Company name is required', undefined, 400)
    }

    // Create new company with Selangor site ID
    const newCompany = await db
      .insert(companies)
      .values({
        siteId: getHalalSelangorSiteId(),
        companyName: body.companyName,
        registrationNumber: body.registrationNumber || null,
        businessType: body.businessType || null,
        category: body.category || null,
        subcategory: body.subcategory || null,
        address: body.address || null,
        state: body.state || null,
        postcode: body.postcode || null,
        city: body.city || null,
        country: body.country || 'Malaysia',
        phone: body.phone || null,
        fax: body.fax || null,
        email: body.email || null,
        website: body.website || null,
        contactPerson: body.contactPerson || null,
        certificateNumber: body.certificateNumber || null,
        certificateType: body.certificateType || null,
        certificateStatus: body.certificateStatus || null,
        issuedDate: body.issuedDate || null,
        expiryDate: body.expiryDate || null,
        sourceUrl: body.sourceUrl || null,
        rawData: body.rawData || null,
      })
      .returning({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
        businessType: companies.businessType,
        category: companies.category,
        subcategory: companies.subcategory,
        address: companies.address,
        state: companies.state,
        postcode: companies.postcode,
        city: companies.city,
        country: companies.country,
        phone: companies.phone,
        fax: companies.fax,
        email: companies.email,
        website: companies.website,
        contactPerson: companies.contactPerson,
        certificateNumber: companies.certificateNumber,
        certificateType: companies.certificateType,
        certificateStatus: companies.certificateStatus,
        issuedDate: companies.issuedDate,
        expiryDate: companies.expiryDate,
        sourceUrl: companies.sourceUrl,
        createdAt: companies.createdAt,
        updatedAt: companies.updatedAt,
      })

    console.log('Company created:', newCompany[0])

    return createSuccessResponse(newCompany[0], 'Company created successfully')
  } catch (error) {
    return handleApiError(error, 'Failed to create company')
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return createErrorResponse('Company ID is required', undefined, 400)
    }

    // Validate required fields
    if (!updateData.companyName) {
      return createErrorResponse('Company name is required', undefined, 400)
    }

    // Update company
    const updatedCompany = await db
      .update(companies)
      .set({
        companyName: updateData.companyName,
        registrationNumber: updateData.registrationNumber || null,
        businessType: updateData.businessType || null,
        category: updateData.category || null,
        subcategory: updateData.subcategory || null,
        address: updateData.address || null,
        state: updateData.state || null,
        postcode: updateData.postcode || null,
        city: updateData.city || null,
        country: updateData.country || 'Malaysia',
        phone: updateData.phone || null,
        fax: updateData.fax || null,
        email: updateData.email || null,
        website: updateData.website || null,
        contactPerson: updateData.contactPerson || null,
        certificateNumber: updateData.certificateNumber || null,
        certificateType: updateData.certificateType || null,
        certificateStatus: updateData.certificateStatus || null,
        issuedDate: updateData.issuedDate || null,
        expiryDate: updateData.expiryDate || null,
        sourceUrl: updateData.sourceUrl || null,
        rawData: updateData.rawData || null,
        updatedAt: new Date(),
      })
      .where(eq(companies.id, id))
      .returning({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
        businessType: companies.businessType,
        category: companies.category,
        subcategory: companies.subcategory,
        address: companies.address,
        state: companies.state,
        postcode: companies.postcode,
        city: companies.city,
        country: companies.country,
        phone: companies.phone,
        fax: companies.fax,
        email: companies.email,
        website: companies.website,
        contactPerson: companies.contactPerson,
        certificateNumber: companies.certificateNumber,
        certificateType: companies.certificateType,
        certificateStatus: companies.certificateStatus,
        issuedDate: companies.issuedDate,
        expiryDate: companies.expiryDate,
        sourceUrl: companies.sourceUrl,
        createdAt: companies.createdAt,
        updatedAt: companies.updatedAt,
      })

    if (updatedCompany.length === 0) {
      return createErrorResponse('Company not found', undefined, 404)
    }

    console.log('Company updated:', updatedCompany[0])

    return createSuccessResponse(
      updatedCompany[0],
      'Company updated successfully'
    )
  } catch (error) {
    return handleApiError(error, 'Failed to update company')
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return createErrorResponse('Company ID is required', undefined, 400)
    }

    // Delete company
    const deletedCompany = await db
      .delete(companies)
      .where(eq(companies.id, Number.parseInt(id)))
      .returning({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
      })

    if (deletedCompany.length === 0) {
      return createErrorResponse('Company not found', undefined, 404)
    }

    console.log('Company deleted:', deletedCompany[0])

    return createSuccessResponse(
      deletedCompany[0],
      'Company deleted successfully'
    )
  } catch (error) {
    return handleApiError(error, 'Failed to delete company')
  }
}
