-- Create sites table
CREATE TABLE "sites" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"code" varchar(50) NOT NULL,
	"domains" text[] DEFAULT '{}'::text[] NOT NULL,
	"status" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sites_code_unique" UNIQUE("code")
);

-- Add site_id column to existing tables
ALTER TABLE "users" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "collections" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "whatsapp_config" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "whatsapp_messages" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "facebook_config" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "facebook_messages" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "chat_sessions" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;
ALTER TABLE "chat_messages" ADD COLUMN "site_id" integer NOT NULL DEFAULT 1;

-- Add foreign key constraints
ALTER TABLE "users" ADD CONSTRAINT "users_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "collections" ADD CONSTRAINT "collections_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "whatsapp_config" ADD CONSTRAINT "whatsapp_config_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "whatsapp_messages" ADD CONSTRAINT "whatsapp_messages_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "facebook_config" ADD CONSTRAINT "facebook_config_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "facebook_messages" ADD CONSTRAINT "facebook_messages_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_site_id_fkey" FOREIGN KEY ("site_id") REFERENCES "sites"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Insert default site
INSERT INTO "sites" ("id", "name", "code", "domains", "status", "created_at", "updated_at") 
VALUES (1, 'Default Site', 'default', '{"localhost", "127.0.0.1"}', true, now(), now());

-- Remove default values from site_id columns (they were only needed for migration)
ALTER TABLE "users" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "collections" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "whatsapp_config" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "whatsapp_messages" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "facebook_config" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "facebook_messages" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "chat_sessions" ALTER COLUMN "site_id" DROP DEFAULT;
ALTER TABLE "chat_messages" ALTER COLUMN "site_id" DROP DEFAULT;
