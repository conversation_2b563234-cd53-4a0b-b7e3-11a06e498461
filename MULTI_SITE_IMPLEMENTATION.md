# Multi-Site Architecture Implementation

## Overview

This document outlines the implementation of a multi-site architecture for the halal chat application, allowing multiple sites to be managed from a single codebase with site-specific data isolation.

## Changes Made

### 1. Database Schema Changes

- **Added `sites` table** with fields:
  - `id` (serial primary key)
  - `name` (varchar 255, site display name)
  - `code` (varchar 50, unique site identifier)
  - `domains` (text array, allowed domains for the site)
  - `status` (boolean, active/inactive)
  - `created_at`, `updated_at` (timestamps)

- **Added `siteId` foreign key** to all relevant tables:
  - `users`
  - `collections`
  - `whatsapp_config`
  - `whatsapp_messages`
  - `facebook_config`
  - `facebook_messages`
  - `chat_sessions`
  - `chat_messages`

### 2. Database Migration

- Created migration file: `server/drizzle/0001_add_sites_table.sql`
- Includes foreign key constraints and default site seeding
- Updated seed file to include default site with ID 1

### 3. Server API Changes

- **Updated API URL structure**: All endpoints now use `/api/sites/{siteId}/` prefix
- **Updated route handlers**: All handler functions now accept `siteId` parameter
- **Updated authentication**: Login endpoints validate users against specific sites
- **Updated database queries**: All queries now filter by `siteId`

### 4. Frontend Changes

- **Added environment variable**: `NEXT_PUBLIC_DEFAULT_SITE_ID`
- **Updated API client**: Automatically prefixes all requests with site ID
- **Exception for login**: Login endpoints don't use site prefix initially

### 5. Environment Configuration

- **Server wrangler.toml**: Added `DEFAULT_SITE_ID = "1"`
- **Frontend wrangler.toml**: Added `NEXT_PUBLIC_DEFAULT_SITE_ID = "1"`

## API Endpoint Changes

### Before:

```
POST /api/admin/login
GET /api/admin/users
POST /api/chat/session
GET /api/sessions
```

### After:

```
POST /api/admin/login (no site prefix for initial login)
GET /api/sites/1/admin/users
POST /api/sites/1/chat/session
GET /api/sites/1/sessions
```

## Default Site Configuration

- **Site ID**: 1
- **Name**: "Default Site"
- **Code**: "default"
- **Domains**: ["localhost", "127.0.0.1", "halal.primalcom.com"]
- **Status**: Active (true)

## Database Relations

- All site-related tables now have foreign key relationships to the `sites` table
- Cascading updates are enabled, but deletes are restricted to prevent data loss

## Authentication Changes

- Login methods now accept optional `siteId` parameter
- User lookup queries filter by site ID when provided
- JWT tokens remain unchanged (no site info in token for now)

## Next Steps Required

### 1. Complete Route Handler Updates

- Update all remaining path references in handlers to use `normalizedPath`
- Update WhatsApp, Facebook, Upload, and Session handlers completely

### 2. Database Query Updates

- Update all database service methods to filter by `siteId`
- Update chat session creation to include `siteId`
- Update message creation to include `siteId`

### 3. Frontend Login Flow

- Update login components to handle site-specific authentication
- Add site selection if multiple sites are supported

### 4. Testing

- Run database migration
- Test login with site ID
- Test API endpoints with site prefix
- Verify data isolation between sites

### 5. Additional Features (Future)

- Site management admin interface
- Dynamic site switching
- Site-specific configurations
- Domain-based site detection

## Migration Commands

### To apply the database changes:

```bash
# In server directory
npm run db:migrate

# Or manually run the SQL migration
psql -d your_database -f drizzle/0001_add_sites_table.sql
```

### To seed the database:

```bash
# In server directory
npm run db:seed
```

## Testing the Implementation

### 1. Test Login with Site ID:

```bash
curl -X POST http://localhost:8787/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 2. Test Site-Prefixed Endpoint:

```bash
curl -X GET http://localhost:8787/api/sites/1/admin/users \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Notes

- The implementation maintains backward compatibility during transition
- Site ID 1 is used as the default for all existing data
- All existing users and data are automatically assigned to site ID 1
- The frontend automatically uses the default site ID for all requests
