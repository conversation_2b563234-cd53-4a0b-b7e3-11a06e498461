import { act, renderHook } from '@testing-library/react'
import {
  cleanupMocks,
  mockAnnouncements,
  mockSearchResults,
} from '@/test-utils'
import {
  useAnnouncements,
  useDebounce,
  useFilters,
  usePagination,
  useSearch,
} from '../use-api'

// Mock the API
jest.mock('@/lib/api', () => ({
  api: {
    announcements: {
      getAll: jest.fn(),
    },
    search: {
      search: jest.fn(),
    },
    utils: {
      isSuccess: jest.fn(),
      handleError: jest.fn(),
    },
  },
}))

import { api } from '@/lib/api'

describe('API Hooks', () => {
  beforeEach(() => {
    cleanupMocks()
    jest.clearAllMocks()
  })

  describe('useAnnouncements', () => {
    it('should fetch announcements on mount', async () => {
      const mockResponse = { success: true, data: mockAnnouncements }
      ;(api.announcements.getAll as jest.Mock).mockResolvedValue(mockResponse)
      ;(api.utils.isSuccess as jest.Mock).mockReturnValue(true)

      const { result } = renderHook(() => useAnnouncements())

      expect(result.current.loading).toBe(true)
      expect(api.announcements.getAll).toHaveBeenCalledWith({})

      // Wait for the async operation to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      expect(result.current.loading).toBe(false)
      expect(result.current.data).toEqual(mockAnnouncements)
      expect(result.current.error).toBeNull()
    })

    it('should handle API errors', async () => {
      const mockError = 'Failed to fetch announcements'
      ;(api.announcements.getAll as jest.Mock).mockRejectedValue(
        new Error(mockError)
      )
      ;(api.utils.handleError as jest.Mock).mockReturnValue(mockError)

      const { result } = renderHook(() => useAnnouncements())

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      expect(result.current.loading).toBe(false)
      expect(result.current.data).toBeNull()
      expect(result.current.error).toBe(mockError)
    })

    it('should refetch data when refetch is called', async () => {
      const mockResponse = { success: true, data: mockAnnouncements }
      ;(api.announcements.getAll as jest.Mock).mockResolvedValue(mockResponse)
      ;(api.utils.isSuccess as jest.Mock).mockReturnValue(true)

      const { result } = renderHook(() => useAnnouncements())

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0))
      })

      // Clear the mock to verify refetch calls
      ;(api.announcements.getAll as jest.Mock).mockClear()

      await act(async () => {
        result.current.refetch()
      })

      expect(api.announcements.getAll).toHaveBeenCalledTimes(1)
    })
  })

  describe('useSearch', () => {
    it('should perform search and update results', async () => {
      const mockResponse = { success: true, data: mockSearchResults }
      ;(api.search.search as jest.Mock).mockResolvedValue(mockResponse)
      ;(api.utils.isSuccess as jest.Mock).mockReturnValue(true)

      const { result } = renderHook(() => useSearch())

      await act(async () => {
        await result.current.search({ query: 'test' })
      })

      expect(api.search.search).toHaveBeenCalledWith({ query: 'test' })
      expect(result.current.results).toEqual(mockSearchResults)
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBeNull()
    })

    it('should handle search errors', async () => {
      const mockError = 'Search failed'
      ;(api.search.search as jest.Mock).mockRejectedValue(new Error(mockError))
      ;(api.utils.handleError as jest.Mock).mockReturnValue(mockError)

      const { result } = renderHook(() => useSearch())

      await act(async () => {
        await result.current.search({ query: 'test' })
      })

      expect(result.current.results).toEqual([])
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(mockError)
    })

    it('should clear results', () => {
      const { result } = renderHook(() => useSearch())

      act(() => {
        result.current.clearResults()
      })

      expect(result.current.results).toEqual([])
      expect(result.current.error).toBeNull()
    })
  })

  describe('usePagination', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => usePagination())

      expect(result.current.page).toBe(1)
      expect(result.current.limit).toBe(10)
    })

    it('should initialize with custom values', () => {
      const { result } = renderHook(() => usePagination(2, 20))

      expect(result.current.page).toBe(2)
      expect(result.current.limit).toBe(20)
    })

    it('should navigate to next page', () => {
      const { result } = renderHook(() => usePagination())

      act(() => {
        result.current.nextPage()
      })

      expect(result.current.page).toBe(2)
    })

    it('should navigate to previous page', () => {
      const { result } = renderHook(() => usePagination(3))

      act(() => {
        result.current.prevPage()
      })

      expect(result.current.page).toBe(2)
    })

    it('should not go below page 1', () => {
      const { result } = renderHook(() => usePagination(1))

      act(() => {
        result.current.prevPage()
      })

      expect(result.current.page).toBe(1)
    })

    it('should go to specific page', () => {
      const { result } = renderHook(() => usePagination())

      act(() => {
        result.current.goToPage(5)
      })

      expect(result.current.page).toBe(5)
    })

    it('should change limit and reset page', () => {
      const { result } = renderHook(() => usePagination(3, 10))

      act(() => {
        result.current.changeLimit(20)
      })

      expect(result.current.limit).toBe(20)
      expect(result.current.page).toBe(1)
    })

    it('should reset to initial values', () => {
      const { result } = renderHook(() => usePagination(2, 15))

      act(() => {
        result.current.nextPage()
        result.current.changeLimit(30)
      })

      act(() => {
        result.current.reset()
      })

      expect(result.current.page).toBe(2)
      expect(result.current.limit).toBe(15)
    })
  })

  describe('useFilters', () => {
    const initialFilters = { category: '', status: 'active' }

    it('should initialize with provided filters', () => {
      const { result } = renderHook(() => useFilters(initialFilters))

      expect(result.current.filters).toEqual(initialFilters)
    })

    it('should update single filter', () => {
      const { result } = renderHook(() => useFilters(initialFilters))

      act(() => {
        result.current.updateFilter('category', 'announcement')
      })

      expect(result.current.filters).toEqual({
        category: 'announcement',
        status: 'active',
      })
    })

    it('should update multiple filters', () => {
      const { result } = renderHook(() => useFilters(initialFilters))

      act(() => {
        result.current.updateFilters({
          category: 'news',
          status: 'inactive',
        })
      })

      expect(result.current.filters).toEqual({
        category: 'news',
        status: 'inactive',
      })
    })

    it('should clear single filter', () => {
      const { result } = renderHook(() => useFilters(initialFilters))

      act(() => {
        result.current.clearFilter('status')
      })

      expect(result.current.filters).toEqual({ category: '' })
    })

    it('should clear all filters', () => {
      const { result } = renderHook(() => useFilters(initialFilters))

      act(() => {
        result.current.updateFilter('category', 'test')
      })

      act(() => {
        result.current.clearAllFilters()
      })

      expect(result.current.filters).toEqual(initialFilters)
    })

    it('should detect active filters', () => {
      const { result } = renderHook(() =>
        useFilters({ category: '', status: '' })
      )

      expect(result.current.hasActiveFilters()).toBe(false)

      act(() => {
        result.current.updateFilter('category', 'announcement')
      })

      expect(result.current.hasActiveFilters()).toBe(true)
    })
  })

  describe('useDebounce', () => {
    it('should debounce value changes', async () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        { initialProps: { value: 'initial', delay: 100 } }
      )

      expect(result.current).toBe('initial')

      rerender({ value: 'updated', delay: 100 })

      // Value should not change immediately
      expect(result.current).toBe('initial')

      // Wait for debounce delay
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150))
      })

      expect(result.current).toBe('updated')
    })

    it('should cancel previous timeout on rapid changes', async () => {
      const { result, rerender } = renderHook(
        ({ value, delay }) => useDebounce(value, delay),
        { initialProps: { value: 'initial', delay: 100 } }
      )

      rerender({ value: 'first', delay: 100 })
      rerender({ value: 'second', delay: 100 })
      rerender({ value: 'final', delay: 100 })

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150))
      })

      expect(result.current).toBe('final')
    })
  })
})
