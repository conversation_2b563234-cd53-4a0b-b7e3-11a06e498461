#!/usr/bin/env tsx

import { Stagehand } from "@browserbasehq/stagehand";
import chalk from "chalk";

async function debugSelectors() {
  console.log(chalk.blue("🔍 Debugging Douyin page selectors..."));

  const stagehand = new Stagehand({
    env: "LOCAL",
    headless: false, // Keep visible for debugging
    verbose: 1, // Enable verbose logging
    debugDom: true, // Enable DOM debugging
  });

  try {
    await stagehand.init();
    const page = stagehand.page;

    console.log(chalk.yellow("📡 Navigating to Douyin..."));
    await page.goto("https://www.douyin.com", {
      waitUntil: "domcontentloaded",
      timeout: 30000,
    });

    console.log(chalk.green("✅ Page loaded successfully"));

    // Wait for page to fully render
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Try to find search elements using various strategies
    console.log(chalk.blue("\n🔍 Looking for search elements..."));

    // Strategy 1: Use AI to describe the page
    try {
      console.log(
        chalk.yellow("🤖 Using AI to describe the page structure..."),
      );
      const description = await page.extract({
        instruction:
          'Describe the main elements on this page, especially any search box, input field, or navigation elements. Look for text that says "搜索" (search in Chinese) or any input fields.',
        schema: {
          type: "object",
          properties: {
            searchElements: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  description: { type: "string" },
                  location: { type: "string" },
                  text: { type: "string" },
                },
              },
            },
            pageDescription: { type: "string" },
          },
        },
      });
      console.log(
        chalk.cyan("AI Description:"),
        JSON.stringify(description, null, 2),
      );
    } catch (error) {
      console.log(chalk.red("AI description failed:", error.message));
    }

    // Strategy 2: Look for common search selectors
    const searchSelectors = [
      'input[placeholder*="搜索"]',
      'input[placeholder*="search"]',
      'input[data-e2e="searchbar-input"]',
      'input[type="search"]',
      'input[name="search"]',
      'input[id*="search"]',
      'input[class*="search"]',
      ".search-input",
      ".searchbar input",
      '[data-testid*="search"] input',
      "input",
    ];

    console.log(chalk.yellow("\n🎯 Testing search selectors..."));
    for (const selector of searchSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          const placeholder = await element.getAttribute("placeholder");
          const className = await element.getAttribute("class");
          const id = await element.getAttribute("id");
          console.log(chalk.green(`✅ Found: ${selector}`));
          console.log(chalk.cyan(`   Placeholder: ${placeholder}`));
          console.log(chalk.cyan(`   Class: ${className}`));
          console.log(chalk.cyan(`   ID: ${id}`));
        }
      } catch (error) {
        // Ignore individual failures
      }
    }

    // Strategy 3: Get all input elements
    console.log(chalk.yellow("\n📝 Finding all input elements..."));
    try {
      const inputs = await page.$$("input");
      console.log(chalk.cyan(`Found ${inputs.length} input elements:`));

      for (let i = 0; i < Math.min(inputs.length, 10); i++) {
        const input = inputs[i];
        const type = await input.getAttribute("type");
        const placeholder = await input.getAttribute("placeholder");
        const className = await input.getAttribute("class");
        const name = await input.getAttribute("name");

        console.log(chalk.cyan(`  Input ${i + 1}:`));
        console.log(chalk.gray(`    Type: ${type}`));
        console.log(chalk.gray(`    Placeholder: ${placeholder}`));
        console.log(chalk.gray(`    Class: ${className}`));
        console.log(chalk.gray(`    Name: ${name}`));
      }
    } catch (error) {
      console.log(chalk.red("Failed to get input elements:", error.message));
    }

    // Strategy 4: Try AI-powered search
    console.log(chalk.yellow("\n🤖 Trying AI-powered search..."));
    try {
      await page.act(
        "Look for a search box or input field on this page and click on it",
      );
      console.log(chalk.green("✅ AI search action completed"));

      // Wait a moment and see if anything changed
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Try to type in the search
      await page.act('Type "sabah" in the search box');
      console.log(chalk.green("✅ AI typing completed"));

      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (error) {
      console.log(chalk.red("AI search failed:", error.message));
    }

    // Keep browser open for manual inspection
    console.log(
      chalk.blue(
        "\n👀 Browser will stay open for 30 seconds for manual inspection...",
      ),
    );
    console.log(
      chalk.yellow(
        "You can manually inspect the page to find the correct selectors.",
      ),
    );
    await new Promise((resolve) => setTimeout(resolve, 30000));
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
  } finally {
    try {
      await stagehand.close();
      console.log(chalk.blue("\n🔚 Browser closed"));
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

// Run the debug script
debugSelectors().catch(console.error);
