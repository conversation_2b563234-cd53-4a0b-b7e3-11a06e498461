'use client'

export const runtime = 'edge'

import {
  ArrowLeft,
  Edit3,
  Facebook,
  MessageSquare,
  PlusCircle,
  Settings,
  TestTube,
  Trash2,
  Zap,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'
import type { AdminUser, FacebookConfiguration } from '@/types'
import { UserRole } from '@/types/roles'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function FacebookPage() {
  const router = useRouter()
  const [configurations, setConfigurations] = useState<FacebookConfiguration[]>(
    []
  )
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchConfigurations()
  }, [])

  const fetchConfigurations = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await api.get<FacebookConfiguration[]>(
        '/admin/facebook-configurations'
      )
      setConfigurations(response.data)
    } catch (err) {
      console.error('Error fetching Facebook configurations:', err)
      setError('Failed to load Facebook configurations')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: number) => {
    if (
      !confirm('Are you sure you want to delete this Facebook configuration?')
    ) {
      return
    }

    try {
      await api.delete(`/admin/facebook-configurations/${id}`)
      await fetchConfigurations() // Refresh the list
    } catch (err) {
      console.error('Error deleting Facebook configuration:', err)
      setError('Failed to delete Facebook configuration')
    }
  }

  const handleTest = (id: number) => {
    router.push(`/admin/facebook/test?config=${id}`)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Facebook Management
          </h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">
            Loading Facebook configurations...
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Facebook Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage Facebook Messenger configurations and integrations
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/facebook/config">
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Configuration
            </Button>
          </Link>
          <Link href="/admin/facebook/test">
            <Button variant="outline">
              <TestTube className="mr-2 h-4 w-4" />
              Test Integration
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {configurations.map(config => (
          <Card key={config.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Facebook className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg">
                    {config.name || `Config ${config.id}`}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/facebook/config?edit=${config.id}`)
                    }
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(config.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                {config.configured ? (
                  <span className="text-green-600 flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    Active
                  </span>
                ) : (
                  <span className="text-yellow-600 flex items-center gap-1">
                    <Settings className="h-3 w-3" />
                    Needs Configuration
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">App ID:</span>{' '}
                  <span className="text-gray-600">
                    {config.app_id
                      ? `${config.app_id.substring(0, 8)}...`
                      : 'Not set'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Page ID:</span>{' '}
                  <span className="text-gray-600">
                    {config.page_id
                      ? `${config.page_id.substring(0, 8)}...`
                      : 'Not set'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Webhook:</span>{' '}
                  <span className="text-gray-600">
                    {config.webhook_url ? 'Configured' : 'Not set'}
                  </span>
                </div>
              </div>
              <div className="mt-4 flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTest(config.id)}
                  disabled={!config.configured}
                  className="flex-1"
                >
                  <MessageSquare className="mr-1 h-3 w-3" />
                  Test
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    router.push(`/admin/facebook/config?edit=${config.id}`)
                  }
                  className="flex-1"
                >
                  <Settings className="mr-1 h-3 w-3" />
                  Configure
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {configurations.length === 0 && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Facebook className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Facebook Configurations
              </h3>
              <p className="text-gray-600 text-center mb-4">
                Get started by creating your first Facebook Messenger
                configuration.
              </p>
              <Link href="/admin/facebook/config">
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create Configuration
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common Facebook Messenger management tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <Link href="/admin/facebook/config">
              <Button variant="outline" className="w-full justify-start">
                <Settings className="mr-2 h-4 w-4" />
                Configure New Integration
              </Button>
            </Link>
            <Link href="/admin/facebook/test">
              <Button variant="outline" className="w-full justify-start">
                <TestTube className="mr-2 h-4 w-4" />
                Test Existing Integration
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
