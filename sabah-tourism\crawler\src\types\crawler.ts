import type {
  CrawlProgress,
  PlatformConfig,
  SearchResult,
  SocialMediaPost,
} from "./social-media.js";

export interface CrawlerConfig {
  platform: "douyin" | "tiktok" | "instagram" | "facebook" | "youtube";
  keywords: string[];
  maxPosts: number;
  downloadMedia: boolean;
  mediaTypes: ("image" | "video" | "audio")[];
  outputDir: string;
  rateLimiting: {
    requestsPerMinute: number;
    downloadConcurrency: number;
  };
  retryConfig: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
  browserConfig: {
    headless: boolean;
    timeout: number;
    userAgent?: string;
    viewport?: {
      width: number;
      height: number;
    };
  };
  filters?: {
    minLikes?: number;
    minViews?: number;
    dateRange?: {
      from: Date;
      to: Date;
    };
    authors?: string[];
    excludeAuthors?: string[];
  };
}

export interface CrawlerOptions {
  resumeSession?: number;
  forceRecrawl?: boolean;
  dryRun?: boolean;
  verbose?: boolean;
  outputFormat?: "json" | "csv" | "database";
}

export abstract class BaseCrawler {
  protected config: CrawlerConfig;
  protected platformConfig: PlatformConfig;

  constructor(config: CrawlerConfig) {
    this.config = config;
    this.platformConfig = this.getPlatformConfig();
  }

  abstract search(
    keyword: string,
    options?: SearchOptions,
  ): Promise<SearchResult>;
  abstract extractPost(url: string): Promise<SocialMediaPost | null>;
  abstract downloadMedia(
    mediaUrl: string,
    outputPath: string,
  ): Promise<boolean>;

  protected abstract getPlatformConfig(): PlatformConfig;
  protected abstract validateConfig(): boolean;

  public async crawl(options?: CrawlerOptions): Promise<CrawlProgress> {
    throw new Error("Method must be implemented by subclass");
  }

  public async pause(): Promise<void> {
    throw new Error("Method must be implemented by subclass");
  }

  public async resume(): Promise<void> {
    throw new Error("Method must be implemented by subclass");
  }

  public async stop(): Promise<void> {
    throw new Error("Method must be implemented by subclass");
  }
}

export interface SearchOptions {
  maxResults?: number;
  sortBy?: "relevance" | "date" | "popularity";
  dateRange?: {
    from: Date;
    to: Date;
  };
  cursor?: string;
}

export interface DownloadProgress {
  url: string;
  fileName: string;
  totalBytes: number;
  downloadedBytes: number;
  percentage: number;
  speed: number; // bytes per second
  estimatedTimeRemaining: number; // in milliseconds
  status: "pending" | "downloading" | "completed" | "failed" | "cancelled";
  error?: string;
}

export interface CrawlerEvents {
  progress: (progress: CrawlProgress) => void;
  "post-extracted": (post: SocialMediaPost) => void;
  "media-downloaded": (progress: DownloadProgress) => void;
  error: (error: Error) => void;
  completed: (summary: CrawlSummary) => void;
  paused: () => void;
  resumed: () => void;
}

export interface CrawlSummary {
  sessionId: number;
  platform: string;
  keywords: string[];
  duration: number; // in milliseconds
  totalPosts: number;
  successfulPosts: number;
  failedPosts: number;
  totalMedia: number;
  downloadedMedia: number;
  totalSize: number; // in bytes
  errors: number;
  startTime: Date;
  endTime: Date;
}
