import { Hono } from 'hono';
import {
  createDocument,
  deleteDocument,
  getDocument,
  getDocuments,
  getDocumentsByCollection,
  updateDocument,
  uploadDocument,
} from '@/controllers/adminDocumentsController';
import { authenticateAdmin } from '@/middleware/auth';

const documentsRouter = new Hono();

// Apply authentication middleware to all routes
documentsRouter.use('*', authenticateAdmin);

documentsRouter.post('/', createDocument);
documentsRouter.get('/', getDocuments);
documentsRouter.get('/:id', getDocument);
documentsRouter.put('/:id', updateDocument);
documentsRouter.delete('/:id', deleteDocument);

// Upload document
documentsRouter.post('/upload', uploadDocument);

// Get documents by collection
documentsRouter.get('/collection/:collectionId', getDocumentsByCollection);

export default documentsRouter;
