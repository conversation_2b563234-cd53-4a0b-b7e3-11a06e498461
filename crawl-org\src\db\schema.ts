import { relations } from "drizzle-orm";
import {
  boolean,
  integer,
  pgTable,
  serial,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

// Sites table (reference from server schema)
export const sites = pgTable("sites", {
  id: serial("id").primary<PERSON>ey(),
  name: varchar("name", { length: 255 }).notNull(),
  code: varchar("code", { length: 50 }).unique().notNull(),
  domains: text("domains").array().notNull(),
  status: boolean("status").notNull().default(true),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Companies table for halal companies directory
export const companies = pgTable("companies", {
  id: serial("id").primaryKey(),
  siteId: integer("site_id").notNull(),
  companyName: varchar("company_name", { length: 500 }).notNull(),
  registrationNumber: varchar("registration_number", { length: 255 }),
  businessType: varchar("business_type", { length: 255 }),
  category: varchar("category", { length: 255 }),
  subcategory: varchar("subcategory", { length: 255 }),
  address: text("address"),
  state: varchar("state", { length: 255 }),
  postcode: varchar("postcode", { length: 20 }),
  city: varchar("city", { length: 255 }),
  country: varchar("country", { length: 255 }).default("Malaysia"),
  phone: varchar("phone", { length: 50 }),
  fax: varchar("fax", { length: 50 }),
  email: varchar("email", { length: 255 }),
  website: varchar("website", { length: 500 }),
  contactPerson: varchar("contact_person", { length: 255 }),
  certificateNumber: varchar("certificate_number", { length: 255 }),
  certificateType: varchar("certificate_type", { length: 255 }),
  certificateStatus: varchar("certificate_status", { length: 100 }),
  issuedDate: varchar("issued_date", { length: 255 }),
  expiryDate: varchar("expiry_date", { length: 255 }),
  sourceUrl: varchar("source_url", { length: 1000 }),
  pageNumber: integer("page_number"),
  rawData: text("raw_data"), // Store original scraped data as JSON
  dataHash: varchar("data_hash", { length: 64 }).unique(), // For duplicate detection
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Relations
export const sitesRelations = relations(sites, ({ many }) => ({
  companies: many(companies),
}));

export const companiesRelations = relations(companies, ({ one }) => ({
  site: one(sites, {
    fields: [companies.siteId],
    references: [sites.id],
  }),
}));

// Type definitions
export type Company = typeof companies.$inferSelect;
export type NewCompany = typeof companies.$inferInsert;
export type Site = typeof sites.$inferSelect;
