'use client'

import { AlertCircle, Camera, CheckCircle, QrCode, X } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { useLanguage } from '@/lib/language-context'

interface QRScannerProps {
  onScan?: (data: string) => void
  onClose?: () => void
  isOpen?: boolean
  className?: string
}

export function QRScanner({
  onScan,
  onClose,
  isOpen = false,
  className,
}: QRScannerProps) {
  const { language } = useLanguage()
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [scannedData, setScannedData] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  const startScanning = async () => {
    try {
      setError(null)
      setIsScanning(true)

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error(
          language === 'en'
            ? 'Camera access is not supported in this browser'
            : 'Akses kamera tidak disokong dalam pelayar ini'
        )
      }

      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera if available
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      })

      streamRef.current = stream
      setHasPermission(true)

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }

      // Start QR code detection
      startQRDetection()
    } catch (err) {
      console.error('Error starting camera:', err)
      setHasPermission(false)
      setError(
        language === 'en'
          ? 'Unable to access camera. Please check permissions.'
          : 'Tidak dapat mengakses kamera. Sila semak kebenaran.'
      )
      setIsScanning(false)
    }
  }

  const stopScanning = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsScanning(false)
    setScannedData(null)
  }

  const startQRDetection = () => {
    // Note: In a real implementation, you would use a QR code detection library
    // like qr-scanner, jsQR, or @zxing/library
    // For this demo, we'll simulate QR detection

    const detectQR = () => {
      if (!isScanning || !videoRef.current) {
        return
      }

      // Simulate QR code detection
      // In real implementation, you would:
      // 1. Capture frame from video
      // 2. Process with QR detection library
      // 3. Extract QR data if found

      setTimeout(detectQR, 100) // Check every 100ms
    }

    detectQR()
  }

  useEffect(() => {
    if (isOpen) {
      startScanning()
    } else {
      stopScanning()
    }

    return () => {
      stopScanning()
    }
  }, [isOpen, startScanning, stopScanning])

  const handleManualInput = () => {
    const input = prompt(
      language === 'en'
        ? 'Enter certificate number or QR data manually:'
        : 'Masukkan nombor sijil atau data QR secara manual:'
    )

    if (input?.trim()) {
      handleScanResult(input.trim())
    }
  }

  const handleScanResult = (data: string) => {
    setScannedData(data)
    onScan?.(data)

    // Auto-close after successful scan
    setTimeout(() => {
      onClose?.()
    }, 2000)
  }

  const handleClose = () => {
    stopScanning()
    onClose?.()
  }

  if (!isOpen) {
    return null
  }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {language === 'en' ? 'Scan QR Code' : 'Imbas Kod QR'}
          </h3>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Camera View */}
          {hasPermission && !error && (
            <div className="relative mb-4">
              <video
                ref={videoRef}
                className="w-full h-64 bg-black rounded-lg object-cover"
                playsInline
                muted
              />

              {/* Scanning Overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-48 h-48 border-2 border-primary-green rounded-lg relative">
                  <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary-green" />
                  <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary-green" />
                  <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary-green" />
                  <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary-green" />

                  {/* Scanning line animation */}
                  <div className="absolute inset-x-0 top-0 h-0.5 bg-primary-green animate-pulse" />
                </div>
              </div>

              {/* Status indicator */}
              {isScanning && (
                <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                  {language === 'en' ? 'Scanning...' : 'Mengimbas...'}
                </div>
              )}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">
                  {language === 'en' ? 'Camera Error' : 'Ralat Kamera'}
                </span>
              </div>
              <p className="text-red-700 text-sm mt-1">{error}</p>
            </div>
          )}

          {/* Success State */}
          {scannedData && (
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">
                  {language === 'en' ? 'QR Code Detected' : 'Kod QR Dikesan'}
                </span>
              </div>
              <p className="text-green-700 text-sm mt-1 font-mono break-all">
                {scannedData}
              </p>
            </div>
          )}

          {/* Instructions */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              {language === 'en'
                ? 'Position the QR code within the scanning area. Make sure there is adequate lighting.'
                : 'Letakkan kod QR dalam kawasan imbasan. Pastikan terdapat pencahayaan yang mencukupi.'}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {!isScanning && !scannedData && (
              <button
                type="button"
                onClick={startScanning}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
              >
                <Camera className="w-5 h-5" />
                {language === 'en' ? 'Start Camera' : 'Mulakan Kamera'}
              </button>
            )}

            <button
              type="button"
              onClick={handleManualInput}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <QrCode className="w-5 h-5" />
              {language === 'en' ? 'Enter Manually' : 'Masukkan Secara Manual'}
            </button>

            <button
              type="button"
              onClick={handleClose}
              className="w-full px-4 py-3 text-gray-600 hover:text-gray-800 transition-colors"
            >
              {language === 'en' ? 'Cancel' : 'Batal'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// QR Scanner Button Component
interface QRScannerButtonProps {
  onScan?: (data: string) => void
  className?: string
  variant?: 'primary' | 'secondary'
}

export function QRScannerButton({
  onScan,
  className,
  variant = 'primary',
}: QRScannerButtonProps) {
  const { language } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)

  const handleScan = (data: string) => {
    onScan?.(data)
    setIsOpen(false)
  }

  const buttonClass = variant === 'primary' ? 'btn-primary' : 'btn-secondary'

  return (
    <>
      <button
        type="button"
        onClick={() => setIsOpen(true)}
        className={`${buttonClass} inline-flex items-center gap-2 ${className}`}
      >
        <QrCode className="w-4 h-4" />
        {language === 'en' ? 'Scan QR Code' : 'Imbas Kod QR'}
      </button>

      <QRScanner
        isOpen={isOpen}
        onScan={handleScan}
        onClose={() => setIsOpen(false)}
      />
    </>
  )
}
