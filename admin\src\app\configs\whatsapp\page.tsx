'use client'

import { MessageSquare, Search } from 'lucide-react'
import { useEffect, useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { useAuthStore } from '@/stores/auth'

interface WhatsAppConfig {
  id: number
  siteId: number
  accessToken: string
  phoneNumberId: string
  webhookVerifyToken: string
  businessAccountId: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function WhatsAppConfigsPage() {
  const { token } = useAuthStore()
  const [configs, setConfigs] = useState<WhatsAppConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

  useEffect(() => {
    const fetchConfigs = async () => {
      if (!token) return

      try {
        setIsLoading(true)
        const response = await fetch(
          `${API_BASE_URL}/api/admin/whatsapp-configs`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (response.ok) {
          const data = await response.json()
          setConfigs(data)
        } else {
          setError('Failed to fetch WhatsApp configurations')
        }
      } catch (error) {
        setError('Network error')
      } finally {
        setIsLoading(false)
      }
    }

    fetchConfigs()
  }, [token, API_BASE_URL])

  const filteredConfigs = configs.filter(
    config =>
      config.phoneNumberId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.businessAccountId
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      config.siteId.toString().includes(searchQuery)
  )

  const maskToken = (token: string) => {
    if (token.length <= 8) return token
    return token.substring(0, 4) + '...' + token.substring(token.length - 4)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            WhatsApp Configurations
          </h1>
          <p className="text-gray-600">
            View all WhatsApp Business API configurations
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>All WhatsApp Configurations</CardTitle>
            <CardDescription>
              WhatsApp Business API configurations across all sites
            </CardDescription>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search configurations..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  Loading WhatsApp configurations...
                </p>
              </div>
            ) : filteredConfigs.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No configurations found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery
                    ? 'No configurations match your search.'
                    : 'No WhatsApp configurations have been created yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredConfigs.map(config => (
                  <div
                    key={config.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            WhatsApp Config #{config.id}
                          </h3>
                          <span className="text-sm text-gray-500">
                            Site ID: {config.siteId}
                          </span>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              config.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {config.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <div className="mt-2 space-y-1">
                          <p className="text-sm text-gray-600">
                            <strong>Phone Number ID:</strong>{' '}
                            {config.phoneNumberId}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Business Account ID:</strong>{' '}
                            {config.businessAccountId}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Access Token:</strong>{' '}
                            {maskToken(config.accessToken)}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Webhook Verify Token:</strong>{' '}
                            {maskToken(config.webhookVerifyToken)}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            Created:{' '}
                            {new Date(config.createdAt).toLocaleDateString()}
                            {config.updatedAt !== config.createdAt && (
                              <span>
                                {' '}
                                • Updated:{' '}
                                {new Date(
                                  config.updatedAt
                                ).toLocaleDateString()}
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
