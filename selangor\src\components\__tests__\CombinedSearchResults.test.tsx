import { render, screen } from '@testing-library/react'
import type { CompanySearchResponse } from '@/types/company'
import type { ProductSearchResponse } from '@/types/product'
import type { SearchResponse } from '@/types/search'
import { CombinedSearchResults } from '../CombinedSearchResults'

// Mock data
const mockSearchResponse: SearchResponse = {
  query: 'test query',
  results: [
    {
      id: '1',
      text: 'Test result',
      score: 0.9,
      metadata: { title: 'Test Document' },
    },
  ],
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    hasMore: false,
  },
}

const mockProductResponse: ProductSearchResponse = {
  query: 'test query',
  products: [
    {
      id: '1',
      name: 'Test Product',
      company: 'Test Company',
      category: 'Test Category',
      score: 0.9,
    },
  ],
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    hasMore: false,
  },
}

const mockCompanyResponse: CompanySearchResponse = {
  query: 'test query',
  companies: [
    {
      id: '1',
      name: 'Test Company',
      address: 'Test Address',
      score: 0.9,
    },
  ],
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    hasMore: false,
  },
}

const mockSummaryResponse = {
  query: 'test query',
  summary: 'Test summary',
  chunks: [],
  totalChunks: 0,
  wordsUsed: 10,
}

describe('CombinedSearchResults', () => {
  it('shows loading indicator on web tab when isLoading is true', () => {
    render(
      <CombinedSearchResults
        searchResponse={null}
        productResponse={null}
        companyResponse={null}
        summaryResponse={null}
        isLoading={true}
        isProductLoading={false}
        isCompanyLoading={false}
        isSummaryLoading={false}
      />
    )

    // Check if the web tab shows loading indicator
    const webTab = screen.getByRole('button', { name: /Documents Results/ })
    expect(webTab).toBeInTheDocument()

    // Should show loading spinner instead of FileText icon
    const loadingSpinner = webTab.querySelector('.animate-spin')
    expect(loadingSpinner).toBeInTheDocument()
  })

  it('shows loading indicator on products tab when isProductLoading is true', () => {
    render(
      <CombinedSearchResults
        searchResponse={null}
        productResponse={null}
        companyResponse={null}
        summaryResponse={null}
        isLoading={false}
        isProductLoading={true}
        isCompanyLoading={false}
        isSummaryLoading={false}
      />
    )

    // Check if the products tab shows loading indicator
    const productsTab = screen.getByRole('button', { name: /Products/ })
    expect(productsTab).toBeInTheDocument()

    // Should show loading spinner
    const loadingSpinner = productsTab.querySelector('.animate-spin')
    expect(loadingSpinner).toBeInTheDocument()

    // Should show "Loading..." text
    expect(productsTab).toHaveTextContent('Loading...')
  })

  it('shows loading indicator on companies tab when isCompanyLoading is true', () => {
    render(
      <CombinedSearchResults
        searchResponse={null}
        productResponse={null}
        companyResponse={null}
        summaryResponse={null}
        isLoading={false}
        isProductLoading={false}
        isCompanyLoading={true}
        isSummaryLoading={false}
      />
    )

    // Check if the companies tab shows loading indicator
    const companiesTab = screen.getByRole('button', { name: /Companies/ })
    expect(companiesTab).toBeInTheDocument()

    // Should show loading spinner
    const loadingSpinner = companiesTab.querySelector('.animate-spin')
    expect(loadingSpinner).toBeInTheDocument()

    // Should show "Loading..." text
    expect(companiesTab).toHaveTextContent('Loading...')
  })

  it('shows result counts when not loading', () => {
    render(
      <CombinedSearchResults
        searchResponse={mockSearchResponse}
        productResponse={mockProductResponse}
        companyResponse={mockCompanyResponse}
        summaryResponse={mockSummaryResponse}
        isLoading={false}
        isProductLoading={false}
        isCompanyLoading={false}
        isSummaryLoading={false}
      />
    )

    // Check if tabs show result counts instead of loading
    const webTab = screen.getByRole('button', { name: /Documents Results/ })
    expect(webTab).toHaveTextContent('1') // Should show count

    const productsTab = screen.getByRole('button', { name: /Products/ })
    expect(productsTab).toHaveTextContent('1') // Should show count

    const companiesTab = screen.getByRole('button', { name: /Companies/ })
    expect(companiesTab).toHaveTextContent('1') // Should show count
  })
})
