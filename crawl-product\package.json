{"name": "crawl-product", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch index.ts", "start": "bun run index.ts", "crawl": "bun run src/crawler.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.8", "drizzle-kit": "^0.31.4"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7", "puppeteer": "^24.11.1"}}