import { ilike, or, and, count, eq, inArray } from 'drizzle-orm'
import { type NextRequest } from 'next/server'
import {
  extractAnalyticsFromRequest,
  getHalalSelangorSiteId,
  trackSearchAnalytics,
} from '@/lib/analytics'
import { db } from '@/lib/db'
import { products, categories, productCategories } from '@/lib/db/schema'
import {
  detectLanguageAndTranslate,
  type TranslationResult,
} from '@/lib/translation'
import { createSuccessResponse, handleApiError } from '@/lib/api-response'

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')
  const page = Number.parseInt(searchParams.get('page') || '1')
  const limit = Number.parseInt(searchParams.get('limit') || '10')
  const category = searchParams.get('category')

  console.log('Product search query:', query)

  // If no query provided, return all products with pagination (with optional category filter)
  if (!query || query.trim() === '') {
    try {
      // Build where condition for category filter using junction table
      const baseConditions = [eq(products.siteId, getHalalSelangorSiteId())]

      if (category) {
        // First, find the category ID by name
        const categoryRecord = await db
          .select({ id: categories.id })
          .from(categories)
          .where(eq(categories.categoryName, category))
          .limit(1)

        if (categoryRecord.length > 0) {
          const categoryId = categoryRecord[0].id

          // Get product IDs that belong to this category
          const productCategoryLinks = await db
            .select({ productId: productCategories.productId })
            .from(productCategories)
            .where(eq(productCategories.categoryId, categoryId))

          const productIdsFromCategory = productCategoryLinks.map(
            (link: any) => link.productId
          )

          if (productIdsFromCategory.length > 0) {
            baseConditions.push(inArray(products.id, productIdsFromCategory))
          } else {
            // No products found for this category, also check legacy category field
            const legacyConditions = [
              ilike(products.category, `%${category}%`),
              ilike(products.subcategory, `%${category}%`),
            ]
            const orCondition = or(...legacyConditions)
            if (orCondition) {
              baseConditions.push(orCondition)
            }
          }
        } else {
          // Category not found in categories table, fall back to legacy search
          const legacyConditions = [
            ilike(products.category, `%${category}%`),
            ilike(products.subcategory, `%${category}%`),
          ]
          const orCondition = or(...legacyConditions)
          if (orCondition) {
            baseConditions.push(orCondition)
          }
        }
      }

      const whereCondition = and(...baseConditions)

      // Get total count for pagination (with category filter if applied)
      const totalResults = await db
        .select({ count: count() })
        .from(products)
        .where(whereCondition)

      const total = totalResults[0]?.count || 0

      // Calculate pagination
      const offset = (page - 1) * limit
      const hasMore = offset + limit < total

      // Get paginated results (filtered by site_id)
      const allProducts = await db
        .select({
          id: products.id,
          productName: products.productName,
          companyName: products.companyName,
          certificateNumber: products.certificateNumber,
          certificateType: products.certificateType,
          issuedDate: products.issuedDate,
          expiryDate: products.expiryDate,
          status: products.status,
          category: products.category,
          subcategory: products.subcategory,
          address: products.address,
          state: products.state,
          country: products.country,
          contactInfo: products.contactInfo,
          website: products.website,
          sourceUrl: products.sourceUrl,
          createdAt: products.createdAt,
          updatedAt: products.updatedAt,
        })
        .from(products)
        .where(whereCondition)
        .limit(limit)
        .offset(offset)
        .orderBy(products.productName)

      // Fetch categories from junction table for all products
      const productIds = allProducts.map((p: any) => p.id)

      if (productIds.length > 0) {
        const productCategoriesData = await db
          .select({
            productId: productCategories.productId,
            categoryName: categories.categoryName,
          })
          .from(productCategories)
          .innerJoin(
            categories,
            eq(productCategories.categoryId, categories.id)
          )
          .where(inArray(productCategories.productId, productIds))

        // Group categories by product ID (ensure consistent string keys)
        const categoriesByProduct: { [key: string]: string[] } = {}
        productCategoriesData.forEach((item: any) => {
          const productIdKey = String(item.productId) // Convert to string for consistent lookup
          if (!categoriesByProduct[productIdKey]) {
            categoriesByProduct[productIdKey] = []
          }
          categoriesByProduct[productIdKey].push(item.categoryName)
        })

        // Replace category field with junction table categories
        allProducts.forEach((product: any) => {
          const productIdKey = String(product.id) // Convert to string for consistent lookup
          const junctionCategories = categoriesByProduct[productIdKey]
          if (junctionCategories && junctionCategories.length > 0) {
            // Use categories from junction table with spaces around pipe
            product.category = junctionCategories.join(' | ')
          } else {
            // No junction table categories found, set to empty
            product.category = ''
          }
        })
      }

      const response = {
        query: '',
        products: allProducts,
        pagination: {
          page,
          limit,
          total,
          hasMore,
          totalPages: Math.ceil(total / limit),
        },
      }

      console.log('Product list (no search) response:', {
        productsCount: allProducts.length,
        total,
        page,
        hasMore,
      })

      // Track analytics for listing all products
      const analyticsData = extractAnalyticsFromRequest(request)
      await trackSearchAnalytics({
        siteId: getHalalSelangorSiteId(),
        searchQuery: '',
        searchType: 'products',
        resultsCount: allProducts.length,
        hasResults: allProducts.length > 0,
        searchFilters: JSON.stringify({ page, limit, category }),
        ...analyticsData,
      })

      return createSuccessResponse(response)
    } catch (error) {
      return handleApiError(error, 'Failed to fetch products')
    }
  }

  try {
    // Detect language and generate translated terms using OpenAI
    const translationResult = await detectLanguageAndTranslate(query, ['halal'])
    const allQueries = [query, ...translationResult.translatedTerms]

    console.log('Product search queries:', {
      original: query,
      detectedLanguage: translationResult.detectedLanguage,
      confidence: translationResult.confidence,
      translatedTerms: translationResult.translatedTerms,
      allQueries,
    })

    // Perform searches for all queries and collect results
    const allResults = new Map<
      string,
      {
        id: string
        productName: string
        companyName: string
        certificateNumber: string
        certificateType: string
        issuedDate: string | null
        expiryDate: string | null
        status: string
        category: string
        subcategory: string | null
        address: string | null
        state: string
        country: string
        contactInfo: string | null
        website: string | null
        sourceUrl: string | null
        createdAt: Date
        updatedAt: Date
      }
    >() // Use Map to deduplicate by product ID

    for (const searchQuery of allQueries) {
      const searchTerm = `%${searchQuery}%`
      let searchConditions = and(
        eq(products.siteId, getHalalSelangorSiteId()),
        or(
          ilike(products.productName, searchTerm),
          ilike(products.companyName, searchTerm),
          ilike(products.category, searchTerm),
          ilike(products.subcategory, searchTerm),
          ilike(products.certificateNumber, searchTerm),
          ilike(products.certificateType, searchTerm),
          ilike(products.status, searchTerm),
          ilike(products.state, searchTerm),
          ilike(products.country, searchTerm)
        )
      )

      // Add category filter if specified using junction table
      if (category) {
        // First, find the category ID by name
        const categoryRecord = await db
          .select({ id: categories.id })
          .from(categories)
          .where(eq(categories.categoryName, category))
          .limit(1)

        if (categoryRecord.length > 0) {
          const categoryId = categoryRecord[0].id

          // Get product IDs that belong to this category
          const productCategoryLinks = await db
            .select({ productId: productCategories.productId })
            .from(productCategories)
            .where(eq(productCategories.categoryId, categoryId))

          const productIdsFromCategory = productCategoryLinks.map(
            (link: any) => link.productId
          )

          if (productIdsFromCategory.length > 0) {
            searchConditions = and(
              searchConditions,
              inArray(products.id, productIdsFromCategory)
            )
          } else {
            // No products found for this category, also check legacy category field
            searchConditions = and(
              searchConditions,
              or(
                ilike(products.category, `%${category}%`),
                ilike(products.subcategory, `%${category}%`)
              )
            )
          }
        } else {
          // Category not found in categories table, fall back to legacy search
          searchConditions = and(
            searchConditions,
            or(
              ilike(products.category, `%${category}%`),
              ilike(products.subcategory, `%${category}%`)
            )
          )
        }
      }

      // Get results for this query (without pagination to collect all matches)
      const queryResults = await db
        .select({
          id: products.id,
          productName: products.productName,
          companyName: products.companyName,
          certificateNumber: products.certificateNumber,
          certificateType: products.certificateType,
          issuedDate: products.issuedDate,
          expiryDate: products.expiryDate,
          status: products.status,
          category: products.category,
          subcategory: products.subcategory,
          address: products.address,
          state: products.state,
          country: products.country,
          contactInfo: products.contactInfo,
          website: products.website,
          sourceUrl: products.sourceUrl,
          createdAt: products.createdAt,
          updatedAt: products.updatedAt,
        })
        .from(products)
        .where(searchConditions)
        .orderBy(products.productName)

      // Add results to map (deduplicates by ID)
      for (const result of queryResults) {
        allResults.set(result.id, result)
      }
    }

    // Convert map to array and sort
    const combinedResults = Array.from(allResults.values()).sort((a, b) =>
      a.productName.localeCompare(b.productName)
    )

    // Get categories from junction tables for all products
    const productIds = combinedResults.map(product => product.id)

    if (productIds.length > 0) {
      const productCategoriesData = await db
        .select({
          productId: productCategories.productId,
          categoryName: categories.categoryName,
        })
        .from(productCategories)
        .innerJoin(categories, eq(productCategories.categoryId, categories.id))
        .where(inArray(productCategories.productId, productIds))

      // Group categories by product ID (ensure consistent string keys)
      const categoriesByProduct: { [key: string]: string[] } = {}
      productCategoriesData.forEach(item => {
        const productIdKey = String(item.productId) // Convert to string for consistent lookup
        if (!categoriesByProduct[productIdKey]) {
          categoriesByProduct[productIdKey] = []
        }
        categoriesByProduct[productIdKey].push(item.categoryName)
      })

      // Replace category field with junction table categories
      combinedResults.forEach(product => {
        const productIdKey = String(product.id) // Convert to string for consistent lookup
        const junctionCategories = categoriesByProduct[productIdKey]
        if (junctionCategories && junctionCategories.length > 0) {
          // Use categories from junction table with spaces around pipe
          product.category = junctionCategories.join(' | ')
        } else {
          // No junction table categories found, set to empty
          product.category = ''
        }
      })
    }

    // Calculate pagination on combined results
    const total = combinedResults.length
    const offset = (page - 1) * limit
    const hasMore = offset + limit < total
    const paginatedResults = combinedResults.slice(offset, offset + limit)

    const response = {
      query,
      products: paginatedResults,
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages: Math.ceil(total / limit),
      },
      ...(translationResult.translatedTerms.length > 0 && {
        translatedTerms: translationResult.translatedTerms,
        detectedLanguage: translationResult.detectedLanguage,
        confidence: translationResult.confidence,
        searchType: 'multilingual' as const,
      }),
    }

    console.log('Product search response:', {
      query,
      detectedLanguage: translationResult.detectedLanguage,
      translatedTerms: translationResult.translatedTerms,
      productsCount: paginatedResults.length,
      totalCombined: total,
      page,
      hasMore,
    })

    // Track search analytics
    const analyticsData = extractAnalyticsFromRequest(request)
    await trackSearchAnalytics({
      siteId: getHalalSelangorSiteId(),
      searchQuery: query,
      searchType: 'products',
      resultsCount: paginatedResults.length,
      hasResults: paginatedResults.length > 0,
      searchFilters: JSON.stringify({
        page,
        limit,
        category,
        detectedLanguage: translationResult.detectedLanguage,
        translatedTerms: translationResult.translatedTerms,
      }),
      ...analyticsData,
    })

    return createSuccessResponse(response)
  } catch (error) {
    return handleApiError(error, 'Failed to search products')
  }
}
