#!/bin/bash

# Script to push crawled files to Qdrant using Docker
# Usage: ./run_index_push.sh <collection_name> [qdrant_url]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if collection name is provided
if [ $# -lt 1 ]; then
    print_error "Collection name is required"
    echo ""
    echo "Usage: $0 <collection_name> [qdrant_url]"
    echo ""
    echo "Examples:"
    echo "  $0 halal_docs"
    echo "  $0 halal_docs http://localhost:6333"
    echo "  $0 my_collection http://*************:6333"
    echo ""
    echo "Default Qdrant URL: http://host.docker.internal:6333"
    exit 1
fi

COLLECTION_NAME="$1"
QDRANT_URL="${2:-http://host.docker.internal:6333}"
OUTPUT_DIR="output"

print_info "Starting Qdrant index push..."
echo ""
print_info "Collection: $COLLECTION_NAME"
print_info "Qdrant URL: $QDRANT_URL"
print_info "Output directory: $OUTPUT_DIR"
echo ""

# Check if output directory exists
if [ ! -d "$OUTPUT_DIR" ]; then
    print_error "Output directory '$OUTPUT_DIR' does not exist"
    print_info "Please run the crawler first to generate files"
    exit 1
fi

# Check if there are any files to process
FILE_COUNT=$(find "$OUTPUT_DIR" -type f \( -name "*.md" -o -name "*.pdf" -o -name "*.docx" -o -name "*.txt" \) | wc -l)
if [ "$FILE_COUNT" -eq 0 ]; then
    print_warning "No supported files found in $OUTPUT_DIR"
    print_info "Supported file types: .md, .pdf, .docx, .txt"
    exit 1
fi

print_info "Found $FILE_COUNT files to process"
echo ""

# Build the Docker image
print_info "Building Docker image..."
if ! docker build -t website-crawler . > /dev/null 2>&1; then
    print_error "Failed to build Docker image"
    exit 1
fi
print_success "Docker image built successfully"

# Run the index push
print_info "Pushing files to Qdrant collection..."
echo ""

if docker run --rm \
    -v "$(pwd)/$OUTPUT_DIR:/app/output:ro" \
    --network host \
    website-crawler \
    python push_to_qdrant.py "$COLLECTION_NAME" "$QDRANT_URL" "output"; then
    
    echo ""
    print_success "Successfully completed index push to collection '$COLLECTION_NAME'"
else
    echo ""
    print_error "Index push failed"
    exit 1
fi
