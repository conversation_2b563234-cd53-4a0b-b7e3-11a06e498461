'use client';

import { Plus, Upload } from 'lucide-react';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Link } from '@/i18n/navigation';
import { useDocumentsStore } from '@/stores/documents';
import { useCollectionsStore } from '@/stores/collections';
import { createColumns } from './columns';
import { DocumentsDataTable } from './data-table';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';

export default function DocumentsPage() {
  const { documents, isLoading, error, fetchDocuments, clearError } = useDocumentsStore();
  const { collections, fetchCollections } = useCollectionsStore();

  // Auth guard
  useAdminAuthGuard();

  useEffect(() => {
    fetchDocuments();
    fetchCollections();
  }, [fetchDocuments, fetchCollections]);

  // Create columns with refresh callback
  const columns = createColumns(() => {
    fetchDocuments(); // Refresh the documents list after deletion
  });

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={() => fetchDocuments()}>Try Again</Button>
              <Button variant="outline" onClick={clearError}>
                Clear Error
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Documents</h1>
          <p className="text-gray-600">
            Manage all documents across collections
          </p>
        </div>
        <div className="flex gap-2">
          <Link href="/admin/documents/upload">
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </Link>
          <Link href="/admin/documents/new">
            <Button variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Add Document
            </Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Documents</CardTitle>
          <CardDescription>
            A list of all documents across all collections in your system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <DocumentsDataTable columns={columns} data={documents} />
          )}
        </CardContent>
      </Card>

      {collections.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Browse documents by collection
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {collections.map((collection) => (
                <Link
                  key={collection.id}
                  href={`/admin/collections/${collection.id}`}
                  className="block p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <h3 className="font-medium text-gray-900">{collection.name}</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {collection._count?.documents || 0} documents
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Status: {collection.status}
                  </p>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
