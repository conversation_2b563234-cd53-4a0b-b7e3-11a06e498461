import eslint from "@eslint/js";
import tseslintPlugin from "@typescript-eslint/eslint-plugin";
import tseslintParser from "@typescript-eslint/parser";
import prettier from "eslint-plugin-prettier";
import prettierConfig from "eslint-config-prettier";
import reactPlugin from "eslint-plugin-react";
import reactHooksPlugin from "eslint-plugin-react-hooks";

export default [
  {
    ignores: [
      ".next",
      "test-*.js",
      "jest.setup.js",
      "jest.config.js",
      "**/**/test-*.js",
      "**/test-*.js",
      "**/*.cjs",
      "**/.output/**",
      "**/node_modules/**",
      "**/.nuxt/**",
      "**/.vercel/**",
      "**/.vite-ssg-temp/**",
      "**/dist/**",
      "**/build/**",
      "**/.next/**",
      "**/.wrangler/**",
      "**/coverage/**",
      "**/public/**",
      "**/tsconfig.node.json",
      "**/shims-vue.d.ts",
      "**/out",
      "**/prisma/client/**",
      "**/prisma-trpc-generator/**",
      "**/logs/**",
      "**/pgdata/**",
      "**/redis/**",
      "**/redpanda/**",
      "**/test-translation.js",
      "**/.history/**",
    ],
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parser: tseslintParser,
      parserOptions: {
        project: [
          "./tsconfig.json",
          "./front/tsconfig.json",
          "./admin/tsconfig.json",
          "./server/tsconfig.json"
        ],
        tsconfigRootDir: process.cwd(),
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        fetch: "readonly",
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        console: "readonly",
        setTimeout: "readonly",
        clearTimeout: "readonly",
        setInterval: "readonly",
        clearInterval: "readonly",
        setImmediate: "readonly",
        clearImmediate: "readonly",
        require: "readonly",
        module: "readonly",
      },
    },
    plugins: {
      "@typescript-eslint": tseslintPlugin,
      prettier,
      react: reactPlugin,
      "react-hooks": reactHooksPlugin,
    },
    settings: {
      react: {
        version: "detect",
      },
    },
    rules: {
      "@typescript-eslint/ban-ts-comment": "off",
      "no-debugger": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "no-console": "off",
      "max-len": [
        "error",
        {
          code: 80,
          ignoreUrls: true,
          ignoreStrings: true,
          ignoreTemplateLiterals: true,
          ignoreRegExpLiterals: true,
        },
      ],
      "@typescript-eslint/no-non-null-assertion": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "prefer-const": "error",
      "no-var": "error",
      "no-constant-condition": "off",
      "no-empty": "off",
      "prettier/prettier": "error",
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "warn",
    },
  },
  prettierConfig,
];
