import { and, eq } from 'drizzle-orm'
import type { Context } from 'hono'
import { bots } from '@/db/schema'

export const createBot = async (c: Context) => {
  const { name, slug, provider, model, temperature, isDefault, systemPrompt } =
    await c.req.json()
  const siteId = c.get('siteId')

  if (!name || !slug || !provider || !model) {
    return c.json(
      {
        success: false,
        data: null,
        error: 'Name, slug, provider, and model are required',
      },
      400,
    )
  }

  const [newBot] = await c
    .get('db')
    .insert(bots)
    .values({
      name,
      slug,
      provider,
      model,
      temperature: temperature || 0.5,
      isDefault: isDefault || false,
      systemPrompt,
      siteId: Number.parseInt(siteId),
    })
    .returning()

  return c.json({ success: true, data: newBot }, 201)
}

export const getBots = async (c: Context) => {
  const siteId = c.get('siteId')
  const allBots = await c
    .get('db')
    .select()
    .from(bots)
    .where(eq(bots.siteId, siteId))
  return c.json({ success: true, data: allBots })
}

export const getBot = async (c: Context) => {
  const id = c.req.param('id')
  const siteId = c.get('siteId')

  const botId = Number.parseInt(id)
  if (Number.isNaN(botId)) {
    return c.json({ success: false, data: null, error: 'Invalid bot ID' }, 400)
  }

  const [bot] = await c
    .get('db')
    .select()
    .from(bots)
    .where(and(eq(bots.id, botId), eq(bots.siteId, siteId)))

  if (!bot) {
    return c.json({ success: false, data: null, error: 'Bot not found' }, 404)
  }

  return c.json({ success: true, data: bot })
}

export const updateBot = async (c: Context) => {
  const id = c.req.param('id')
  const { name, slug, provider, model, temperature, isDefault, systemPrompt } =
    await c.req.json()
  const siteId = c.get('siteId')

  const botId = Number.parseInt(id)
  if (Number.isNaN(botId)) {
    return c.json({ success: false, data: null, error: 'Invalid bot ID' }, 400)
  }

  const updateData: Record<string, string | number | boolean | Date> = {
    updatedAt: new Date(),
  }
  if (name !== undefined) {
    updateData.name = name
  }
  if (slug !== undefined) {
    updateData.slug = slug
  }
  if (provider !== undefined) {
    updateData.provider = provider
  }
  if (model !== undefined) {
    updateData.model = model
  }
  if (temperature !== undefined) {
    updateData.temperature = temperature
  }
  if (isDefault !== undefined) {
    updateData.isDefault = isDefault
  }
  if (systemPrompt !== undefined) {
    updateData.systemPrompt = systemPrompt
  }

  const [updatedBot] = await c
    .get('db')
    .update(bots)
    .set(updateData)
    .where(and(eq(bots.id, botId), eq(bots.siteId, siteId)))
    .returning()

  if (!updatedBot) {
    return c.json({ success: false, data: null, error: 'Bot not found' }, 404)
  }

  return c.json({ success: true, data: updatedBot })
}

export const deleteBot = async (c: Context) => {
  const id = c.req.param('id')
  const siteId = c.get('siteId')

  const botId = Number.parseInt(id)
  if (Number.isNaN(botId)) {
    return c.json({ success: false, data: null, error: 'Invalid bot ID' }, 400)
  }

  const [deletedBot] = await c
    .get('db')
    .delete(bots)
    .where(and(eq(bots.id, botId), eq(bots.siteId, siteId)))
    .returning()

  if (!deletedBot) {
    return c.json({ success: false, data: null, error: 'Bot not found' }, 404)
  }

  return c.json({
    success: true,
    data: { message: 'Bot deleted successfully' },
  })
}
