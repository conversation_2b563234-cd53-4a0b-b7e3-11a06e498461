#!/usr/bin/env python3
"""
Script to push crawled files to Qdrant collection
Usage: python push_to_qdrant.py <collection_name> [qdrant_url] [output_dir]
"""

import sys
import logging
from qdrant_manager import push_output_files_to_qdrant

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main function to push files to Qdrant"""
    
    # Parse command line arguments
    if len(sys.argv) < 2:
        print("Usage: python push_to_qdrant.py <collection_name> [qdrant_url] [output_dir]")
        print("Example: python push_to_qdrant.py halal_docs http://host.docker.internal:6333 output")
        sys.exit(1)
    
    collection_name = sys.argv[1]
    qdrant_url = sys.argv[2] if len(sys.argv) > 2 else "http://host.docker.internal:6333"
    output_dir = sys.argv[3] if len(sys.argv) > 3 else "output"
    
    logger.info(f"Starting push to Qdrant collection: {collection_name}")
    logger.info(f"Qdrant URL: {qdrant_url}")
    logger.info(f"Output directory: {output_dir}")
    
    try:
        # Push files to Qdrant
        stats = push_output_files_to_qdrant(
            collection_name=collection_name,
            output_dir=output_dir,
            qdrant_url=qdrant_url
        )
        
        # Print results
        print("\n=== PUSH RESULTS ===")
        print(f"Collection: {stats.get('collection_name', 'N/A')}")
        print(f"Total files found: {stats.get('total_files_found', 0)}")
        print(f"Files processed: {stats.get('files_processed', 0)}")
        print(f"Files skipped (unchanged): {stats.get('files_skipped', 0)}")
        print(f"Files updated: {stats.get('files_updated', 0)}")
        print(f"Errors: {stats.get('errors', 0)}")
        print(f"Start time: {stats.get('start_time', 'N/A')}")
        print(f"End time: {stats.get('end_time', 'N/A')}")
        
        if stats.get('errors', 0) > 0:
            print(f"\n⚠️  {stats['errors']} errors occurred during processing")
            sys.exit(1)
        else:
            print(f"\n✅ Successfully pushed {stats.get('files_updated', 0)} files to collection '{collection_name}'")
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"Failed to push files to Qdrant: {e}")
        print(f"\n❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
