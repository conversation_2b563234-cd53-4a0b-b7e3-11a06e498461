import express, { type Request, type Response } from 'express'
import agentAuthService from '../services/agentAuth'
import databaseService from '../services/database'
import type {
  AgentLoginRequest,
  AgentLoginResponse,
  AgentUser,
  ErrorResponse,
} from '../types'

const router: express.Router = express.Router()

// Agent login
router.post(
  '/login',
  async (req: Request, res: Response<AgentLoginResponse>) => {
    try {
      const credentials: AgentLoginRequest = req.body

      if (!credentials.username || !credentials.password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required',
        })
      }

      const result = await agentAuthService.login(credentials)

      if (!result.success) {
        return res.status(401).json(result)
      }

      res.json(result)
    } catch (error) {
      console.error('Agent login error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      })
    }
  },
)

// Agent logout
router.post(
  '/logout',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (req.agent) {
        await agentAuthService.logout(req.agent.agentId)
      }

      res.json({ success: true })
    } catch (error) {
      console.error('Agent logout error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      })
    }
  },
)

// Get agent profile
router.get(
  '/profile',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response<AgentUser | ErrorResponse>) => {
    try {
      if (!req.agent) {
        return res.status(401).json({
          error: 'Authentication required',
        })
      }

      const agent = await agentAuthService.getAgentProfile(req.agent.agentId)

      if (!agent) {
        return res.status(404).json({
          error: 'Agent not found',
        })
      }

      res.json(agent)
    } catch (error) {
      console.error('Get agent profile error:', error)
      res.status(500).json({
        error: 'Internal server error',
      })
    }
  },
)

// Update agent online status
router.post(
  '/status',
  agentAuthService.authenticateAgent,
  async (req: Request, res: Response) => {
    try {
      if (!req.agent) {
        return res.status(401).json({
          error: 'Authentication required',
        })
      }

      const { isOnline } = req.body

      if (typeof isOnline !== 'boolean') {
        return res.status(400).json({
          error: 'isOnline must be a boolean',
        })
      }

      await agentAuthService.updateOnlineStatus(req.agent.agentId, isOnline)

      res.json({ success: true })
    } catch (error) {
      console.error('Update agent status error:', error)
      res.status(500).json({
        error: 'Internal server error',
      })
    }
  },
)

// Get all agents (for supervisors and admins)
router.get(
  '/list',
  agentAuthService.authenticateAgent,
  agentAuthService.requireRole(['supervisor']),
  async (_req: Request, res: Response<AgentUser[] | ErrorResponse>) => {
    try {
      const agents = await databaseService.getAllAgents()
      res.json(agents)
    } catch (error) {
      console.error('Get agents list error:', error)
      res.status(500).json({
        error: 'Internal server error',
      })
    }
  },
)

// Verify token (for frontend to check if token is still valid)
router.get(
  '/verify',
  agentAuthService.authenticateAgent,
  (req: Request, res: Response) => {
    res.json({
      success: true,
      agent: req.agent,
    })
  },
)

export default router
