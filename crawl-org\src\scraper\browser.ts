import puppeteer, { type Browser, type Page } from "puppeteer";
import type { ScrapingConfig } from "../types";

export class BrowserManager {
  private browser: Browser | null = null;
  private config: ScrapingConfig;

  constructor(config: ScrapingConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    try {
      // Try to use system Chrome first, fallback to Puppeteer's Chrome
      const chromeExecutables = [
        "/usr/bin/google-chrome-stable",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
      ];

      let executablePath: string | undefined;

      // Check if system Chrome exists
      for (const path of chromeExecutables) {
        try {
          const fs = await import("fs");
          if (fs.existsSync(path)) {
            executablePath = path;
            console.log(`✅ Using system Chrome: ${path}`);
            break;
          }
        } catch (error) {
          // Continue to next path
        }
      }

      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        executablePath, // Use system Chrome if available
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
          "--disable-audio-output",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-features=TranslateUI",
          "--disable-ipc-flooding-protection",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
          "--disable-extensions",
          "--disable-default-apps",
          "--disable-sync",
          "--no-default-browser-check",
          "--no-first-run",
          "--disable-plugins",
          "--disable-translate",
          "--disable-background-networking",
          "--disable-background-timer-throttling",
          "--disable-client-side-phishing-detection",
          "--disable-component-update",
          "--disable-default-apps",
          "--disable-domain-reliability",
          "--disable-features=AudioServiceOutOfProcess",
          "--disable-hang-monitor",
          "--disable-ipc-flooding-protection",
          "--disable-popup-blocking",
          "--disable-prompt-on-repost",
          "--disable-renderer-backgrounding",
          "--disable-sync",
          "--force-color-profile=srgb",
          "--metrics-recording-only",
          "--no-crash-upload",
          "--no-default-browser-check",
          "--no-first-run",
          "--password-store=basic",
          "--use-mock-keychain",
          "--disable-component-extensions-with-background-pages",
          "--disable-extensions",
          "--mute-audio",
        ],
      });

      if (this.config.debug) {
        console.log("✅ Browser initialized successfully");
      }
    } catch (error) {
      console.error("❌ Failed to initialize browser:", error);
      throw error;
    }
  }

  async createPage(): Promise<Page> {
    if (!this.browser) {
      throw new Error("Browser not initialized. Call initialize() first.");
    }

    const page = await this.browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });

    // Set user agent
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    );

    // Set timeout
    page.setDefaultTimeout(this.config.timeoutMs);
    page.setDefaultNavigationTimeout(this.config.timeoutMs);

    // Block unnecessary resources to speed up scraping
    await page.setRequestInterception(true);
    page.on("request", (req) => {
      const resourceType = req.resourceType();
      if (["image", "stylesheet", "font", "media"].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    return page;
  }

  async closePage(page: Page): Promise<void> {
    try {
      await page.close();
    } catch (error) {
      console.error("Error closing page:", error);
    }
  }

  async close(): Promise<void> {
    if (this.browser) {
      try {
        await this.browser.close();
        this.browser = null;
        if (this.config.debug) {
          console.log("✅ Browser closed successfully");
        }
      } catch (error) {
        console.error("❌ Error closing browser:", error);
      }
    }
  }

  isInitialized(): boolean {
    return this.browser !== null;
  }
}
