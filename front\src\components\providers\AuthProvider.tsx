'use client'

import { useEffect } from 'react'
import { useTokenRefresh } from '@/hooks/useAuthGuard'

interface AuthProviderProps {
  children: React.ReactNode
}

/**
 * AuthProvider component that initializes authentication state
 * and handles automatic token refresh
 */
export function AuthProvider({ children }: AuthProviderProps) {
  // Initialize token refresh mechanism
  useTokenRefresh()

  return <>{children}</>
}

export default AuthProvider
