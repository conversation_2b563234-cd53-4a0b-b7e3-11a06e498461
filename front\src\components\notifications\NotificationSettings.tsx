'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Volume2, VolumeX } from 'lucide-react'
import { useState } from 'react'
import { useNotifications } from '@/lib/notifications'

interface NotificationSettingsProps {
  onClose?: () => void
}

export function NotificationSettings({ onClose }: NotificationSettingsProps) {
  const {
    soundEnabled,
    browserNotificationsEnabled,
    setSoundEnabled,
    setBrowserNotificationsEnabled,
    requestBrowserPermission,
  } = useNotifications()

  const [isRequestingPermission, setIsRequestingPermission] = useState(false)

  const handleBrowserNotificationToggle = async () => {
    if (!browserNotificationsEnabled) {
      setIsRequestingPermission(true)
      try {
        const granted = await requestBrowserPermission()
        if (!granted) {
          alert(
            'Browser notifications permission was denied. You can enable it in your browser settings.'
          )
        }
      } finally {
        setIsRequestingPermission(false)
      }
    } else {
      setBrowserNotificationsEnabled(false)
    }
  }

  const testNotification = () => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Test Notification', {
        body: 'This is a test notification from the Halal Chat System',
        icon: '/favicon.ico',
      })
    }
  }

  const testSound = () => {
    try {
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.type = 'sine'

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.5
      )

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.5)
    } catch (error) {
      console.warn('Could not play test sound:', error)
      alert('Could not play test sound. Please check your browser settings.')
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 w-full max-w-md">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Notification Settings
        </h3>
        {onClose && (
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Sound Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {soundEnabled ? (
              <Volume2 className="h-5 w-5 text-blue-500" />
            ) : (
              <VolumeX className="h-5 w-5 text-gray-400" />
            )}
            <div>
              <p className="text-sm font-medium text-gray-900">Sound Alerts</p>
              <p className="text-xs text-gray-500">
                Play sound for urgent notifications
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={testSound}
              disabled={!soundEnabled}
              className="text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400"
            >
              Test
            </button>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={soundEnabled}
                onChange={e => setSoundEnabled(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600" />
            </label>
          </div>
        </div>

        {/* Browser Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {browserNotificationsEnabled ? (
              <Bell className="h-5 w-5 text-blue-500" />
            ) : (
              <BellOff className="h-5 w-5 text-gray-400" />
            )}
            <div>
              <p className="text-sm font-medium text-gray-900">
                Browser Notifications
              </p>
              <p className="text-xs text-gray-500">
                Show desktop notifications
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={testNotification}
              disabled={!browserNotificationsEnabled}
              className="text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400"
            >
              Test
            </button>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={browserNotificationsEnabled}
                onChange={handleBrowserNotificationToggle}
                disabled={isRequestingPermission}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50" />
            </label>
          </div>
        </div>

        {/* Browser Permission Status */}
        {typeof window !== 'undefined' && 'Notification' in window && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">
              Browser Permission Status
            </h4>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Status:</span>
              <span
                className={`text-sm font-medium ${
                  Notification.permission === 'granted'
                    ? 'text-green-600'
                    : Notification.permission === 'denied'
                      ? 'text-red-600'
                      : 'text-yellow-600'
                }`}
              >
                {Notification.permission === 'granted'
                  ? 'Granted'
                  : Notification.permission === 'denied'
                    ? 'Denied'
                    : 'Not Requested'}
              </span>
            </div>
            {Notification.permission === 'denied' && (
              <p className="text-xs text-gray-500 mt-2">
                To enable notifications, please allow them in your browser
                settings and refresh the page.
              </p>
            )}
          </div>
        )}

        {/* Notification Types */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Notification Types
          </h4>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center justify-between">
              <span>Handover Requests</span>
              <span className="text-orange-600 font-medium">Urgent</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Agent Status Changes</span>
              <span className="text-blue-600 font-medium">Info</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Session Completions</span>
              <span className="text-green-600 font-medium">Success</span>
            </div>
            <div className="flex items-center justify-between">
              <span>System Alerts</span>
              <span className="text-red-600 font-medium">Error</span>
            </div>
          </div>
        </div>

        {/* Help Text */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Tips</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Urgent notifications will not auto-dismiss</li>
            <li>• Sound alerts only play for urgent notifications</li>
            <li>
              • Browser notifications work even when the tab is not active
            </li>
            <li>• You can click on notifications to view details</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

// Standalone settings page component
export function NotificationSettingsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Notification Settings
          </h1>
          <p className="mt-2 text-gray-600">
            Customize how you receive notifications from the chat system
          </p>
        </div>

        <div className="flex justify-center">
          <NotificationSettings />
        </div>
      </div>
    </div>
  )
}
