'use client'

export const runtime = 'edge'

import { PageWrapper } from '@/components/page-wrapper'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'

export default function ProcedurePage() {
  const { language } = useLanguage()

  const breadcrumbs = [
    {
      label: 'Procedure',
      labelBM: 'Prosedur',
    },
  ]

  const steps = [
    {
      number: 1,
      title:
        language === 'en' ? 'Application Submission' : 'Penyerahan Permohonan',
      description:
        language === 'en'
          ? 'Submit your application through the MYeHALAL system with all required documents'
          : 'Serahkan permohonan anda melalui sistem MYeHALAL dengan semua dokumen yang diperlukan',
    },
    {
      number: 2,
      title: language === 'en' ? 'Document Review' : 'Semakan Dokumen',
      description:
        language === 'en'
          ? 'Our team will review your application and supporting documents'
          : 'Pasukan kami akan menyemak permohonan dan dokumen sokongan anda',
    },
    {
      number: 3,
      title: language === 'en' ? 'Site Inspection' : '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description:
        language === 'en'
          ? 'On-site inspection of your facilities and processes'
          : 'Pemeriksaan di tapak kemudahan dan proses anda',
    },
    {
      number: 4,
      title:
        language === 'en' ? 'Evaluation & Decision' : 'Penilaian & Keputusan',
      description:
        language === 'en'
          ? 'Final evaluation and certification decision'
          : 'Penilaian akhir dan keputusan pensijilan',
    },
    {
      number: 5,
      title: language === 'en' ? 'Certificate Issuance' : 'Pengeluaran Sijil',
      description:
        language === 'en'
          ? 'Halal certificate issued upon successful completion'
          : 'Sijil Halal dikeluarkan setelah berjaya diselesaikan',
    },
  ]

  return (
    <PageWrapper
      title="Certification Procedure"
      titleBM="Prosedur Pensijilan"
      description="Step-by-step guide to obtaining Halal certification from JAKIM."
      descriptionBM="Panduan langkah demi langkah untuk mendapatkan pensijilan Halal daripada JAKIM."
      breadcrumbs={breadcrumbs}
    >
      <div className="mb-12">
        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Certification Process' : 'Proses Pensijilan'}
          </h3>
          <div className="space-y-8">
            {steps.map((step, index) => (
              <div key={step.number} className="flex gap-6">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary-green text-white rounded-full flex items-center justify-center font-bold text-lg">
                    {step.number}
                  </div>
                  {index < steps.length - 1 && (
                    <div className="w-0.5 h-16 bg-gray-300 mx-auto mt-4" />
                  )}
                </div>
                <div className="flex-1 pb-8">
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h4>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <div className="card">
          <h3 className="text-xl font-bold mb-4 text-primary-green">
            {language === 'en' ? 'Required Documents' : 'Dokumen Diperlukan'}
          </h3>
          <ul className="space-y-3">
            <li className="flex items-start gap-3">
              <span className="text-primary-green">•</span>
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Company registration documents'
                  : 'Dokumen pendaftaran syarikat'}
              </span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary-green">•</span>
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Product specifications and ingredients list'
                  : 'Spesifikasi produk dan senarai ramuan'}
              </span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary-green">•</span>
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Manufacturing process flow chart'
                  : 'Carta alir proses pembuatan'}
              </span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary-green">•</span>
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Supplier certificates and declarations'
                  : 'Sijil dan pengisytiharan pembekal'}
              </span>
            </li>
            <li className="flex items-start gap-3">
              <span className="text-primary-green">•</span>
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Halal management system documentation'
                  : 'Dokumentasi sistem pengurusan Halal'}
              </span>
            </li>
          </ul>
        </div>

        <div className="card">
          <h3 className="text-xl font-bold mb-4 text-primary-green">
            {language === 'en'
              ? 'Processing Timeline'
              : 'Garis Masa Pemprosesan'}
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">
                {language === 'en'
                  ? 'Application Review'
                  : 'Semakan Permohonan'}
              </span>
              <span className="font-semibold text-primary-green">
                {language === 'en' ? '2-4 weeks' : '2-4 minggu'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">
                {language === 'en' ? 'Site Inspection' : 'Pemeriksaan Tapak'}
              </span>
              <span className="font-semibold text-primary-green">
                {language === 'en' ? '1-2 weeks' : '1-2 minggu'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200">
              <span className="text-gray-600">
                {language === 'en' ? 'Final Evaluation' : 'Penilaian Akhir'}
              </span>
              <span className="font-semibold text-primary-green">
                {language === 'en' ? '2-3 weeks' : '2-3 minggu'}
              </span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-600 font-semibold">
                {language === 'en' ? 'Total Duration' : 'Jumlah Tempoh'}
              </span>
              <span className="font-bold text-primary-green text-lg">
                {language === 'en' ? '5-9 weeks' : '5-9 minggu'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div className="card text-center">
          <h3 className="text-lg font-semibold mb-3 text-primary-green">
            {language === 'en' ? 'Application Process' : 'Proses Permohonan'}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {language === 'en'
              ? 'Detailed step-by-step guide to the application process'
              : 'Panduan langkah demi langkah terperinci untuk proses permohonan'}
          </p>
          <Link
            href="/procedure/application"
            className="inline-block px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
          >
            {language === 'en' ? 'Learn More' : 'Ketahui Lebih Lanjut'}
          </Link>
        </div>
        <div className="card text-center">
          <h3 className="text-lg font-semibold mb-3 text-primary-green">
            {language === 'en' ? 'Requirements' : 'Keperluan'}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {language === 'en'
              ? 'Complete list of requirements and documentation needed'
              : 'Senarai lengkap keperluan dan dokumentasi yang diperlukan'}
          </p>
          <Link
            href="/procedure/requirements"
            className="inline-block px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
          >
            {language === 'en' ? 'View Requirements' : 'Lihat Keperluan'}
          </Link>
        </div>
        <div className="card text-center">
          <h3 className="text-lg font-semibold mb-3 text-primary-green">
            {language === 'en' ? 'Guidelines' : 'Garis Panduan'}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {language === 'en'
              ? 'Best practices and guidelines for successful certification'
              : 'Amalan terbaik dan garis panduan untuk pensijilan yang berjaya'}
          </p>
          <Link
            href="/procedure/guidelines"
            className="inline-block px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
          >
            {language === 'en' ? 'Read Guidelines' : 'Baca Garis Panduan'}
          </Link>
        </div>
      </div>

      <div className="card">
        <h3 className="text-2xl font-bold mb-6 text-gray-900">
          {language === 'en' ? 'Important Notes' : 'Nota Penting'}
        </h3>
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400 text-xl">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                {language === 'en'
                  ? 'Processing times may vary depending on the complexity of your application and the completeness of submitted documents.'
                  : 'Masa pemprosesan mungkin berbeza bergantung pada kerumitan permohonan anda dan kelengkapan dokumen yang diserahkan.'}
              </p>
            </div>
          </div>
        </div>
        <ul className="space-y-3 text-gray-600">
          <li className="flex items-start gap-3">
            <span className="text-primary-green">•</span>
            <span>
              {language === 'en'
                ? 'All documents must be in Bahasa Malaysia or English'
                : 'Semua dokumen mestilah dalam Bahasa Malaysia atau Bahasa Inggeris'}
            </span>
          </li>
          <li className="flex items-start gap-3">
            <span className="text-primary-green">•</span>
            <span>
              {language === 'en'
                ? 'Incomplete applications will be returned for completion'
                : 'Permohonan yang tidak lengkap akan dikembalikan untuk dilengkapkan'}
            </span>
          </li>
          <li className="flex items-start gap-3">
            <span className="text-primary-green">•</span>
            <span>
              {language === 'en'
                ? 'Site inspection must be scheduled in advance'
                : 'Pemeriksaan tapak mesti dijadualkan terlebih dahulu'}
            </span>
          </li>
          <li className="flex items-start gap-3">
            <span className="text-primary-green">•</span>
            <span>
              {language === 'en'
                ? 'Certificate validity period is typically 2 years'
                : 'Tempoh kesahan sijil biasanya 2 tahun'}
            </span>
          </li>
        </ul>
      </div>
    </PageWrapper>
  )
}
