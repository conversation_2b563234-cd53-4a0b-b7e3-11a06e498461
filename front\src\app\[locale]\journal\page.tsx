'use client'

export const runtime = 'edge'

import { BookOpen, Calendar, Download, ExternalLink } from 'lucide-react'
import type { BreadcrumbItem } from '@/components/breadcrumb'
import { PageWrapper } from '@/components/page-wrapper'
import { useLanguage } from '@/lib/language-context'

export default function JournalPage() {
  const { language } = useLanguage()

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : '<PERSON><PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Journal' : 'Jurnal',
      href: '/journal',
    },
  ]

  const journals = [
    {
      id: 'JH-2024-01',
      title: 'Malaysian Journal of Halal Research',
      titleBM: 'Jurnal Penyelidikan Halal Malaysia',
      volume: 'Volume 7, Issue 1',
      volumeBM: 'Jilid 7, Isu 1',
      date: '2024-03-01',
      issn: '2180-4087',
      description:
        'Latest research on Halal food science, certification processes, and industry developments',
      descriptionBM:
        'Penyelidikan terkini mengenai sains makanan <PERSON>, proses pen<PERSON>, dan perkembangan industri',
      coverImage: '/images/journals/halal-research-2024-1.jpg',
      pdfUrl: '/documents/journals/JH-2024-01.pdf',
      articles: 8,
      pages: 120,
      featured: true,
    },
    {
      id: 'JH-2023-04',
      title: 'Malaysian Journal of Halal Research',
      titleBM: 'Jurnal Penyelidikan Halal Malaysia',
      volume: 'Volume 6, Issue 4',
      volumeBM: 'Jilid 6, Isu 4',
      date: '2023-12-01',
      issn: '2180-4087',
      description:
        'Special issue on international Halal standards and certification harmonization',
      descriptionBM:
        'Isu khas mengenai piawaian Halal antarabangsa dan penyelarasan pensijilan',
      coverImage: '/images/journals/halal-research-2023-4.jpg',
      pdfUrl: '/documents/journals/JH-2023-04.pdf',
      articles: 10,
      pages: 150,
      featured: true,
    },
    {
      id: 'JH-2023-03',
      title: 'Malaysian Journal of Halal Research',
      titleBM: 'Jurnal Penyelidikan Halal Malaysia',
      volume: 'Volume 6, Issue 3',
      volumeBM: 'Jilid 6, Isu 3',
      date: '2023-09-01',
      issn: '2180-4087',
      description:
        'Research on Halal pharmaceuticals and cosmetics certification',
      descriptionBM:
        'Penyelidikan mengenai pensijilan farmaseutikal dan kosmetik Halal',
      coverImage: '/images/journals/halal-research-2023-3.jpg',
      pdfUrl: '/documents/journals/JH-2023-03.pdf',
      articles: 7,
      pages: 95,
      featured: false,
    },
    {
      id: 'JH-2023-02',
      title: 'Malaysian Journal of Halal Research',
      titleBM: 'Jurnal Penyelidikan Halal Malaysia',
      volume: 'Volume 6, Issue 2',
      volumeBM: 'Jilid 6, Isu 2',
      date: '2023-06-01',
      issn: '2180-4087',
      description: 'Advances in Halal food technology and processing methods',
      descriptionBM:
        'Kemajuan dalam teknologi makanan Halal dan kaedah pemprosesan',
      coverImage: '/images/journals/halal-research-2023-2.jpg',
      pdfUrl: '/documents/journals/JH-2023-02.pdf',
      articles: 9,
      pages: 130,
      featured: false,
    },
  ]

  const researchAreas = [
    {
      title: 'Food Science & Technology',
      titleBM: 'Sains & Teknologi Makanan',
      description:
        'Research on Halal food processing, preservation, and quality assurance',
      descriptionBM:
        'Penyelidikan mengenai pemprosesan makanan Halal, pengawetan, dan jaminan kualiti',
      icon: '🔬',
    },
    {
      title: 'Certification Standards',
      titleBM: 'Piawaian Pensijilan',
      description:
        'Development and improvement of Halal certification standards and procedures',
      descriptionBM:
        'Pembangunan dan penambahbaikan piawaian dan prosedur pensijilan Halal',
      icon: '📋',
    },
    {
      title: 'Supply Chain Management',
      titleBM: 'Pengurusan Rantaian Bekalan',
      description: 'Halal supply chain integrity and traceability systems',
      descriptionBM:
        'Integriti rantaian bekalan Halal dan sistem kebolehsurihan',
      icon: '🔗',
    },
    {
      title: 'International Trade',
      titleBM: 'Perdagangan Antarabangsa',
      description: 'Global Halal market analysis and trade facilitation',
      descriptionBM: 'Analisis pasaran Halal global dan pemudahan perdagangan',
      icon: '🌍',
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(language === 'en' ? 'en-US' : 'ms-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <PageWrapper
      title="Journal"
      titleBM="Jurnal"
      description="Academic journals and publications on Halal research, certification standards, and industry developments."
      descriptionBM="Jurnal akademik dan penerbitan mengenai penyelidikan Halal, piawaian pensijilan, dan perkembangan industri."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Academic Publications'
              : 'Penerbitan Akademik'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'Explore our collection of academic journals and research publications covering various aspects of Halal science, certification standards, and industry developments. These peer-reviewed publications contribute to the advancement of knowledge in the Halal field and support evidence-based decision making.'
              : 'Terokai koleksi jurnal akademik dan penerbitan penyelidikan kami yang merangkumi pelbagai aspek sains Halal, piawaian pensijilan, dan perkembangan industri. Penerbitan yang disemak oleh rakan sebaya ini menyumbang kepada kemajuan pengetahuan dalam bidang Halal dan menyokong pembuatan keputusan berasaskan bukti.'}
          </p>
        </div>

        {/* Research Areas */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Research Areas' : 'Bidang Penyelidikan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {researchAreas.map((area, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 rounded-lg hover:border-primary-green transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{area.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'bm' ? area.titleBM : area.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {language === 'bm'
                        ? area.descriptionBM
                        : area.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Latest Issues */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Latest Issues' : 'Isu Terkini'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {journals
              .filter(journal => journal.featured)
              .map(journal => (
                <div
                  key={journal.id}
                  className="border border-gray-200 rounded-lg overflow-hidden hover:border-primary-green hover:shadow-lg transition-all"
                >
                  <div className="aspect-[3/4] bg-gradient-to-br from-primary-green to-green-600 flex items-center justify-center">
                    <div className="text-center text-white p-6">
                      <BookOpen className="w-16 h-16 mx-auto mb-4" />
                      <h4 className="font-bold text-lg mb-2">
                        {language === 'bm' ? journal.titleBM : journal.title}
                      </h4>
                      <p className="text-sm opacity-90">
                        {language === 'bm' ? journal.volumeBM : journal.volume}
                      </p>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-green text-white">
                        {language === 'en' ? 'Latest' : 'Terkini'}
                      </span>
                      <span className="text-xs text-gray-500">
                        ISSN: {journal.issn}
                      </span>
                    </div>
                    <h5 className="font-semibold text-gray-900 mb-2">
                      {language === 'bm' ? journal.volumeBM : journal.volume}
                    </h5>
                    <p className="text-gray-600 text-sm mb-4">
                      {language === 'bm'
                        ? journal.descriptionBM
                        : journal.description}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(journal.date)}</span>
                        </div>
                        <span>
                          {journal.articles}{' '}
                          {language === 'en' ? 'articles' : 'artikel'}
                        </span>
                        <span>
                          {journal.pages}{' '}
                          {language === 'en' ? 'pages' : 'muka surat'}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <a
                        href={journal.pdfUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-primary-green text-white text-sm rounded-lg hover:bg-primary-green-dark transition-colors"
                      >
                        <Download className="w-4 h-4" />
                        {language === 'en' ? 'Download PDF' : 'Muat Turun PDF'}
                      </a>
                      <button className="px-4 py-2 border border-primary-green text-primary-green text-sm rounded-lg hover:bg-primary-green hover:text-white transition-colors">
                        {language === 'en' ? 'View Details' : 'Lihat Butiran'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* All Issues */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'All Issues' : 'Semua Isu'}
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Issue ID' : 'ID Isu'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Volume/Issue' : 'Jilid/Isu'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Description' : 'Penerangan'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Date' : 'Tarikh'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Articles' : 'Artikel'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Action' : 'Tindakan'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {journals.map(journal => (
                  <tr
                    key={journal.id}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="py-3 px-4">
                      <span className="font-mono text-sm text-primary-green">
                        {journal.id}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {language === 'bm'
                            ? journal.volumeBM
                            : journal.volume}
                        </div>
                        <div className="text-xs text-gray-500">
                          ISSN: {journal.issn}
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <p className="text-sm text-gray-600 max-w-xs">
                        {language === 'bm'
                          ? journal.descriptionBM
                          : journal.description}
                      </p>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">
                      {formatDate(journal.date)}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">
                      {journal.articles}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <a
                          href={journal.pdfUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-1 text-primary-green hover:text-primary-green-dark text-sm"
                        >
                          <Download className="w-4 h-4" />
                          {language === 'en' ? 'PDF' : 'PDF'}
                        </a>
                        <button className="inline-flex items-center gap-1 text-gray-600 hover:text-gray-800 text-sm">
                          <ExternalLink className="w-4 h-4" />
                          {language === 'en' ? 'Details' : 'Butiran'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Submission Guidelines */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Submission Guidelines'
              : 'Garis Panduan Penyerahan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'For Authors' : 'Untuk Pengarang'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Original research articles'
                    : 'Artikel penyelidikan asal'}
                </li>
                <li>
                  • {language === 'en' ? 'Review papers' : 'Kertas ulasan'}
                </li>
                <li>• {language === 'en' ? 'Case studies' : 'Kajian kes'}</li>
                <li>
                  • {language === 'en' ? 'Technical notes' : 'Nota teknikal'}
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'Submission Process' : 'Proses Penyerahan'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Peer review process'
                    : 'Proses semakan rakan sebaya'}
                </li>
                <li>
                  •{' '}
                  {language === 'en' ? 'Editorial review' : 'Semakan editorial'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Publication timeline: 3-6 months'
                    : 'Garis masa penerbitan: 3-6 bulan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Open access publication'
                    : 'Penerbitan akses terbuka'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Editorial Contact' : 'Hubungan Editorial'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'For manuscript submissions, editorial inquiries, or subscription information, please contact our editorial team.'
              : 'Untuk penyerahan manuskrip, pertanyaan editorial, atau maklumat langganan, sila hubungi pasukan editorial kami.'}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Editorial Office' : 'Pejabat Editorial'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Email:' : 'E-mel:'}
                  </span>{' '}
                  <EMAIL>
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  03-8892 5000
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Publication Schedule'
                  : 'Jadual Penerbitan'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  {language === 'en'
                    ? 'Quarterly publication'
                    : 'Penerbitan suku tahunan'}
                </p>
                <p>
                  {language === 'en'
                    ? 'March, June, September, December'
                    : 'Mac, Jun, September, Disember'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
