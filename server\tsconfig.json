{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "drizzle", "**/*.test.ts", "**/*.spec.ts", "src/routes/**/*"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}