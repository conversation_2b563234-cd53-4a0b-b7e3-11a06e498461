{"name": "halal-companies-crawler", "version": "1.0.0", "description": "Bun.js web scraper for Malaysian halal companies directory", "main": "src/index.ts", "type": "module", "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "crawl": "bun run src/index.ts crawl -t 50000", "crawl:pages": "bun run src/index.ts --pages", "test": "bun test", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "keywords": ["web-scraping", "puppeteer", "halal", "malaysia", "bun", "typescript", "drizzle"], "author": "", "license": "MIT", "dependencies": {"chalk": "^5.3.0", "cli-progress": "^3.12.0", "commander": "^12.0.0", "dotenv": "^16.6.1", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7", "puppeteer": "^22.0.0"}, "devDependencies": {"@types/cli-progress": "^3.11.5", "@types/node": "^20.0.0", "drizzle-kit": "^0.31.2", "typescript": "^5.8.3"}}