'use client'

import { Award, Building, FileText, Loader2, Search, X } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { searchPlaceholders } from '@/data/content'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'
import { QRScannerButton } from './qr-scanner'

interface SearchResult {
  id: string
  type: 'company' | 'product' | 'certificate'
  title: string
  subtitle: string
  status: 'valid' | 'expired' | 'suspended' | 'revoked'
  certificateNumber?: string
  expiryDate?: string
}

interface SearchWidgetProps {
  className?: string
  onSearch?: (query: string) => void
  onResultSelect?: (result: SearchResult) => void
  placeholder?: string
  showRecentSearches?: boolean
}

export function SearchWidget({
  className,
  onSearch,
  onResultSelect,
  placeholder,
  showRecentSearches = true,
}: SearchWidgetProps) {
  const { language } = useLanguage()
  const [query, setQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const searchPlaceholder =
    placeholder ||
    (language === 'bm' ? searchPlaceholders.bm : searchPlaceholders.en)

  // Load recent searches from localStorage
  useEffect(() => {
    if (showRecentSearches) {
      const saved = localStorage.getItem('halal-recent-searches')
      if (saved) {
        try {
          setRecentSearches(JSON.parse(saved))
        } catch (error) {
          console.error('Failed to parse recent searches:', error)
        }
      }
    }
  }, [showRecentSearches])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Real API search function
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)

    try {
      // Use the real API
      const response = await fetch(
        `/api/search?q=${encodeURIComponent(searchQuery)}&limit=5`
      )
      const data = await response.json()

      if (data.success && data.data) {
        setResults(data.data)
      } else {
        setResults([])
        console.error('Search failed:', data.error)
      }
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = async (searchQuery: string = query) => {
    if (!searchQuery.trim()) {
      return
    }

    // Add to recent searches
    if (showRecentSearches) {
      const updated = [
        searchQuery,
        ...recentSearches.filter(s => s !== searchQuery),
      ].slice(0, 5)
      setRecentSearches(updated)
      localStorage.setItem('halal-recent-searches', JSON.stringify(updated))
    }

    await performSearch(searchQuery)
    onSearch?.(searchQuery)
    setIsOpen(true)
  }

  const handleInputChange = (value: string) => {
    setQuery(value)
    if (value.trim()) {
      performSearch(value)
      setIsOpen(true)
    } else {
      setResults([])
      setIsOpen(false)
    }
  }

  const handleResultClick = (result: SearchResult) => {
    onResultSelect?.(result)
    setIsOpen(false)
    setQuery('')
  }

  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery)
    handleSearch(recentQuery)
  }

  const handleQRScan = (data: string) => {
    setQuery(data)
    handleSearch(data)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    inputRef.current?.focus()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'text-green-600 bg-green-100'
      case 'expired':
        return 'text-red-600 bg-red-100'
      case 'suspended':
        return 'text-yellow-600 bg-yellow-100'
      case 'revoked':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusText = (status: string) => {
    const statusMap = {
      valid: { en: 'Valid', bm: 'Sah' },
      expired: { en: 'Expired', bm: 'Tamat Tempoh' },
      suspended: { en: 'Suspended', bm: 'Digantung' },
      revoked: { en: 'Revoked', bm: 'Dibatalkan' },
    }
    return statusMap[status as keyof typeof statusMap]?.[language] || status
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'company':
        return <Building className="w-4 h-4" />
      case 'product':
        return <FileText className="w-4 h-4" />
      case 'certificate':
        return <Award className="w-4 h-4" />
      default:
        return <Search className="w-4 h-4" />
    }
  }

  return (
    <div ref={searchRef} className={cn('relative w-full max-w-3xl', className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
          <Search className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={e => handleInputChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              handleSearch()
            } else if (e.key === 'Escape') {
              setIsOpen(false)
            }
          }}
          placeholder={searchPlaceholder}
          className="block w-full pl-10 sm:pl-12 pr-10 sm:pr-12 py-3 sm:py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent text-base sm:text-lg bg-white text-gray-800"
        />
        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 sm:pr-4 flex items-center"
          >
            <X className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Search and QR Buttons */}
      <div className="mt-4 flex flex-col sm:flex-row gap-3">
        <button
          type="button"
          onClick={() => handleSearch()}
          disabled={!query.trim() || isLoading}
          className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px]"
        >
          {isLoading ? (
            <div className="flex items-center justify-center gap-2">
              <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
              <span className="text-sm sm:text-base">
                {language === 'en' ? 'Searching...' : 'Mencari...'}
              </span>
            </div>
          ) : (
            <span className="text-sm sm:text-base">
              {language === 'en' ? 'Search' : 'Cari'}
            </span>
          )}
        </button>
        <QRScannerButton
          onScan={handleQRScan}
          variant="secondary"
          className="px-4 py-3 sm:px-6 sm:py-4 min-h-[44px] w-full sm:w-auto"
        />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          {/* Recent Searches */}
          {showRecentSearches && recentSearches.length > 0 && !query && (
            <div className="p-4 border-b border-gray-100">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {language === 'en' ? 'Recent Searches' : 'Carian Terkini'}
              </h4>
              <div className="space-y-1">
                {recentSearches.map((recentQuery, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleRecentSearchClick(recentQuery)}
                    className="block w-full text-left px-2 py-1 text-sm text-gray-600 hover:bg-gray-50 rounded"
                  >
                    {recentQuery}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          {results.length > 0 && (
            <div className="p-2">
              {results.map(result => (
                <button
                  key={result.id}
                  type="button"
                  onClick={() => handleResultClick(result)}
                  className="w-full p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1 text-gray-400">
                      {getTypeIcon(result.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 truncate">
                          {result.title}
                        </h4>
                        <span
                          className={cn(
                            'px-2 py-1 text-xs font-medium rounded-full',
                            getStatusColor(result.status)
                          )}
                        >
                          {getStatusText(result.status)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 truncate">
                        {result.subtitle}
                      </p>
                      {result.certificateNumber && (
                        <p className="text-xs text-gray-500 mt-1">
                          {result.certificateNumber}
                          {result.expiryDate && (
                            <span className="ml-2">
                              {language === 'en' ? 'Expires:' : 'Tamat:'}{' '}
                              {result.expiryDate}
                            </span>
                          )}
                        </p>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* No Results */}
          {query && results.length === 0 && !isLoading && (
            <div className="p-4 text-center text-gray-500">
              {language === 'en'
                ? 'No results found'
                : 'Tiada keputusan ditemui'}
            </div>
          )}

          {/* Loading */}
          {isLoading && (
            <div className="p-4 text-center">
              <Loader2 className="w-6 h-6 animate-spin mx-auto text-primary-green" />
            </div>
          )}
        </div>
      )}
    </div>
  )
}
