'use client'

export const runtime = 'edge'

import type { BreadcrumbItem } from '@/components/breadcrumb'
import { PageWrapper } from '@/components/page-wrapper'
import { useLanguage } from '@/lib/language-context'

export default function GuidelinesPage() {
  const { language } = useLanguage()

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Procedure' : 'Prosedur',
      href: '/procedure',
    },
    {
      label: language === 'en' ? 'Guidelines' : 'Garis Panduan',
      href: '/procedure/guidelines',
    },
  ]

  return (
    <PageWrapper
      title="Guidelines"
      titleBM="Garis Panduan"
      description="Comprehensive guidelines for Halal certification including best practices, standards, and compliance procedures."
      descriptionBM="Garis panduan komprehensif untuk pensijilan Halal termasuk amalan terbaik, piawaian, dan prosedur pema<PERSON>."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Halal Certification Guidelines'
              : 'Garis Panduan Pensijilan Halal'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'These guidelines provide comprehensive information on Halal certification standards, best practices, and compliance procedures. They are designed to help applicants understand and meet the requirements for obtaining and maintaining Halal certification from JAKIM.'
              : 'Garis panduan ini menyediakan maklumat komprehensif mengenai piawaian pensijilan Halal, amalan terbaik, dan prosedur pematuhan. Ia direka untuk membantu pemohon memahami dan memenuhi keperluan untuk mendapatkan dan mengekalkan pensijilan Halal daripada JAKIM.'}
          </p>
        </div>

        {/* Malaysian Standards */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Malaysian Halal Standards'
              : 'Piawaian Halal Malaysia'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-primary-green mb-3">
                MS 1500:2019
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {language === 'en'
                  ? 'General guidelines on Halal food - Production, preparation, handling and storage'
                  : 'Garis panduan am mengenai makanan Halal - Pengeluaran, penyediaan, pengendalian dan penyimpanan'}
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Food production requirements'
                    : 'Keperluan pengeluaran makanan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Hygiene and sanitation'
                    : 'Kebersihan dan sanitasi'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Storage and handling'
                    : 'Penyimpanan dan pengendalian'}
                </li>
              </ul>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-primary-green mb-3">
                MS 2200-1:2012
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {language === 'en'
                  ? 'Islamic consumer goods - Part 1: Cosmetics and personal care'
                  : 'Barangan pengguna Islam - Bahagian 1: Kosmetik dan penjagaan diri'}
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Cosmetic ingredients'
                    : 'Ramuan kosmetik'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Manufacturing processes'
                    : 'Proses pembuatan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Packaging requirements'
                    : 'Keperluan pembungkusan'}
                </li>
              </ul>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-primary-green mb-3">
                MS 2424:2012
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {language === 'en'
                  ? 'Halal pharmaceuticals - General guidelines'
                  : 'Farmaseutikal Halal - Garis panduan am'}
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Pharmaceutical ingredients'
                    : 'Ramuan farmaseutikal'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Manufacturing standards'
                    : 'Piawaian pembuatan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en' ? 'Quality assurance' : 'Jaminan kualiti'}
                </li>
              </ul>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-primary-green mb-3">
                MS 2565:2014
              </h4>
              <p className="text-sm text-gray-600 mb-3">
                {language === 'en'
                  ? 'Halal food service - Management system requirements'
                  : 'Perkhidmatan makanan Halal - Keperluan sistem pengurusan'}
              </p>
              <ul className="text-xs text-gray-500 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Service management'
                    : 'Pengurusan perkhidmatan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en' ? 'Staff training' : 'Latihan kakitangan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Customer service'
                    : 'Perkhidmatan pelanggan'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Best Practices */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Best Practices' : 'Amalan Terbaik'}
          </h3>
          <div className="space-y-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'Ingredient Management'
                  : 'Pengurusan Ramuan'}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h5 className="font-medium text-green-800 mb-2">
                    {language === 'en' ? 'Do' : 'Lakukan'}
                  </h5>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Verify all ingredient certificates'
                        : 'Sahkan semua sijil ramuan'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Maintain updated supplier database'
                        : 'Kekalkan pangkalan data pembekal terkini'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Implement traceability system'
                        : 'Laksanakan sistem kebolehsurihan'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Regular supplier audits'
                        : 'Audit pembekal berkala'}
                    </li>
                  </ul>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h5 className="font-medium text-red-800 mb-2">
                    {language === 'en' ? "Don't" : 'Jangan'}
                  </h5>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Use uncertified ingredients'
                        : 'Gunakan ramuan tidak disijilkan'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Mix Halal and non-Halal ingredients'
                        : 'Campur ramuan Halal dan bukan Halal'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Accept expired certificates'
                        : 'Terima sijil yang tamat tempoh'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Ignore supplier changes'
                        : 'Abaikan perubahan pembekal'}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'Production Management'
                  : 'Pengurusan Pengeluaran'}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-2">
                    {language === 'en' ? 'Segregation' : 'Pengasingan'}
                  </h5>
                  <p className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Maintain clear separation between Halal and non-Halal production lines'
                      : 'Kekalkan pengasingan yang jelas antara barisan pengeluaran Halal dan bukan Halal'}
                  </p>
                </div>
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-2">
                    {language === 'en' ? 'Cleaning' : 'Pembersihan'}
                  </h5>
                  <p className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Implement thorough cleaning procedures between production runs'
                      : 'Laksanakan prosedur pembersihan menyeluruh antara pengeluaran'}
                  </p>
                </div>
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-2">
                    {language === 'en' ? 'Documentation' : 'Dokumentasi'}
                  </h5>
                  <p className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Maintain detailed records of all production activities'
                      : 'Kekalkan rekod terperinci semua aktiviti pengeluaran'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance Procedures */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Compliance Procedures' : 'Prosedur Pematuhan'}
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Internal Audit System'
                    : 'Sistem Audit Dalaman'}
                </h4>
                <p className="text-gray-600 text-sm mb-2">
                  {language === 'en'
                    ? 'Establish regular internal audits to ensure ongoing compliance with Halal requirements.'
                    : 'Wujudkan audit dalaman berkala untuk memastikan pematuhan berterusan dengan keperluan Halal.'}
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Monthly facility inspections'
                      : 'Pemeriksaan kemudahan bulanan'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Quarterly documentation review'
                      : 'Semakan dokumentasi suku tahunan'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Annual comprehensive audit'
                      : 'Audit komprehensif tahunan'}
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Staff Training Program'
                    : 'Program Latihan Kakitangan'}
                </h4>
                <p className="text-gray-600 text-sm mb-2">
                  {language === 'en'
                    ? 'Implement comprehensive training programs for all staff involved in Halal operations.'
                    : 'Laksanakan program latihan komprehensif untuk semua kakitangan yang terlibat dalam operasi Halal.'}
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Initial Halal awareness training'
                      : 'Latihan kesedaran Halal awal'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Role-specific training modules'
                      : 'Modul latihan khusus peranan'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Regular refresher courses'
                      : 'Kursus penyegaran berkala'}
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Corrective Action System'
                    : 'Sistem Tindakan Pembetulan'}
                </h4>
                <p className="text-gray-600 text-sm mb-2">
                  {language === 'en'
                    ? 'Establish procedures for identifying, documenting, and correcting non-conformities.'
                    : 'Wujudkan prosedur untuk mengenal pasti, mendokumentasi, dan membetulkan ketidakakuran.'}
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Non-conformity reporting system'
                      : 'Sistem pelaporan ketidakakuran'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Root cause analysis procedures'
                      : 'Prosedur analisis punca masalah'}
                  </li>
                  <li>
                    •{' '}
                    {language === 'en'
                      ? 'Corrective action implementation'
                      : 'Pelaksanaan tindakan pembetulan'}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Common Challenges */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Common Challenges & Solutions'
              : 'Cabaran Biasa & Penyelesaian'}
          </h3>
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Challenge: Ingredient Verification'
                  : 'Cabaran: Pengesahan Ramuan'}
              </h4>
              <p className="text-gray-600 text-sm mb-2">
                {language === 'en'
                  ? 'Difficulty in obtaining and verifying Halal status of all ingredients.'
                  : 'Kesukaran mendapatkan dan mengesahkan status Halal semua ramuan.'}
              </p>
              <div className="bg-green-50 p-3 rounded">
                <p className="text-green-800 text-sm">
                  <strong>
                    {language === 'en' ? 'Solution:' : 'Penyelesaian:'}
                  </strong>{' '}
                  {language === 'en'
                    ? 'Establish direct relationships with certified suppliers and maintain a comprehensive database of approved ingredients.'
                    : 'Wujudkan hubungan langsung dengan pembekal yang disijilkan dan kekalkan pangkalan data komprehensif ramuan yang diluluskan.'}
                </p>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Challenge: Cross-Contamination Prevention'
                  : 'Cabaran: Pencegahan Pencemaran Silang'}
              </h4>
              <p className="text-gray-600 text-sm mb-2">
                {language === 'en'
                  ? 'Preventing cross-contamination in shared production facilities.'
                  : 'Mencegah pencemaran silang dalam kemudahan pengeluaran berkongsi.'}
              </p>
              <div className="bg-green-50 p-3 rounded">
                <p className="text-green-800 text-sm">
                  <strong>
                    {language === 'en' ? 'Solution:' : 'Penyelesaian:'}
                  </strong>{' '}
                  {language === 'en'
                    ? 'Implement strict cleaning protocols, dedicated equipment, and time-based segregation of production runs.'
                    : 'Laksanakan protokol pembersihan ketat, peralatan khusus, dan pengasingan berasaskan masa untuk pengeluaran.'}
                </p>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Challenge: Documentation Management'
                  : 'Cabaran: Pengurusan Dokumentasi'}
              </h4>
              <p className="text-gray-600 text-sm mb-2">
                {language === 'en'
                  ? 'Maintaining comprehensive and up-to-date documentation systems.'
                  : 'Mengekalkan sistem dokumentasi yang komprehensif dan terkini.'}
              </p>
              <div className="bg-green-50 p-3 rounded">
                <p className="text-green-800 text-sm">
                  <strong>
                    {language === 'en' ? 'Solution:' : 'Penyelesaian:'}
                  </strong>{' '}
                  {language === 'en'
                    ? 'Implement digital document management systems with automated reminders for certificate renewals and regular reviews.'
                    : 'Laksanakan sistem pengurusan dokumen digital dengan peringatan automatik untuk pembaharuan sijil dan semakan berkala.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Resources */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Additional Resources' : 'Sumber Tambahan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Training Programs' : 'Program Latihan'}
              </h4>
              <p className="text-gray-600 text-sm mb-2">
                {language === 'en'
                  ? 'JAKIM offers various training programs for industry players.'
                  : 'JAKIM menawarkan pelbagai program latihan untuk pemain industri.'}
              </p>
              <p className="text-sm text-primary-green">
                {language === 'en'
                  ? 'Contact us for training schedules'
                  : 'Hubungi kami untuk jadual latihan'}
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Technical Support' : 'Sokongan Teknikal'}
              </h4>
              <p className="text-gray-600 text-sm mb-2">
                {language === 'en'
                  ? 'Get technical assistance for specific certification challenges.'
                  : 'Dapatkan bantuan teknikal untuk cabaran pensijilan khusus.'}
              </p>
              <p className="text-sm text-primary-green">
                <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
