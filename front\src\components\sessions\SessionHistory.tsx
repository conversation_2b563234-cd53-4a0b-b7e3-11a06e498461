'use client'

import {
  Calendar,
  Clock,
  Download,
  Eye,
  Filter,
  MessageSquare,
  Search,
  Star,
  User,
  Users,
} from 'lucide-react'
import { useState } from 'react'

interface Message {
  id: string
  role: 'user' | 'assistant' | 'agent'
  content: string
  timestamp: string
  metadata?: {
    platform?: string
    attachments?: string[]
    reactions?: string[]
  }
}

interface AgentNote {
  id: string
  agentId: number
  agentName: string
  content: string
  timestamp: string
  type: 'note' | 'escalation' | 'resolution'
}

interface CustomerFeedback {
  id: string
  rating: number // 1-5
  comment?: string
  timestamp: string
  categories: string[]
}

interface SessionData {
  id: string
  userId?: string
  customerName?: string
  customerEmail?: string
  platform: 'web' | 'whatsapp' | 'facebook'
  status: 'active' | 'completed' | 'abandoned' | 'escalated'
  isHandedOver: boolean
  agentId?: number
  agentName?: string
  createdAt: string
  endedAt?: string
  duration?: number // in minutes
  messageCount: number
  messages: Message[]
  agentNotes: AgentNote[]
  customerFeedback?: CustomerFeedback
  tags: string[]
  priority: 'low' | 'normal' | 'high' | 'urgent'
  resolution?: {
    type: 'resolved' | 'escalated' | 'transferred'
    summary: string
    timestamp: string
  }
}

interface SessionHistoryProps {
  sessions: SessionData[]
  onSessionSelect?: (session: SessionData) => void
  onExport?: (sessions: SessionData[]) => void
}

export function SessionHistory({
  sessions,
  onSessionSelect,
  onExport,
}: SessionHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [platformFilter, setPlatformFilter] = useState<string>('all')
  const [dateRange, setDateRange] = useState({ from: '', to: '' })
  const [selectedSession, setSelectedSession] = useState<SessionData | null>(
    null
  )

  const filteredSessions = sessions.filter(session => {
    const matchesSearch =
      searchTerm === '' ||
      session.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.agentName?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus =
      statusFilter === 'all' || session.status === statusFilter
    const matchesPlatform =
      platformFilter === 'all' || session.platform === platformFilter

    const matchesDateRange =
      (!dateRange.from ||
        new Date(session.createdAt) >= new Date(dateRange.from)) &&
      (!dateRange.to || new Date(session.createdAt) <= new Date(dateRange.to))

    return matchesSearch && matchesStatus && matchesPlatform && matchesDateRange
  })

  const handleSessionClick = (session: SessionData) => {
    setSelectedSession(session)
    onSessionSelect?.(session)
  }

  const handleExport = () => {
    onExport?.(filteredSessions)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'abandoned':
        return 'bg-gray-100 text-gray-800'
      case 'escalated':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'whatsapp':
        return '📱'
      case 'facebook':
        return '💬'
      default:
        return '🌐'
    }
  }

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'N/A'
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}h ${remainingMinutes}m`
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search sessions by customer, agent, or session ID..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-3">
            <select
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="abandoned">Abandoned</option>
              <option value="escalated">Escalated</option>
            </select>

            <select
              value={platformFilter}
              onChange={e => setPlatformFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Platforms</option>
              <option value="web">Web</option>
              <option value="whatsapp">WhatsApp</option>
              <option value="facebook">Facebook</option>
            </select>

            <input
              type="date"
              value={dateRange.from}
              onChange={e =>
                setDateRange(prev => ({ ...prev, from: e.target.value }))
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="From"
            />

            <input
              type="date"
              value={dateRange.to}
              onChange={e =>
                setDateRange(prev => ({ ...prev, to: e.target.value }))
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="To"
            />

            <button
              type="button"
              onClick={handleExport}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>
            Showing {filteredSessions.length} of {sessions.length} sessions
          </span>
          <div className="flex items-center space-x-4">
            <span>
              Avg Duration:{' '}
              {formatDuration(
                filteredSessions.reduce(
                  (sum, s) => sum + (s.duration || 0),
                  0
                ) / filteredSessions.length
              )}
            </span>
            <span>
              Completion Rate:{' '}
              {Math.round(
                (filteredSessions.filter(s => s.status === 'completed').length /
                  filteredSessions.length) *
                  100
              )}
              %
            </span>
          </div>
        </div>
      </div>

      {/* Sessions List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Session History
          </h3>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredSessions.length === 0 ? (
            <div className="p-8 text-center">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No sessions found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria.
              </p>
            </div>
          ) : (
            filteredSessions.map(session => (
              <div
                key={session.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleSessionClick(session)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-lg">
                          {getPlatformIcon(session.platform)}
                        </span>
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.customerName || 'Anonymous User'}
                        </p>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}
                        >
                          {session.status}
                        </span>
                        {session.priority !== 'normal' && (
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                              session.priority === 'urgent'
                                ? 'bg-red-100 text-red-800'
                                : session.priority === 'high'
                                  ? 'bg-orange-100 text-orange-800'
                                  : 'bg-yellow-100 text-yellow-800'
                            }`}
                          >
                            {session.priority}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <p className="text-sm text-gray-500">
                          Session {session.id.slice(-8)}
                        </p>
                        {session.customerEmail && (
                          <p className="text-sm text-gray-500">
                            {session.customerEmail}
                          </p>
                        )}
                        {session.agentName && (
                          <p className="text-sm text-gray-500">
                            Agent: {session.agentName}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MessageSquare className="h-4 w-4" />
                      <span>{session.messageCount}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatDuration(session.duration)}</span>
                    </div>
                    {session.customerFeedback && (
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400" />
                        <span>{session.customerFeedback.rating}/5</span>
                      </div>
                    )}
                    <div className="text-xs">
                      {new Date(session.createdAt).toLocaleDateString()}
                    </div>
                    <Eye className="h-4 w-4 text-gray-400" />
                  </div>
                </div>

                {session.tags.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {session.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
