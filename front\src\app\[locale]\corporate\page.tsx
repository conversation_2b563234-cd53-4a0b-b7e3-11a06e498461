'use client'

export const runtime = 'edge'

import { PageWrapper } from '@/components/page-wrapper'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'

export default function CorporatePage() {
  const { language } = useLanguage()

  const breadcrumbs = [
    {
      label: 'Corporate',
      labelBM: 'Korporat',
    },
  ]

  return (
    <PageWrapper
      title="Corporate"
      titleBM="Korporat"
      description="Learn about JAKIM's Halal Management Division and our mission to ensure Halal integrity in Malaysia."
      descriptionBM="Ketahui tentang Bahagian Pengurusan Halal JAKIM dan misi kami untuk memastikan integriti Halal di Malaysia."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div className="card">
          <h3 className="text-xl font-semibold mb-4 text-primary-green">
            {language === 'en' ? 'About Us' : 'Tentang Kami'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'Learn about the Halal Management Division of JAKIM and our role in ensuring Halal integrity.'
              : 'Ketahui tentang Bahagian Pengurusan Halal JAKIM dan peranan kami dalam memastikan integriti Halal.'}
          </p>
          <Link
            href="/corporate/about"
            className="text-primary-green hover:text-primary-green-dark font-medium"
          >
            {language === 'en' ? 'Read More →' : 'Baca Lagi →'}
          </Link>
        </div>

        <div className="card">
          <h3 className="text-xl font-semibold mb-4 text-primary-green">
            {language === 'en' ? 'Vision & Mission' : 'Visi & Misi'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'Our vision and mission in promoting and maintaining Halal standards in Malaysia.'
              : 'Visi dan misi kami dalam mempromosi dan mengekalkan piawaian Halal di Malaysia.'}
          </p>
          <Link
            href="/corporate/vision-mission"
            className="text-primary-green hover:text-primary-green-dark font-medium"
          >
            {language === 'en' ? 'Read More →' : 'Baca Lagi →'}
          </Link>
        </div>

        <div className="card">
          <h3 className="text-xl font-semibold mb-4 text-primary-green">
            {language === 'en' ? 'Organization Chart' : 'Carta Organisasi'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'View our organizational structure and key personnel in the Halal Management Division.'
              : 'Lihat struktur organisasi kami dan kakitangan utama dalam Bahagian Pengurusan Halal.'}
          </p>
          <Link
            href="/corporate/organization"
            className="text-primary-green hover:text-primary-green-dark font-medium"
          >
            {language === 'en' ? 'View Chart →' : 'Lihat Carta →'}
          </Link>
        </div>
      </div>

      <div className="mt-12">
        <div className="card">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Our Commitment' : 'Komitmen Kami'}
          </h3>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-600 leading-relaxed">
              {language === 'en'
                ? 'The Halal Management Division of JAKIM is committed to ensuring the highest standards of Halal certification and compliance. We work tirelessly to maintain the integrity of Halal products and services, providing confidence to Muslim consumers both locally and internationally.'
                : 'Bahagian Pengurusan Halal JAKIM komited untuk memastikan piawaian tertinggi pensijilan dan pematuhan Halal. Kami bekerja tanpa lelah untuk mengekalkan integriti produk dan perkhidmatan Halal, memberikan keyakinan kepada pengguna Muslim tempatan dan antarabangsa.'}
            </p>
            <p className="text-gray-600 leading-relaxed mt-4">
              {language === 'en'
                ? 'Through continuous improvement and innovation, we strive to be the leading authority in Halal certification, setting benchmarks for excellence in the global Halal industry.'
                : 'Melalui penambahbaikan berterusan dan inovasi, kami berusaha untuk menjadi pihak berkuasa terkemuka dalam pensijilan Halal, menetapkan penanda aras kecemerlangan dalam industri Halal global.'}
            </p>
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
