'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Edit, MoreHorizontal, Trash2, Download, Copy, ExternalLink } from 'lucide-react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Link } from '@/i18n/navigation';
import type { Document } from '@/types/document';
import { useDocumentsStore } from '@/stores/documents';
import { getFileTypeIcon, formatFileSize } from '@/types/document';

function DocumentActionsCell({
  document,
  onDelete,
}: {
  document: Document;
  onDelete?: () => void;
}) {
  const params = useParams();
  const locale = params.locale as string;
  const { deleteDocument } = useDocumentsStore();

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${document.filename}"? This action cannot be undone.`)) {
      const success = await deleteDocument(document.id);
      if (success && onDelete) {
        onDelete();
      }
    }
  };

  const handleDownload = () => {
    // Create download URL - this would typically be a signed URL from S3
    const downloadUrl = `/api/documents/${document.id}/download`;
    window.open(downloadUrl, '_blank');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(document.id.toString())}
        >
          <Copy className="mr-2 h-4 w-4" />
          Copy ID
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(document.s3Key)}
        >
          <Copy className="mr-2 h-4 w-4" />
          Copy S3 Key
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" />
          Download File
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${locale}/admin/documents/${document.id}`}>
            <ExternalLink className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${locale}/admin/documents/${document.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Document
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Document
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createColumns = (onDelete?: () => void): ColumnDef<Document>[] => [
  {
    accessorKey: 'filename',
    header: 'File',
    cell: ({ row }) => {
      const document = row.original;
      const icon = getFileTypeIcon(document.mimetype || '');
      return (
        <div className="flex items-center space-x-2">
          <span className="text-lg">{icon}</span>
          <div>
            <p className="font-medium text-gray-900">{document.filename}</p>
            {document.filesize && (
              <p className="text-sm text-gray-500">
                {formatFileSize(document.filesize)}
              </p>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'collectionName',
    header: 'Collection',
    cell: ({ row }) => {
      const document = row.original;
      const params = useParams();
      const locale = params.locale as string;
      
      return document.collectionName ? (
        <Link 
          href={`/${locale}/admin/collections/${document.collectionId}`}
          className="text-blue-600 hover:text-blue-800"
        >
          {document.collectionName}
        </Link>
      ) : (
        <span className="text-gray-500">Unknown</span>
      );
    },
  },
  {
    accessorKey: 'mimetype',
    header: 'Type',
    cell: ({ row }) => {
      const mimetype = row.getValue('mimetype') as string;
      if (!mimetype) return <span className="text-gray-500">Unknown</span>;
      
      // Extract file extension from mimetype
      const typeMap: Record<string, string> = {
        'application/pdf': 'PDF',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
        'text/plain': 'TXT',
        'text/markdown': 'MD',
        'text/asciidoc': 'ASCIIDOC',
        'audio/mpeg': 'MP3',
        'audio/wav': 'WAV',
        'text/csv': 'CSV',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
      };
      
      return (
        <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
          {typeMap[mimetype] || mimetype.split('/')[1]?.toUpperCase() || 'Unknown'}
        </span>
      );
    },
  },
  {
    accessorKey: 's3ConfigurationServiceName',
    header: 'Storage',
    cell: ({ row }) => {
      const serviceName = row.getValue('s3ConfigurationServiceName') as string;
      return serviceName ? (
        <span className="text-gray-600">{serviceName}</span>
      ) : (
        <span className="text-gray-500">Unknown</span>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Uploaded',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return (
        <div>
          <p className="text-sm text-gray-900">{date.toLocaleDateString()}</p>
          <p className="text-xs text-gray-500">{date.toLocaleTimeString()}</p>
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end space-x-2">
        <Link href={`/admin/documents/${row.original.id}/edit`}>
          <Button variant="outline" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Edit className="h-4 w-4" />
          </Button>
        </Link>
        <DocumentActionsCell document={row.original} onDelete={onDelete} />
      </div>
    ),
  },
];
