# Use official Playwright Python image with latest Linux and pre-installed browsers
# https://playwright.dev/python/docs/docker
FROM mcr.microsoft.com/playwright/python:v1.53.0-noble

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies (playwright is already installed in the base image)
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir aiohttp aiofiles beautifulsoup4 lxml requests python-dotenv && \
    pip install --no-cache-dir crawl4ai && \
    pip install --no-cache-dir qdrant-client sentence-transformers PyPDF2 python-docx

# Copy the crawler script and qdrant manager
COPY crawl_website.py .
COPY qdrant_manager.py .
COPY push_to_qdrant.py .
COPY test_qdrant_local.py .

# Create output directory
RUN mkdir -p /app/output

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Create entrypoint script (no need for Xvfb as Playwright image handles display)
RUN echo '#!/bin/bash\n\
# Check if first argument is "python" - if so, run directly\n\
if [ "$1" = "python" ]; then\n\
    exec "$@"\n\
else\n\
    # Run the crawler with provided arguments\n\
    python crawl_website.py "$@"\n\
fi\n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command
CMD ["https://www.myehalal.halal.gov.my"]
