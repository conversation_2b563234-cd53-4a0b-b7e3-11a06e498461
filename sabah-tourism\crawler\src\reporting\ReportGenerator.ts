import fs from "fs";
import path from "path";
import type { DatabaseManager } from "../crawlers/base/DatabaseManager.js";
import type { CrawlSession, MediaFile, SocialPost } from "../types/database.js";
import { reportLogger } from "../utils/logger.js";

export interface ReportOptions {
  platform?: string;
  keywords?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
  authors?: string[];
  minLikes?: number;
  minViews?: number;
  includeMedia?: boolean;
  format?: "json" | "csv" | "html";
  outputPath?: string;
}

export interface ReportData {
  summary: {
    totalPosts: number;
    totalAuthors: <AUTHORS>
    totalMedia: number;
    totalEngagement: {
      likes: number;
      comments: number;
      shares: number;
      views: number;
    };
    dateRange: {
      earliest: Date | null;
      latest: Date | null;
    };
    platforms: Record<string, number>;
    topKeywords: Array<{ keyword: string; count: number }>;
    topAuthors: <AUTHORS>
  };
  posts: Array<SocialPost & { mediaFiles?: MediaFile[] }>;
  sessions: CrawlSession[];
  generatedAt: Date;
}

export class ReportGenerator {
  constructor(private database: DatabaseManager) {}

  public async generateReport(
    options: ReportOptions = {},
  ): Promise<ReportData> {
    reportLogger.info("Generating report", { options });

    try {
      const posts = await this.getFilteredPosts(options);
      const sessions = await this.getRelevantSessions(options);
      const summary = await this.generateSummary(posts, options);

      // Add media files if requested
      if (options.includeMedia) {
        for (const post of posts) {
          (post as any).mediaFiles = this.database.getMediaFiles(post.id!);
        }
      }

      const reportData: ReportData = {
        summary,
        posts,
        sessions,
        generatedAt: new Date(),
      };

      reportLogger.info("Report generated successfully", {
        totalPosts: posts.length,
        totalSessions: sessions.length,
        format: options.format,
      });

      return reportData;
    } catch (error) {
      reportLogger.error("Failed to generate report", { error, options });
      throw error;
    }
  }

  public async exportReport(
    reportData: ReportData,
    options: ReportOptions,
  ): Promise<string> {
    const format = options.format || "json";
    const outputPath = options.outputPath || this.getDefaultOutputPath(format);

    try {
      switch (format) {
        case "json":
          await this.exportAsJson(reportData, outputPath);
          break;
        case "csv":
          await this.exportAsCsv(reportData, outputPath);
          break;
        case "html":
          await this.exportAsHtml(reportData, outputPath);
          break;
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      reportLogger.info("Report exported successfully", { outputPath, format });
      return outputPath;
    } catch (error) {
      reportLogger.error("Failed to export report", {
        error,
        outputPath,
        format,
      });
      throw error;
    }
  }

  private async getFilteredPosts(
    options: ReportOptions,
  ): Promise<SocialPost[]> {
    // Build SQL query based on filters
    let query = "SELECT * FROM social_posts WHERE 1=1";
    const params: any[] = [];

    if (options.platform) {
      query += " AND platform = ?";
      params.push(options.platform);
    }

    if (options.authors && options.authors.length > 0) {
      const placeholders = options.authors.map(() => "?").join(",");
      query += ` AND author_username IN (${placeholders})`;
      params.push(...options.authors);
    }

    if (options.minLikes) {
      query += " AND likes_count >= ?";
      params.push(options.minLikes);
    }

    if (options.minViews) {
      query += " AND views_count >= ?";
      params.push(options.minViews);
    }

    if (options.dateRange) {
      query += " AND crawled_at BETWEEN ? AND ?";
      params.push(
        options.dateRange.from.toISOString(),
        options.dateRange.to.toISOString(),
      );
    }

    query += " ORDER BY crawled_at DESC";

    const stmt = this.database["db"].prepare(query);
    return stmt.all(...params) as SocialPost[];
  }

  private async getRelevantSessions(
    options: ReportOptions,
  ): Promise<CrawlSession[]> {
    let query = "SELECT * FROM crawl_sessions WHERE 1=1";
    const params: any[] = [];

    if (options.platform) {
      query += " AND platform = ?";
      params.push(options.platform);
    }

    if (options.keywords && options.keywords.length > 0) {
      // Check if any of the keywords are in the session keywords JSON
      const keywordConditions = options.keywords
        .map(() => "keywords LIKE ?")
        .join(" OR ");
      query += ` AND (${keywordConditions})`;
      params.push(...options.keywords.map((k) => `%"${k}"%`));
    }

    query += " ORDER BY started_at DESC";

    const stmt = this.database["db"].prepare(query);
    return stmt.all(...params) as CrawlSession[];
  }

  private async generateSummary(
    posts: SocialPost[],
    options: ReportOptions,
  ): Promise<ReportData["summary"]> {
    const totalPosts = posts.length;
    const uniqueAuthors = new Set(
      posts.map((p) => p.author_username).filter(Boolean),
    );

    // Calculate total engagement
    const totalEngagement = posts.reduce(
      (acc, post) => ({
        likes: acc.likes + (post.likes_count || 0),
        comments: acc.comments + (post.comments_count || 0),
        shares: acc.shares + (post.shares_count || 0),
        views: acc.views + (post.views_count || 0),
      }),
      { likes: 0, comments: 0, shares: 0, views: 0 },
    );

    // Get date range
    const dates = posts
      .map((p) => p.crawled_at)
      .filter(Boolean)
      .map((d) => new Date(d!));
    const dateRange = {
      earliest:
        dates.length > 0
          ? new Date(Math.min(...dates.map((d) => d.getTime())))
          : null,
      latest:
        dates.length > 0
          ? new Date(Math.max(...dates.map((d) => d.getTime())))
          : null,
    };

    // Platform breakdown
    const platforms: Record<string, number> = {};
    posts.forEach((post) => {
      platforms[post.platform] = (platforms[post.platform] || 0) + 1;
    });

    // Top keywords (extract from hashtags)
    const keywordCounts: Record<string, number> = {};
    posts.forEach((post) => {
      if (post.tags) {
        try {
          const hashtags = JSON.parse(post.tags) as string[];
          hashtags.forEach((tag) => {
            keywordCounts[tag] = (keywordCounts[tag] || 0) + 1;
          });
        } catch (error) {
          // Ignore parsing errors
        }
      }
    });

    const topKeywords = Object.entries(keywordCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([keyword, count]) => ({ keyword, count }));

    // Top authors
    const authorStats: Record<string, { posts: number; totalLikes: number }> =
      {};
    posts.forEach((post) => {
      if (post.author_username) {
        if (!authorStats[post.author_username]) {
          authorStats[post.author_username] = { posts: 0, totalLikes: 0 };
        }
        authorStats[post.author_username].posts++;
        authorStats[post.author_username].totalLikes += post.likes_count || 0;
      }
    });

    const topAuthors = Object.entries(authorStats)
      .sort(([, a], [, b]) => b.totalLikes - a.totalLikes)
      .slice(0, 10)
      .map(([author, stats]) => ({ author, ...stats }));

    // Get total media count
    const totalMediaQuery = "SELECT COUNT(*) as count FROM media_files";
    const mediaResult = this.database["db"].prepare(totalMediaQuery).get() as {
      count: number;
    };

    return {
      totalPosts,
      totalAuthors: <AUTHORS>
      totalMedia: mediaResult.count,
      totalEngagement,
      dateRange,
      platforms,
      topKeywords,
      topAuthors,
    };
  }

  private async exportAsJson(
    reportData: ReportData,
    outputPath: string,
  ): Promise<void> {
    const jsonData = JSON.stringify(reportData, null, 2);
    await fs.promises.writeFile(outputPath, jsonData, "utf-8");
  }

  private async exportAsCsv(
    reportData: ReportData,
    outputPath: string,
  ): Promise<void> {
    const headers = [
      "Platform",
      "Post ID",
      "URL",
      "Title",
      "Content",
      "Author Username",
      "Author Display Name",
      "Posted At",
      "Crawled At",
      "Likes",
      "Comments",
      "Shares",
      "Views",
      "Hashtags",
    ];

    const rows = reportData.posts.map((post) => [
      post.platform,
      post.post_id,
      post.url,
      (post.title || "").replace(/"/g, '""'),
      (post.content || "").replace(/"/g, '""'),
      post.author_username || "",
      post.author_display_name || "",
      post.posted_at || "",
      post.crawled_at || "",
      post.likes_count || 0,
      post.comments_count || 0,
      post.shares_count || 0,
      post.views_count || 0,
      post.tags || "",
    ]);

    const csvContent = [
      headers.map((h) => `"${h}"`).join(","),
      ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
    ].join("\n");

    await fs.promises.writeFile(outputPath, csvContent, "utf-8");
  }

  private async exportAsHtml(
    reportData: ReportData,
    outputPath: string,
  ): Promise<void> {
    const html = this.generateHtmlReport(reportData);
    await fs.promises.writeFile(outputPath, html, "utf-8");
  }

  private generateHtmlReport(reportData: ReportData): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sabah Tourism Crawler Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2563eb; }
        .metric-label { font-size: 14px; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .truncate { max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    </style>
</head>
<body>
    <h1>Sabah Tourism Crawler Report</h1>
    <p>Generated on: ${reportData.generatedAt.toLocaleString()}</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <div class="metric">
            <div class="metric-value">${reportData.summary.totalPosts}</div>
            <div class="metric-label">Total Posts</div>
        </div>
        <div class="metric">
            <div class="metric-value">${reportData.summary.totalAuthors}</div>
            <div class="metric-label">Unique Authors</div>
        </div>
        <div class="metric">
            <div class="metric-value">${reportData.summary.totalMedia}</div>
            <div class="metric-label">Media Files</div>
        </div>
        <div class="metric">
            <div class="metric-value">${reportData.summary.totalEngagement.likes.toLocaleString()}</div>
            <div class="metric-label">Total Likes</div>
        </div>
    </div>

    <h2>Posts (${reportData.posts.length})</h2>
    <table>
        <thead>
            <tr>
                <th>Platform</th>
                <th>Author</th>
                <th>Title</th>
                <th>Likes</th>
                <th>Comments</th>
                <th>Crawled At</th>
            </tr>
        </thead>
        <tbody>
            ${reportData.posts
              .slice(0, 100)
              .map(
                (post) => `
                <tr>
                    <td>${post.platform}</td>
                    <td>${post.author_username || "N/A"}</td>
                    <td class="truncate">${post.title || "N/A"}</td>
                    <td>${post.likes_count || 0}</td>
                    <td>${post.comments_count || 0}</td>
                    <td>${post.crawled_at ? new Date(post.crawled_at).toLocaleDateString() : "N/A"}</td>
                </tr>
            `,
              )
              .join("")}
        </tbody>
    </table>
    ${reportData.posts.length > 100 ? `<p><em>Showing first 100 posts of ${reportData.posts.length} total</em></p>` : ""}
</body>
</html>`;
  }

  private getDefaultOutputPath(format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `sabah-tourism-report-${timestamp}.${format}`;
    return path.join(process.cwd(), "reports", filename);
  }
}
