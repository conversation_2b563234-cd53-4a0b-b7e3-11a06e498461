CREATE TABLE "companies" (
	"id" serial PRIMARY KEY NOT NULL,
	"site_id" integer NOT NULL,
	"company_name" varchar(500) NOT NULL,
	"registration_number" varchar(255),
	"business_type" varchar(255),
	"category" varchar(255),
	"subcategory" varchar(255),
	"address" text,
	"state" varchar(255),
	"postcode" varchar(20),
	"city" varchar(255),
	"country" varchar(255) DEFAULT 'Malaysia',
	"phone" varchar(50),
	"fax" varchar(50),
	"email" varchar(255),
	"website" varchar(500),
	"contact_person" varchar(255),
	"certificate_number" varchar(255),
	"certificate_type" varchar(255),
	"certificate_status" varchar(100),
	"issued_date" varchar(255),
	"expiry_date" varchar(255),
	"source_url" varchar(1000),
	"page_number" integer,
	"raw_data" text,
	"data_hash" varchar(64),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "companies_data_hash_unique" UNIQUE("data_hash")
);
