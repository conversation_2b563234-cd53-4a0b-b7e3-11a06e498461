import { EventEmitter } from "events";
import puppeteer, { type Browser, type Page } from "puppeteer";
import type {
  CrawlerConfig,
  CrawlerStats,
  NextPageElement,
  PageData,
  ScrapedProduct,
} from "./types";
import { Logger } from "./utils/logger";

export class HalalProductScraper extends EventEmitter {
  private browser: Browser | null = null;
  private logger: Logger;
  private config: CrawlerConfig;
  private stats: CrawlerStats;
  private baseUrl: string;

  constructor(config: Partial<CrawlerConfig> = {}) {
    super();
    this.logger = Logger.getInstance();
    this.baseUrl =
      "https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=&negeri=&category=&cari=";

    this.config = {
      maxPages:
        config.maxPages || Number.parseInt(process.env.MAX_PAGES || "15"),
      concurrentPages:
        config.concurrentPages ||
        Number.parseInt(process.env.CONCURRENT_PAGES || "3"),
      delayBetweenPages:
        config.delayBetweenPages ||
        Number.parseInt(process.env.DELAY_BETWEEN_PAGES || "2000"),
      headless: config.headless !== undefined ? config.headless : true,
      timeout: config.timeout || 30000,
    };

    this.stats = {
      totalPages: 0,
      successfulPages: 0,
      failedPages: 0,
      totalProducts: 0,
      duplicateProducts: 0,
      startTime: new Date(),
      errors: [],
    };
  }

  async initialize(): Promise<void> {
    this.logger.info("Initializing browser...");

    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
      ],
    });

    this.logger.success("Browser initialized successfully");
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.logger.info("Browser closed");
    }
  }

  private async createPage(): Promise<Page> {
    if (!this.browser) {
      throw new Error("Browser not initialized");
    }

    const page = await this.browser.newPage();

    // Set viewport and user agent
    await page.setViewport({ width: 1920, height: 1080 });
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    );

    // Set timeout
    page.setDefaultTimeout(this.config.timeout);

    return page;
  }

  private async extractProductsFromPage(
    page: Page,
    pageNumber: number,
  ): Promise<PageData> {
    const url = await page.url();
    this.logger.info(`Extracting products from page ${pageNumber}: ${url}`);

    try {
      // Wait for the page content to load - try multiple selectors
      try {
        await page.waitForSelector("table", { timeout: 15000 });
      } catch (error) {
        // If no table found, try waiting for body content
        await page.waitForSelector("body", { timeout: 5000 });
        this.logger.warn(
          `No table found immediately on page ${pageNumber}, proceeding anyway`,
        );
      }

      const pageData = await page.evaluate((pageNum) => {
        const products: any[] = [];

        // Find the main table containing products (first table with data)
        const tables = document.querySelectorAll("table");
        let productTable: Element | null = null;

        // Look for the table that contains product data - should be the first table with multiple rows
        for (const table of tables) {
          const rows = table.querySelectorAll("tr");
          const text = table.textContent || "";

          // Check if this table has the expected headers and multiple data rows
          if (
            rows.length > 5 &&
            (text.includes("Bil") ||
              text.includes("Nama") ||
              text.includes("Syarikat") ||
              text.includes("Expiry"))
          ) {
            productTable = table;
            break;
          }
        }

        if (!productTable) {
          console.log("No product table found on page");
          return {
            products: [],
            hasNextPage: false,
            nextPageElement: null,
            pageNumber: pageNum,
          };
        }

        // Extract rows from the table
        const rows = productTable.querySelectorAll("tr");

        for (let i = 1; i < rows.length; i++) {
          // Skip header row
          const row = rows[i];
          const cells = row.querySelectorAll("td");

          if (cells.length >= 3) {
            // Should have: Bil, Company Name & Address, Expiry Date
            const cellTexts = Array.from(cells).map((cell) =>
              (cell.textContent || "").trim().replace(/\s+/g, " "),
            );

            // Skip empty rows
            if (!cellTexts[1] || cellTexts[1].length < 5) continue;

            const productData: any = {
              rawData: row.innerHTML,
              sourceUrl: window.location.href,
            };

            // Based on the actual structure:
            // Column 0: Bil (number)
            // Column 1: Company Name & Address (combined)
            // Column 2: Expiry Date

            const companyAndAddress = cellTexts[1] || "";
            const expiryDate = cellTexts[2] || "";

            // Clean up the company and address data
            // Split by line breaks and clean each line
            const lines = companyAndAddress
              .split(/[\n\r]+/)
              .map((line) => line.trim())
              .filter((line) => line && line.length > 0);

            if (lines.length > 0) {
              // Parse the first line to extract product name and company name based on "JENAMA:" keyword
              const firstLine = lines[0];

              if (firstLine.includes("JENAMA:")) {
                // Split by "JENAMA:" - text before is product name, text after is company name
                const parts = firstLine.split("JENAMA:");
                productData.productName = parts[0].trim();
                productData.companyName =
                  parts.length > 1 ? parts[1].trim() : "";
              } else {
                // If no "JENAMA:" keyword, treat the whole line as product name
                // and try to extract company name from business indicators
                const lowerLine = firstLine.toLowerCase();
                if (
                  lowerLine.includes("sdn") ||
                  lowerLine.includes("bhd") ||
                  lowerLine.includes("enterprise") ||
                  lowerLine.includes("trading") ||
                  lowerLine.includes("food") ||
                  lowerLine.includes("restaurant")
                ) {
                  // This looks like a company name
                  productData.productName = firstLine;
                  productData.companyName = firstLine;
                } else {
                  // This might be a product name without clear company indicators
                  productData.productName = firstLine;
                  productData.companyName = ""; // Empty string as requested
                }
              }

              productData.expiryDate = expiryDate.trim();

              // Extract address (everything after company name)
              if (lines.length > 1) {
                // Filter out address components
                const addressLines = lines.slice(1).filter((line) => {
                  const lowerLine = line.toLowerCase();
                  // Skip lines that look like postal codes or are too short
                  return line.length > 2 && !/^\d{5}$/.test(line.trim());
                });

                productData.address = addressLines.join(", ");

                // Try to extract state from the lines (usually at the end)
                const states = [
                  "Selangor",
                  "Kuala Lumpur",
                  "Johor",
                  "Penang",
                  "Perak",
                  "Kedah",
                  "Kelantan",
                  "Terengganu",
                  "Pahang",
                  "Negeri Sembilan",
                  "Melaka",
                  "Sabah",
                  "Sarawak",
                  "Perlis",
                  "Putrajaya",
                  "Labuan",
                  "Wilayah Persekutuan",
                ];

                for (const line of lines) {
                  for (const state of states) {
                    if (line.includes(state)) {
                      productData.state = state;
                      break;
                    }
                  }
                  if (productData.state) break;
                }
              }

              productData.country = "Malaysia";
              productData.category = "Halal Certified Product";

              // Add some metadata
              productData.extractedAt = new Date().toISOString();
              productData.pageNumber = pageNum;

              products.push(productData);
            }
          }
        }

        // Check for next page link - look for pagination links that can be clicked
        const nextLinks = document.querySelectorAll("a");
        let hasNextPage = false;
        let nextPageElement: Element | null = null;

        // Look for pagination links (numbers or "Next")
        for (const link of nextLinks) {
          const text = (link.textContent || "").toLowerCase().trim();
          const href = link.getAttribute("href") || "";
          const onclick = link.getAttribute("onclick") || "";

          // Check for "Next" link or number links that represent the next page
          if (href && (text.includes("next") || text.includes("seterusnya"))) {
            hasNextPage = true;
            nextPageElement = link;
            break;
          }

          // Look for numeric pagination - find the next number after current page
          if (href && /^\d+$/.test(text)) {
            const pageNum = Number.parseInt(text);
            // If this is the next sequential page number, use it
            if (pageNum === pageNum + 1) {
              hasNextPage = true;
              nextPageElement = link;
              break;
            }
          }

          // Also check onclick handlers for pagination
          if (onclick && onclick.includes("do_menu1") && /^\d+$/.test(text)) {
            const pageNum = Number.parseInt(text);
            if (pageNum === pageNum + 1) {
              hasNextPage = true;
              nextPageElement = link;
              break;
            }
          }
        }

        // If we didn't find a specific next link, look for any numeric link that could be next
        if (!hasNextPage) {
          for (const link of nextLinks) {
            const text = (link.textContent || "").toLowerCase().trim();
            const href = link.getAttribute("href") || "";

            // Look for any number greater than 1 (assuming we start from page 1)
            if (href && /^\d+$/.test(text)) {
              const pageNum = Number.parseInt(text);
              if (pageNum > 1) {
                hasNextPage = true;
                nextPageElement = link;
                break;
              }
            }
          }
        }

        // Also check if we can find pagination info in tables
        if (!hasNextPage) {
          const allTables = document.querySelectorAll("table");
          for (const table of allTables) {
            const text = table.textContent || "";
            if (text.includes("Total Record") && text.includes("Page 1")) {
              // If we're on page 1 and there are total records, there should be more pages
              hasNextPage = true;
              // Look for any pagination link in this case
              for (const link of nextLinks) {
                const linkText = (link.textContent || "").trim();
                if (/^\d+$/.test(linkText) && Number.parseInt(linkText) > 1) {
                  nextPageElement = link;
                  break;
                }
              }
              break;
            }
          }
        }

        return {
          products,
          hasNextPage,
          nextPageElement: nextPageElement
            ? {
                text: nextPageElement.textContent || "",
                href: nextPageElement.getAttribute("href") || "",
                onclick: nextPageElement.getAttribute("onclick") || "",
                outerHTML: nextPageElement.outerHTML,
              }
            : undefined,
          pageNumber: pageNum,
        };
      }, pageNumber);

      this.logger.success(
        `Extracted ${pageData.products.length} products from page ${pageNumber}`,
      );
      return {
        url,
        pageNumber,
        products: pageData.products,
        hasNextPage: pageData.hasNextPage,
        nextPageElement: pageData.nextPageElement,
      };
    } catch (error) {
      this.logger.error(
        `Failed to extract products from page ${pageNumber}:`,
        error,
      );
      this.stats.errors.push(`Page ${pageNumber}: ${error}`);
      return {
        url,
        pageNumber,
        products: [],
        hasNextPage: false,
      };
    }
  }

  async scrapePage(pageNumber: number, url?: string): Promise<PageData> {
    const page = await this.createPage();

    try {
      const targetUrl = url || this.baseUrl;
      this.logger.info(`Navigating to page ${pageNumber}: ${targetUrl}`);

      await page.goto(targetUrl, {
        waitUntil: "domcontentloaded",
        timeout: this.config.timeout,
      });

      // Add a small delay to ensure page is fully loaded
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // If this is the first page, navigate to the Products section
      if (pageNumber === 1) {
        this.logger.info(
          'Clicking on "Produk" tab to navigate to products section...',
        );

        try {
          // Look for the Produk link/button
          await page.waitForSelector("body", { timeout: 10000 });

          // Click on the Produk link - it should contain "Produk" text
          const produkClicked = await page.evaluate(() => {
            // Find all links/elements containing "Produk"
            const allElements = document.querySelectorAll("a");

            for (const element of allElements) {
              const text = element.textContent || "";
              if (text.includes("Produk")) {
                (element as HTMLAnchorElement).click();
                return true;
              }
            }
            return false;
          });

          if (produkClicked) {
            this.logger.success("Successfully clicked on Produk tab");
            // Wait for navigation or content to load
            await new Promise((resolve) => setTimeout(resolve, 10000));

            // Wait for product table to load
            try {
              await page.waitForSelector("table", { timeout: 15000 });
            } catch (err) {
              this.logger.warn("No table found after clicking Produk tab");
            }
          } else {
            throw new Error("Could not find Produk tab to click");
            // this.logger.warn('Could not find Produk tab to click');
          }
        } catch (error) {
          this.logger.warn("Failed to click Produk tab:", error);
        }
      }

      const pageData = await this.extractProductsFromPage(page, pageNumber);
      this.stats.successfulPages++;

      return pageData;
    } catch (error) {
      this.logger.error(`Failed to scrape page ${pageNumber}:`, error);
      this.stats.failedPages++;
      this.stats.errors.push(`Page ${pageNumber}: ${error}`);

      return {
        url: url || this.baseUrl,
        pageNumber,
        products: [],
        hasNextPage: false,
      };
    } finally {
      await page.close();
    }
  }

  async scrapeAllPages(): Promise<void> {
    this.logger.info(
      `Starting to scrape up to ${this.config.maxPages} pages...`,
    );

    const page = await this.createPage();
    let pageNumber = 1;

    try {
      // Navigate to the initial page
      this.logger.info(`Navigating to initial page: ${this.baseUrl}`);
      await page.goto(this.baseUrl, {
        waitUntil: "domcontentloaded",
        timeout: this.config.timeout,
      });

      // Add initial delay to ensure page is fully loaded
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Navigate to the Products section on the first page
      this.logger.info(
        'Clicking on "Produk" tab to navigate to products section...',
      );

      try {
        await page.waitForSelector("body", { timeout: 10000 });

        const produkClicked = await page.evaluate(() => {
          const allElements = document.querySelectorAll("a");

          for (const element of allElements) {
            const text = element.textContent || "";
            if (text.includes("Produk")) {
              (element as HTMLAnchorElement).click();
              return true;
            }
          }
          return false;
        });

        if (produkClicked) {
          this.logger.success("Successfully clicked on Produk tab");
          // Wait for navigation or content to load with minimum 6 seconds
          await new Promise((resolve) =>
            setTimeout(resolve, Math.max(6000, this.config.delayBetweenPages)),
          );

          // Wait for product table to load
          try {
            await page.waitForSelector("table", { timeout: 15000 });
          } catch (err) {
            this.logger.warn("No table found after clicking Produk tab");
          }
        } else {
          throw new Error("Could not find Produk tab to click");
        }
      } catch (error) {
        this.logger.error("Failed to click Produk tab:", error);
        throw error;
      }

      // Now scrape pages using click-based navigation
      while (pageNumber <= this.config.maxPages) {
        this.logger.progress(
          pageNumber,
          this.config.maxPages,
          `Scraping page ${pageNumber}`,
        );

        const pageData = await this.extractProductsFromPage(page, pageNumber);
        this.stats.totalPages++;
        this.stats.successfulPages++;

        // Emit each product individually
        for (const product of pageData.products) {
          this.emit("product", product);
          this.stats.totalProducts++;
        }

        // Check if there's a next page
        if (!pageData.hasNextPage || !pageData.nextPageElement) {
          this.logger.info(`No more pages found after page ${pageNumber}`);
          break;
        }

        // Click on the next page link
        this.logger.info(
          `Clicking on next page link: ${pageData.nextPageElement.text}`,
        );

        try {
          const nextPageClicked = await page.evaluate((elementInfo) => {
            // Find the element to click based on the stored information
            const allLinks = document.querySelectorAll("a");

            for (const link of allLinks) {
              const text = (link.textContent || "").trim();
              const href = link.getAttribute("href") || "";
              const onclick = link.getAttribute("onclick") || "";

              // Match based on text, href, and onclick to find the exact element
              if (
                text === elementInfo.text &&
                href === elementInfo.href &&
                onclick === elementInfo.onclick
              ) {
                (link as HTMLAnchorElement).click();
                return true;
              }
            }
            return false;
          }, pageData.nextPageElement);

          if (nextPageClicked) {
            this.logger.success("Successfully clicked on next page link");

            // Wait for navigation with minimum 6 seconds
            const waitTime = Math.max(6000, this.config.delayBetweenPages);
            this.logger.info(`Waiting ${waitTime}ms for page to load...`);
            await new Promise((resolve) => setTimeout(resolve, waitTime));

            // Wait for new content to load
            try {
              await page.waitForSelector("table", { timeout: 15000 });
            } catch (err) {
              this.logger.warn("No table found after clicking next page");
            }

            pageNumber++;
          } else {
            this.logger.warn(
              "Failed to click next page link, ending pagination",
            );
            break;
          }
        } catch (error) {
          this.logger.error("Error clicking next page:", error);
          this.stats.failedPages++;
          this.stats.errors.push(`Page ${pageNumber + 1}: ${error}`);
          break;
        }
      }
    } catch (error) {
      this.logger.error("Error during scraping:", error);
      this.stats.errors.push(`General error: ${error}`);
    } finally {
      await page.close();
    }

    this.stats.endTime = new Date();
    this.logger.success(
      `Scraping completed! Total products: ${this.stats.totalProducts}`,
    );

    this.emit("finished");
  }

  getStats(): CrawlerStats {
    return { ...this.stats };
  }
}
