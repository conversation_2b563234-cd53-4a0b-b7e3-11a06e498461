#!/usr/bin/env bun

import "dotenv/config";
import chalk from "chalk";
import { createCrawlerConfig } from "../config/crawler.config.js";
import { createDatabaseConfig } from "../config/database.config.js";
import { DatabaseManager } from "../src/crawlers/base/DatabaseManager.js";
import { DouyinCrawler } from "../src/crawlers/douyin/DouyinCrawler.js";
import { ReportGenerator } from "../src/reporting/ReportGenerator.js";
import logger from "../src/utils/logger.js";
import {
  getMemoryUsage,
  performanceMonitor,
} from "../src/utils/performance.js";

/**
 * Comprehensive example demonstrating all crawler capabilities
 * This script shows:
 * - Setting up the crawler with custom configuration
 * - Running a crawl with progress monitoring
 * - Generating reports in multiple formats
 * - Performance monitoring and optimization
 * - Error handling and recovery
 */

async function runExampleCrawl() {
  console.log(chalk.blue("🚀 Sabah Tourism Crawler - Complete Example"));
  console.log(
    chalk.gray("This example demonstrates all crawler capabilities\n"),
  );

  let database: DatabaseManager | null = null;
  let crawler: DouyinCrawler | null = null;

  try {
    // Step 1: Initialize Database
    console.log(chalk.blue("📊 Step 1: Initializing Database"));
    const dbConfig = createDatabaseConfig();
    database = new DatabaseManager(dbConfig);

    const initialStats = database.getStats();
    console.log(
      chalk.green(
        `✅ Database ready (${initialStats.totalPosts} existing posts)`,
      ),
    );

    // Step 2: Configure Crawler
    console.log(chalk.blue("\n⚙️  Step 2: Configuring Crawler"));
    const crawlerConfig = createCrawlerConfig(
      "douyin",
      ["sabah tourism", "kota kinabalu", "mount kinabalu"],
      {
        maxPosts: 20, // Small number for demo
        downloadMedia: true,
        mediaTypes: ["image", "video"],
        browserConfig: {
          headless: true, // Set to false to see browser in action
          timeout: 30000,
        },
        rateLimiting: {
          requestsPerMinute: 15, // Conservative for demo
          downloadConcurrency: 2,
        },
        filters: {
          minLikes: 10, // Only posts with at least 10 likes
          minViews: 100,
        },
      },
    );

    console.log(chalk.green("✅ Crawler configured"));
    console.log(
      chalk.cyan(`   Keywords: ${crawlerConfig.keywords.join(", ")}`),
    );
    console.log(chalk.cyan(`   Max posts: ${crawlerConfig.maxPosts}`));
    console.log(
      chalk.cyan(`   Download media: ${crawlerConfig.downloadMedia}`),
    );
    console.log(
      chalk.cyan(`   Min likes filter: ${crawlerConfig.filters?.minLikes}`),
    );

    // Step 3: Create Crawler Instance
    console.log(chalk.blue("\n🤖 Step 3: Creating Crawler Instance"));
    crawler = new DouyinCrawler(crawlerConfig, database);

    // Step 4: Set up Event Monitoring
    console.log(chalk.blue("\n📡 Step 4: Setting up Event Monitoring"));

    let progressUpdateCount = 0;
    let mediaDownloadCount = 0;

    crawler.on("progress", (progress) => {
      progressUpdateCount++;
      const { processedPosts, totalPosts, successfulPosts, failedPosts } =
        progress.progress;

      if (progressUpdateCount % 5 === 0 || progress.status === "completed") {
        // Throttle output
        console.log(
          chalk.blue(`📊 Progress: ${processedPosts}/${totalPosts} posts`),
        );
        console.log(chalk.green(`   ✅ Success: ${successfulPosts}`));
        console.log(chalk.red(`   ❌ Failed: ${failedPosts}`));

        if (progress.currentActivity) {
          console.log(chalk.gray(`   🔄 ${progress.currentActivity}`));
        }

        // Show memory usage periodically
        const memory = getMemoryUsage();
        console.log(chalk.gray(`   💾 Memory: ${memory.heapUsedMB}MB`));
      }
    });

    crawler.on("media-downloaded", (downloadProgress) => {
      mediaDownloadCount++;
      if (downloadProgress.status === "completed") {
        console.log(chalk.green(`📥 Downloaded: ${downloadProgress.fileName}`));
      } else if (downloadProgress.status === "failed") {
        console.log(
          chalk.red(`❌ Download failed: ${downloadProgress.fileName}`),
        );
      }
    });

    crawler.on("error", (error) => {
      console.error(chalk.red(`❌ Crawler error: ${error.message}`));
    });

    // Step 5: Start Performance Monitoring
    console.log(chalk.blue("\n⚡ Step 5: Starting Performance Monitoring"));
    performanceMonitor.startOperation("full-crawl", {
      keywords: crawlerConfig.keywords,
      maxPosts: crawlerConfig.maxPosts,
    });

    // Step 6: Execute Crawl
    console.log(chalk.blue("\n🔍 Step 6: Executing Crawl"));
    console.log(
      chalk.yellow(
        "This may take several minutes depending on the number of posts...",
      ),
    );

    const startTime = Date.now();
    const crawlResult = await crawler.crawl();
    const crawlDuration = Date.now() - startTime;

    // End performance monitoring
    const perfMetrics = performanceMonitor.endOperation("full-crawl", {
      postsProcessed: crawlResult.progress.processedPosts,
      successfulPosts: crawlResult.progress.successfulPosts,
      mediaDownloaded: crawlResult.progress.downloadedMedia,
    });

    // Step 7: Show Crawl Results
    console.log(chalk.green("\n🎉 Step 7: Crawl Results"));
    console.log(
      chalk.cyan(`   Duration: ${Math.round(crawlDuration / 1000)}s`),
    );
    console.log(
      chalk.cyan(`   Posts processed: ${crawlResult.progress.processedPosts}`),
    );
    console.log(
      chalk.cyan(`   Successful: ${crawlResult.progress.successfulPosts}`),
    );
    console.log(chalk.cyan(`   Failed: ${crawlResult.progress.failedPosts}`));
    console.log(
      chalk.cyan(
        `   Media downloaded: ${crawlResult.progress.downloadedMedia}/${crawlResult.progress.totalMedia}`,
      ),
    );
    console.log(chalk.cyan(`   Errors: ${crawlResult.errors.length}`));

    // Step 8: Database Analysis
    console.log(chalk.blue("\n📈 Step 8: Database Analysis"));
    const finalStats = database.getStats();
    const newPosts = finalStats.totalPosts - initialStats.totalPosts;

    console.log(
      chalk.cyan(
        `   Total posts in database: ${finalStats.totalPosts} (+${newPosts} new)`,
      ),
    );
    console.log(
      chalk.cyan(`   Total media files: ${finalStats.totalMediaFiles}`),
    );
    console.log(chalk.cyan(`   Active sessions: ${finalStats.activeSessions}`));

    // Show platform breakdown
    console.log(chalk.blue("\n📊 Platform Breakdown:"));
    Object.entries(finalStats.platformBreakdown).forEach(
      ([platform, count]) => {
        console.log(chalk.cyan(`   ${platform}: ${count} posts`));
      },
    );

    // Show top authors
    const topAuthors = database.getTopAuthors("douyin", 5);
    if (topAuthors.length > 0) {
      console.log(chalk.blue("\n👥 Top Authors: <AUTHORS>
      topAuthors.forEach((author, index) => {
        console.log(
          chalk.cyan(
            `   ${index + 1}. ${author.author_username} (${author.post_count} posts, ${author.total_likes} likes)`,
          ),
        );
      });
    }

    // Step 9: Generate Reports
    console.log(chalk.blue("\n📋 Step 9: Generating Reports"));
    const reportGenerator = new ReportGenerator(database);

    // Generate HTML report
    console.log(chalk.blue("Generating HTML report..."));
    const htmlReport = await reportGenerator.generateReport({
      platform: "douyin",
      includeMedia: true,
      format: "html",
    });

    const htmlPath = await reportGenerator.exportReport(htmlReport, {
      format: "html",
      outputPath: "./reports/sabah-tourism-example.html",
    });
    console.log(chalk.green(`✅ HTML report: ${htmlPath}`));

    // Generate CSV report
    console.log(chalk.blue("Generating CSV report..."));
    const csvPath = await reportGenerator.exportReport(htmlReport, {
      format: "csv",
      outputPath: "./reports/sabah-tourism-example.csv",
    });
    console.log(chalk.green(`✅ CSV report: ${csvPath}`));

    // Step 10: Performance Summary
    console.log(chalk.blue("\n⚡ Step 10: Performance Summary"));
    const perfSummary = performanceMonitor.getSummary();

    console.log(
      chalk.cyan(`   Total operations: ${perfSummary.totalOperations}`),
    );
    console.log(
      chalk.cyan(`   Total time: ${Math.round(perfSummary.totalDuration)}ms`),
    );
    console.log(
      chalk.cyan(
        `   Average operation time: ${Math.round(perfSummary.avgDuration)}ms`,
      ),
    );
    console.log(
      chalk.cyan(
        `   Peak memory usage: ${Math.round(perfSummary.memoryPeak / 1024 / 1024)}MB`,
      ),
    );

    // Show operation breakdown
    console.log(chalk.blue("\n🔧 Operation Breakdown:"));
    Object.entries(perfSummary.operationBreakdown).forEach(
      ([operation, count]) => {
        const avgMetrics = performanceMonitor.getAverageMetrics(operation);
        if (avgMetrics) {
          console.log(
            chalk.cyan(
              `   ${operation}: ${count} ops, avg ${Math.round(avgMetrics.avgDuration)}ms`,
            ),
          );
        }
      },
    );

    // Step 11: Success Summary
    console.log(chalk.green("\n🎊 Example Completed Successfully!"));
    console.log(chalk.blue("\n📝 What was demonstrated:"));
    console.log(chalk.cyan("   ✅ Database setup and management"));
    console.log(chalk.cyan("   ✅ Crawler configuration and execution"));
    console.log(chalk.cyan("   ✅ Real-time progress monitoring"));
    console.log(chalk.cyan("   ✅ Media download with progress tracking"));
    console.log(chalk.cyan("   ✅ Error handling and retry mechanisms"));
    console.log(chalk.cyan("   ✅ Performance monitoring and optimization"));
    console.log(chalk.cyan("   ✅ Database analysis and statistics"));
    console.log(chalk.cyan("   ✅ Report generation in multiple formats"));
    console.log(chalk.cyan("   ✅ Memory usage monitoring"));

    console.log(chalk.blue("\n📁 Generated Files:"));
    console.log(chalk.cyan(`   📊 Database: ${dbConfig.path}`));
    console.log(chalk.cyan(`   📄 HTML Report: ${htmlPath}`));
    console.log(chalk.cyan(`   📊 CSV Report: ${csvPath}`));
    console.log(chalk.cyan("   📥 Media Files: ./output/douyin/"));
    console.log(chalk.cyan("   📝 Logs: ./logs/"));
  } catch (error) {
    console.error(chalk.red(`❌ Example failed: ${(error as Error).message}`));
    logger.error("Example crawl error", { error });

    // Show partial results if available
    if (database) {
      try {
        const stats = database.getStats();
        console.log(chalk.yellow("\n📊 Partial Results:"));
        console.log(chalk.cyan(`   Posts in database: ${stats.totalPosts}`));
        console.log(chalk.cyan(`   Media files: ${stats.totalMediaFiles}`));
      } catch (dbError) {
        console.error(chalk.red("Could not retrieve database stats"));
      }
    }

    process.exit(1);
  } finally {
    // Cleanup
    if (crawler) {
      await crawler.cleanup();
    }
    if (database) {
      database.close();
    }

    // Final memory check
    const finalMemory = getMemoryUsage();
    console.log(
      chalk.gray(`\n💾 Final memory usage: ${finalMemory.heapUsedMB}MB`),
    );
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log(chalk.yellow("\n⏸️  Example interrupted by user"));
  process.exit(0);
});

// Run the example
runExampleCrawl().catch((error) => {
  console.error(chalk.red(`❌ Unexpected error: ${error.message}`));
  process.exit(1);
});
