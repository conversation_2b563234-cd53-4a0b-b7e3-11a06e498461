import type { SearchOptions } from "../../types/crawler.js";
import type {
  PlatformConfig,
  SearchResult,
  SocialMediaPost,
} from "../../types/social-media.js";
import { crawlerLogger } from "../../utils/logger.js";
import { createRetryConfig, sleep, withRetry } from "../../utils/retry.js";
import { BaseCrawler } from "../base/BaseCrawler.js";
import {
  douyinConfig,
  douyinConstants,
  douyinSelectors,
} from "./DouyinConfig.js";
import { DouyinParser } from "./DouyinParser.js";

export class DouyinCrawler extends BaseCrawler {
  protected getPlatformConfig(): PlatformConfig {
    return douyinConfig;
  }

  protected validateConfig(): boolean {
    // Validate Douyin-specific configuration
    if (this.config.platform !== "douyin") {
      throw new Error("Invalid platform for DouyinCrawler");
    }

    if (!this.config.keywords || this.config.keywords.length === 0) {
      throw new Error("Keywords are required for Douyin crawling");
    }

    return true;
  }

  public async search(
    keyword: string,
    options?: SearchOptions,
  ): Promise<SearchResult> {
    crawlerLogger.info("Starting Douyin search", { keyword, options });

    const retryConfig = createRetryConfig({
      maxRetries: 3,
      initialDelay: 2000,
    });

    const result = await withRetry(
      () => this.performSearch(keyword, options),
      retryConfig,
      `douyin-search-${keyword}`,
    );

    if (!result.success) {
      throw result.error || new Error("Search failed");
    }

    return result.result!;
  }

  private async performSearch(
    keyword: string,
    options?: SearchOptions,
  ): Promise<SearchResult> {
    const page = this.stagehand.page;
    const startTime = Date.now();
    const allPosts: SocialMediaPost[] = [];
    let currentPage = 1;
    const maxResults = options?.maxResults || this.config.maxPosts;

    try {
      // Navigate to Douyin search page
      await page.goto(douyinConfig.baseUrl, {
        waitUntil: "domcontentloaded", // Changed from 'networkidle' for better reliability
        timeout: douyinConstants.requestTimeout,
      });

      crawlerLogger.info("Navigated to Douyin homepage");
      await sleep(douyinConstants.searchDelay);

      // Handle any popups or overlays before searching
      await this.handlePopups(page);

      // Perform initial search
      await this.performSearchAction(page, keyword);

      // Extract posts with pagination support
      while (allPosts.length < maxResults) {
        crawlerLogger.info("Extracting posts from page", {
          page: currentPage,
          keyword,
        });

        // Wait for search results to load
        await page.waitForSelector(douyinSelectors.searchResults, {
          timeout: douyinConstants.requestTimeout,
        });

        // Extract current page results
        const pageResults = await this.extractCurrentPageResults(page, keyword);

        if (pageResults.length === 0) {
          crawlerLogger.info("No more results found", {
            keyword,
            page: currentPage,
          });
          break;
        }

        // Add new posts (avoid duplicates)
        const newPosts = pageResults.filter(
          (post) => !allPosts.some((existing) => existing.id === post.id),
        );

        allPosts.push(...newPosts);
        crawlerLogger.info("Added posts from page", {
          page: currentPage,
          newPosts: newPosts.length,
          totalPosts: allPosts.length,
        });

        // Check if we have enough posts or if we should continue
        if (allPosts.length >= maxResults || newPosts.length === 0) {
          break;
        }

        // Try to load more content (infinite scroll or pagination)
        const hasMore = await this.loadMoreContent(page);
        if (!hasMore) {
          crawlerLogger.info("No more content available", { keyword });
          break;
        }

        currentPage++;
        await sleep(douyinConstants.scrollDelay);
      }

      // Apply filters and limit results
      const filteredPosts = this.applyFilters(allPosts, options);
      const limitedPosts = filteredPosts.slice(0, maxResults);

      const searchResult: SearchResult = {
        posts: limitedPosts,
        pagination: {
          hasNext: allPosts.length >= maxResults,
          currentPage,
          totalCount: allPosts.length,
        },
        searchMetadata: {
          keyword,
          platform: "douyin",
          searchedAt: new Date(),
          resultsCount: limitedPosts.length,
          searchDuration: Date.now() - startTime,
        },
      };

      crawlerLogger.info("Search completed", {
        keyword,
        totalFound: allPosts.length,
        returned: limitedPosts.length,
        pages: currentPage,
        duration: searchResult.searchMetadata.searchDuration,
      });

      return searchResult;
    } catch (error) {
      crawlerLogger.error("Search failed", { keyword, error });
      throw error;
    }
  }

  private async performSearchAction(page: any, keyword: string): Promise<void> {
    try {
      // Try multiple search strategies
      const searchStrategies = [
        async () => {
          await page.act("Click on the search input box");
          await sleep(1000);
          await page.act(`Type "${keyword}" into the search box`);
          await sleep(1000);
          await page.act("Press Enter or click search button to search");
        },
        async () => {
          await page.click(douyinSelectors.searchInput);
          await page.type(douyinSelectors.searchInput, keyword);
          await page.click(douyinSelectors.searchButton);
        },
        async () => {
          await page.fill(douyinSelectors.searchInput, keyword);
          await page.press(douyinSelectors.searchInput, "Enter");
        },
      ];

      for (const strategy of searchStrategies) {
        try {
          await strategy();
          await sleep(douyinConstants.searchDelay);

          // Check if search was successful
          const hasResults = await page.$(douyinSelectors.searchResults);
          if (hasResults) {
            crawlerLogger.info("Search successful", { keyword });
            return;
          }
        } catch (error) {
          crawlerLogger.warn("Search strategy failed, trying next", { error });
        }
      }

      throw new Error("All search strategies failed");
    } catch (error) {
      crawlerLogger.error("Search action failed", { keyword, error });
      throw error;
    }
  }

  private async handlePopups(page: any): Promise<void> {
    crawlerLogger.info("Checking for popups and overlays");

    try {
      // Common popup selectors for Douyin
      const popupSelectors = [
        // Cookie consent banners
        '[data-testid="cookie-banner"] button',
        ".cookie-banner .close-btn",
        '.cookie-consent button[data-action="accept"]',
        ".cookie-notice .accept-btn",

        // Login/signup modals
        ".login-modal .close-btn",
        ".signup-modal .close-btn",
        '[data-testid="login-modal"] .close',
        ".modal-close",
        ".modal .close-button",

        // Age verification popups
        ".age-verification .confirm-btn",
        ".age-gate .continue-btn",

        // App download prompts
        ".app-download-banner .close-btn",
        ".download-app-modal .close",
        ".app-promotion .dismiss-btn",

        // General overlay close buttons
        ".overlay .close-btn",
        ".popup .close-btn",
        ".dialog .close-btn",
        ".notification .close-btn",

        // Common close button patterns
        'button[aria-label="Close"]',
        'button[aria-label="关闭"]', // Chinese for "Close"
        'button[title="Close"]',
        'button[title="关闭"]',
        ".close-icon",
        ".close-x",
        '[data-action="close"]',
        '[data-dismiss="modal"]',
      ];

      // Try to close popups using Stagehand's AI capabilities first
      try {
        await page.act(
          "Look for any popup, modal, banner, or overlay that is blocking the page and close it by clicking the close button, X button, or dismiss button",
        );
        await sleep(2000);
        crawlerLogger.info("Used AI to handle popups");
      } catch (aiError) {
        crawlerLogger.info("AI popup handling failed, trying manual selectors");
      }

      // Fallback to manual selector-based popup closing
      for (const selector of popupSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            await element.click();
            crawlerLogger.info("Closed popup using selector", { selector });
            await sleep(1000); // Wait for popup to close
          }
        } catch (error) {}
      }

      // Handle specific Douyin popups with more targeted approaches
      await this.handleDouyinSpecificPopups(page);

      crawlerLogger.info("Popup handling completed");
    } catch (error) {
      crawlerLogger.warn("Error during popup handling", { error });
      // Don't throw error - continue with crawling even if popup handling fails
    }
  }

  private async handleDouyinSpecificPopups(page: any): Promise<void> {
    try {
      // Handle region/location popup
      try {
        await page.act(
          "If there is a location or region selection popup, dismiss it or select a default option",
        );
        await sleep(1000);
      } catch (error) {
        // Ignore
      }

      // Handle "Use App" prompts
      try {
        await page.act(
          'If there is a prompt to download or use the mobile app, dismiss it by clicking "Continue in browser" or close button',
        );
        await sleep(1000);
      } catch (error) {
        // Ignore
      }

      // Handle login suggestions
      try {
        await page.act(
          "If there is a login or signup suggestion popup, close it without logging in",
        );
        await sleep(1000);
      } catch (error) {
        // Ignore
      }

      // Handle notification permission requests
      try {
        await page.act(
          'If there is a notification permission request, click "Block" or "Not now"',
        );
        await sleep(1000);
      } catch (error) {
        // Ignore
      }
    } catch (error) {
      crawlerLogger.warn("Error handling Douyin-specific popups", { error });
    }
  }

  public async extractPost(url: string): Promise<SocialMediaPost | null> {
    crawlerLogger.info("Extracting Douyin post", { url });

    if (!DouyinParser.isValidDouyinUrl(url)) {
      crawlerLogger.warn("Invalid Douyin URL", { url });
      return null;
    }

    const retryConfig = createRetryConfig({
      maxRetries: 2,
      initialDelay: 3000,
    });

    const result = await withRetry(
      () => this.performPostExtraction(url),
      retryConfig,
      `douyin-extract-${url}`,
    );

    if (!result.success) {
      crawlerLogger.error("Post extraction failed", {
        url,
        error: result.error,
      });
      return null;
    }

    return result.result!;
  }

  private async performPostExtraction(
    url: string,
  ): Promise<SocialMediaPost | null> {
    const page = this.stagehand.page;

    try {
      // Navigate to the video page
      await page.goto(url, {
        waitUntil: "domcontentloaded", // Changed from 'networkidle' for better reliability
        timeout: douyinConstants.requestTimeout,
      });

      crawlerLogger.info("Navigated to video page", { url });

      // Handle any popups on the video page
      await this.handlePopups(page);

      // Wait for video to load
      await sleep(douyinConstants.videoLoadDelay);

      // Extract detailed video information
      const extractedData = await page.extract({
        instruction: `Extract detailed information about this Douyin video:
          - Video ID from the URL
          - Video title and description
          - Author information (username, display name, avatar, verification status, follower count)
          - Engagement metrics (likes, comments, shares, views)
          - Video URL and thumbnail URL
          - Video duration and dimensions if available
          - All hashtags and mentions in the description
          - Location information if available
          - Publication date if visible`,
        schema: DouyinParser.DouyinVideoDetailSchema,
      });

      crawlerLogger.info("Extracted video detail data", { url });

      // Parse the extracted data
      const post = DouyinParser.parseVideoDetail(extractedData);

      if (post) {
        crawlerLogger.info("Successfully extracted post", {
          url,
          postId: post.id,
          author: post.author.username,
        });
      }

      return post;
    } catch (error) {
      crawlerLogger.error("Post extraction failed", { url, error });
      throw error;
    }
  }

  private applyFilters(
    posts: SocialMediaPost[],
    options?: SearchOptions,
  ): SocialMediaPost[] {
    let filtered = [...posts];

    // Apply date range filter
    if (options?.dateRange) {
      filtered = filtered.filter((post) => {
        if (!post.publishedAt) return true;
        return (
          post.publishedAt >= options.dateRange!.from &&
          post.publishedAt <= options.dateRange!.to
        );
      });
    }

    // Apply engagement filters from crawler config
    if (this.config.filters?.minLikes) {
      filtered = filtered.filter(
        (post) => post.engagement.likes >= this.config.filters!.minLikes!,
      );
    }

    if (this.config.filters?.minViews && this.config.filters.minViews > 0) {
      filtered = filtered.filter(
        (post) =>
          (post.engagement.views || 0) >= this.config.filters!.minViews!,
      );
    }

    // Apply author filters
    if (
      this.config.filters?.authors &&
      this.config.filters.authors.length > 0
    ) {
      filtered = filtered.filter((post) =>
        this.config.filters!.authors!.includes(post.author.username),
      );
    }

    if (
      this.config.filters?.excludeAuthors &&
      this.config.filters.excludeAuthors.length > 0
    ) {
      filtered = filtered.filter(
        (post) =>
          !this.config.filters!.excludeAuthors!.includes(post.author.username),
      );
    }

    return filtered;
  }

  private async extractCurrentPageResults(
    page: any,
    keyword: string,
  ): Promise<SocialMediaPost[]> {
    try {
      // Extract search results using Stagehand's AI extraction
      const extractedData = await page.extract({
        instruction: `Extract video posts from the current search results page. For each video post, get:
          - Video ID from the URL (look for /video/ in links)
          - Complete video URL (full link to the video page)
          - Title or description text
          - Author username and display name
          - Like count, comment count, share count if visible (parse Chinese numbers like 万, 千)
          - Video thumbnail URL if available
          - Any hashtags in the description (starting with #)
          - Publication date if visible`,
        schema: DouyinParser.DouyinPostSchema,
      });

      crawlerLogger.info("Extracted page data", {
        keyword,
        postsFound: extractedData.posts?.length || 0,
      });

      // Parse and validate the extracted data
      const posts = DouyinParser.parseSearchResults(extractedData);

      // Additional validation and enhancement
      return posts.map((post) => this.enhancePostData(post, keyword));
    } catch (error) {
      crawlerLogger.error("Failed to extract page results", { keyword, error });
      return [];
    }
  }

  private async loadMoreContent(page: any): Promise<boolean> {
    try {
      // Try different methods to load more content
      const loadMoreStrategies = [
        // Strategy 1: Infinite scroll
        async () => {
          await page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
          });
          await sleep(douyinConstants.scrollDelay);

          // Check if new content loaded
          const initialHeight = await page.evaluate(
            () => document.body.scrollHeight,
          );
          await sleep(2000);
          const newHeight = await page.evaluate(
            () => document.body.scrollHeight,
          );

          return newHeight > initialHeight;
        },

        // Strategy 2: Load more button
        async () => {
          const loadMoreButton = await page.$(douyinSelectors.loadMoreButton);
          if (loadMoreButton) {
            await loadMoreButton.click();
            await sleep(douyinConstants.scrollDelay);
            return true;
          }
          return false;
        },

        // Strategy 3: Next page button
        async () => {
          const nextButton = await page.$(douyinSelectors.nextPageButton);
          if (nextButton) {
            await nextButton.click();
            await sleep(douyinConstants.searchDelay);
            return true;
          }
          return false;
        },
      ];

      for (const strategy of loadMoreStrategies) {
        try {
          const success = await strategy();
          if (success) {
            crawlerLogger.info("Successfully loaded more content");
            return true;
          }
        } catch (error) {
          crawlerLogger.warn("Load more strategy failed", { error });
        }
      }

      return false;
    } catch (error) {
      crawlerLogger.error("Failed to load more content", { error });
      return false;
    }
  }

  private enhancePostData(
    post: SocialMediaPost,
    keyword: string,
  ): SocialMediaPost {
    // Enhance post data with additional processing
    const enhanced = { ...post };

    // Extract additional hashtags from content
    if (enhanced.content) {
      const additionalHashtags = DouyinParser.extractHashtags(enhanced.content);
      enhanced.hashtags = [
        ...new Set([...enhanced.hashtags, ...additionalHashtags]),
      ];

      // Extract mentions
      enhanced.mentions = DouyinParser.extractMentions(enhanced.content);

      // Clean content
      enhanced.content = DouyinParser.cleanText(enhanced.content);
    }

    // Add search context to metadata
    enhanced.metadata = {
      ...enhanced.metadata,
      searchKeyword: keyword,
      extractedAt: new Date().toISOString(),
      platform: "douyin",
    };

    return enhanced;
  }

  public async downloadMedia(
    mediaUrl: string,
    outputPath: string,
  ): Promise<boolean> {
    try {
      // Use the inherited media downloader
      const mediaItem = {
        type: "video" as const,
        url: mediaUrl,
        format: "mp4",
      };

      const result = await this.mediaDownloader.downloadMedia(
        mediaItem,
        "temp",
        "douyin",
      );

      return result.success;
    } catch (error) {
      crawlerLogger.error("Media download failed", {
        mediaUrl,
        outputPath,
        error,
      });
      return false;
    }
  }
}
