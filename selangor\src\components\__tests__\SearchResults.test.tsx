import { render, screen } from '@testing-library/react'
import type { SearchResponse, TextResult } from '@/types/search'
import { SearchResults } from '../SearchResults'

// Mock data for testing
const mockSearchResult: TextResult = {
  id: 'test-1',
  text: 'This is a test search result about halal certification requirements.',
  type: 'vector',
  document_id: 'doc-123',
  score: 0.85,
  wordCount: 12,
  metadata: {
    title: 'Halal Certification Guidelines',
    url: 'https://example.com/halal-guidelines.pdf',
    source: 'halal-guidelines.pdf',
    chunk_index: 0,
    document_type: 'PDF',
  },
}

const mockSearchResponse: SearchResponse = {
  query: 'halal certification',
  results: [mockSearchResult],
  totalChunks: 1,
  totalGraphResults: 0,
  options: {
    retrieveDocument: true,
    maxWordCount: 3000,
    includeGraph: false,
    minScore: 0.2,
    limit: 10,
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    hasMore: false,
  },
}

describe('SearchResults', () => {
  it('renders search results with file information prominently displayed', () => {
    render(<SearchResults searchResponse={mockSearchResponse} />)

    // Check that the search query is displayed
    expect(
      screen.getByText('Search Results for "halal certification"')
    ).toBeInTheDocument()

    // Check that the file information is prominently displayed
    expect(screen.getByText('From: halal-guidelines.pdf')).toBeInTheDocument()
    expect(screen.getByText('PDF')).toBeInTheDocument()

    // Check that the title is displayed
    expect(
      screen.getByText('Halal Certification Guidelines')
    ).toBeInTheDocument()

    // Check that the score is displayed
    expect(screen.getByText('85%')).toBeInTheDocument()

    // Check that the content is displayed (text may be split by highlighting)
    expect(
      screen.getByText(/This is a test search result about/)
    ).toBeInTheDocument()
    expect(screen.getByText(/requirements/)).toBeInTheDocument()

    // Check that the "View Original" link is present
    expect(screen.getByText('View Original')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /View Original/ })).toHaveAttribute(
      'href',
      'https://example.com/halal-guidelines.pdf'
    )

    // Check that additional metadata is displayed
    expect(screen.getByText('12 words')).toBeInTheDocument()
    expect(screen.getByText('Chunk 1')).toBeInTheDocument()
  })

  it('handles search results without file metadata gracefully', () => {
    const resultWithoutMetadata: TextResult = {
      ...mockSearchResult,
      metadata: undefined,
    }

    const responseWithoutMetadata: SearchResponse = {
      ...mockSearchResponse,
      results: [resultWithoutMetadata],
    }

    render(<SearchResults searchResponse={responseWithoutMetadata} />)

    // Should still render the content (text may be split by highlighting)
    expect(
      screen.getByText(/This is a test search result about/)
    ).toBeInTheDocument()
    expect(screen.getByText(/requirements/)).toBeInTheDocument()

    // Should not show file information section when no metadata
    expect(screen.queryByText(/From:/)).not.toBeInTheDocument()
  })

  it('displays correct file icons for different file types', () => {
    const pdfResult: TextResult = {
      ...mockSearchResult,
      metadata: {
        ...mockSearchResult.metadata,
        source: 'document.pdf',
        document_type: 'PDF',
      },
    }

    const docResult: TextResult = {
      ...mockSearchResult,
      id: 'test-2',
      metadata: {
        ...mockSearchResult.metadata,
        source: 'document.docx',
        document_type: 'Word Document',
      },
    }

    const webResult: TextResult = {
      ...mockSearchResult,
      id: 'test-3',
      metadata: {
        ...mockSearchResult.metadata,
        source: 'https://example.com/page',
        url: 'https://example.com/page',
      },
    }

    const responseWithMultipleTypes: SearchResponse = {
      ...mockSearchResponse,
      results: [pdfResult, docResult, webResult],
    }

    render(<SearchResults searchResponse={responseWithMultipleTypes} />)

    // Check that different file types are displayed
    expect(screen.getByText('From: document.pdf')).toBeInTheDocument()
    expect(screen.getByText('From: document.docx')).toBeInTheDocument()
    expect(screen.getByText('From: example.com')).toBeInTheDocument()
  })
})
