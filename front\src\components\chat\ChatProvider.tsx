'use client'

import type React from 'react'
import { createContext, useContext, useEffect, useState } from 'react'
import { usePathname } from '@/hooks/usePathname'

interface ChatContextType {
  isFloatingChatEnabled: boolean
  setFloatingChatEnabled: (enabled: boolean) => void
  unreadCount: number
  setUnreadCount: (count: number) => void
  isStaffLoggedIn: boolean
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function useChatContext() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider')
  }
  return context
}

interface ChatProviderProps {
  children: React.ReactNode
}

export function ChatProvider({ children }: ChatProviderProps) {
  const pathname = usePathname()
  const [unreadCount, setUnreadCount] = useState(0)
  const [isStaffLoggedIn, setIsStaffLoggedIn] = useState(false)

  // Check if user is logged in as staff (admin, agent, supervisor)
  useEffect(() => {
    const checkStaffAuth = () => {
      if (typeof window === 'undefined') {
        return
      }

      const adminToken = localStorage.getItem('admin_token')
      const agentToken = localStorage.getItem('agent_token')

      // Check if user is logged in as admin
      if (adminToken) {
        setIsStaffLoggedIn(true)
        return
      }

      // Check if user is logged in as agent/supervisor
      if (agentToken) {
        const agentUserData = localStorage.getItem('agent_user')
        if (agentUserData) {
          try {
            const parsedUser = JSON.parse(agentUserData)
            // Check if role is agent or supervisor
            if (
              parsedUser.role === 'AGENT' ||
              parsedUser.role === 'SUPERVISOR'
            ) {
              setIsStaffLoggedIn(true)
              return
            }
          } catch (error) {
            console.error('Error parsing agent user data:', error)
          }
        }
      }

      setIsStaffLoggedIn(false)
    }

    // Check on mount
    checkStaffAuth()

    // Listen for storage changes (when user logs in/out in another tab)
    const handleStorageChange = () => {
      checkStaffAuth()
    }

    window.addEventListener('storage', handleStorageChange)

    // Also check periodically in case localStorage changes in same tab
    const interval = setInterval(checkStaffAuth, 1000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(interval)
    }
  }, [])

  // Disable floating chat on the dedicated chat page, admin/agent pages, or if staff is logged in
  // Check for chat pages: /chat and /[locale]/chat/[botSlug] patterns
  // Check for admin/agent pages: /admin/, /agent/, /[locale]/admin/, /[locale]/agent/
  const isChatPage = pathname === '/chat' || pathname.includes('/chat/')
  const isAdminPage = pathname.includes('/admin/') || pathname.includes('/cp1')
  const isAgentPage = pathname.includes('/agent/')
  const isStaffPage = isChatPage || isAdminPage || isAgentPage
  const isFloatingChatEnabled = !isStaffPage && !isStaffLoggedIn

  const value = {
    isFloatingChatEnabled,
    setFloatingChatEnabled: () => {}, // Not needed anymore since it's computed
    unreadCount,
    setUnreadCount,
    isStaffLoggedIn,
  }

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}
