'use client'

export const runtime = 'edge'

import {
  ArrowLeft,
  Building,
  Clock,
  Contact,
  Edit3,
  Globe,
  Mail,
  MapPin,
  Phone,
  Trash2,
  User,
} from 'lucide-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link } from '@/i18n/navigation'
import { api } from '@/lib/api'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

interface ContactInfo {
  id: number
  name: string
  title: string
  department: string
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email: string
  phone?: string
  mobile?: string
  address?: string
  city?: string
  state?: string
  postcode?: string
  country?: string
  website?: string
  workingHours?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function ContactViewPage() {
  const router = useRouter()
  const params = useParams()
  const contactId = params.id ? Number.parseInt(params.id as string, 10) : null

  const [contact, setContact] = useState<ContactInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (contactId) {
      fetchContact()
    }
  }, [contactId])

  const fetchContact = async () => {
    if (!contactId) return

    try {
      setIsLoading(true)
      setError(null)
      const response = await api.get<{ data: ContactInfo }>(
        `/admin/contacts/${contactId}`
      )
      setContact(response.data)
    } catch (err: any) {
      console.error('Error fetching contact:', err)
      setError(
        err.response?.data?.error || err.message || 'Failed to load contact'
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!contact || !confirm('Are you sure you want to delete this contact?')) {
      return
    }

    try {
      await api.delete(`/admin/contacts/${contact.id}`)
      router.push('/admin/contact')
    } catch (err: any) {
      console.error('Error deleting contact:', err)
      setError(
        err.response?.data?.error || err.message || 'Failed to delete contact'
      )
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'general':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'support':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'sales':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'technical':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'emergency':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contact">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Contact Details
            </h1>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading contact...</div>
        </div>
      </div>
    )
  }

  if (error || !contact) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contact">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Contact Details
            </h1>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <span>{error || 'Contact not found'}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contact">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {contact.name}
            </h1>
            <p className="text-gray-600 mt-2">Contact Details</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/contact/${contact.id}/edit`}>
            <Button>
              <Edit3 className="mr-2 h-4 w-4" />
              Edit Contact
            </Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Name</label>
              <p className="text-lg font-medium">{contact.name}</p>
            </div>

            {contact.title && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Title
                </label>
                <p>{contact.title}</p>
              </div>
            )}

            {contact.department && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Department
                </label>
                <p>{contact.department}</p>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-500">Type</label>
              <div className="mt-1">
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium border ${getTypeColor(contact.type)}`}
                >
                  {contact.type.toUpperCase()}
                </span>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Status
              </label>
              <div className="mt-1">
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    contact.isActive
                      ? 'text-green-600 bg-green-50 border border-green-200'
                      : 'text-gray-600 bg-gray-50 border border-gray-200'
                  }`}
                >
                  {contact.isActive ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Contact className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-400" />
              <a
                href={`mailto:${contact.email}`}
                className="text-blue-600 hover:text-blue-700"
              >
                {contact.email}
              </a>
            </div>

            {contact.phone && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <a
                  href={`tel:${contact.phone}`}
                  className="text-blue-600 hover:text-blue-700"
                >
                  {contact.phone}
                </a>
                <span className="text-sm text-gray-500">(Phone)</span>
              </div>
            )}

            {contact.mobile && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <a
                  href={`tel:${contact.mobile}`}
                  className="text-blue-600 hover:text-blue-700"
                >
                  {contact.mobile}
                </a>
                <span className="text-sm text-gray-500">(Mobile)</span>
              </div>
            )}

            {contact.website && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-gray-400" />
                <a
                  href={contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {contact.website}
                </a>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Address Information */}
      {(contact.address ||
        contact.city ||
        contact.state ||
        contact.postcode ||
        contact.country) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Address Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {contact.address && <p>{contact.address}</p>}
              <div className="flex gap-2 text-sm text-gray-600">
                {contact.city && <span>{contact.city}</span>}
                {contact.state && <span>{contact.state}</span>}
                {contact.postcode && <span>{contact.postcode}</span>}
              </div>
              {contact.country && (
                <p className="text-sm text-gray-600">{contact.country}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Working Hours */}
      {contact.workingHours && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Working Hours
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{contact.workingHours}</p>
          </CardContent>
        </Card>
      )}

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Metadata</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div>
            <label className="text-sm font-medium text-gray-500">Created</label>
            <p className="text-sm">
              {new Date(contact.createdAt).toLocaleString()}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">
              Last Updated
            </label>
            <p className="text-sm">
              {new Date(contact.updatedAt).toLocaleString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
