{"name": "sabah-tourism-crawler", "version": "1.0.0", "description": "Comprehensive social media crawler for Douyin and other platforms using Stagehand", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "crawl": "tsx src/index.ts crawl", "crawl:douyin": "tsx scripts/crawl-douyin.ts", "setup": "tsx scripts/setup-db.ts", "setup:db": "tsx scripts/setup-db.ts", "report": "tsx scripts/generate-report.ts", "generate:report": "tsx scripts/generate-report.ts", "status": "tsx src/index.ts status", "test": "bun test", "test:unit": "bun test tests/unit", "test:integration": "bun test tests/integration", "test:watch": "bun test --watch", "clean": "rm -rf dist output/*/ reports/*", "clean:all": "rm -rf dist output reports logs data node_modules", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "example": "tsx scripts/example-crawl.ts", "benchmark": "tsx scripts/benchmark.ts"}, "keywords": ["crawler", "social-media", "do<PERSON><PERSON>", "stagehand", "automation", "scraping"], "author": "Sabah Tourism", "license": "MIT", "dependencies": {"@browserbasehq/stagehand": "^1.12.0", "axios": "^1.6.2", "better-sqlite3": "^12.2.0", "chalk": "^5.3.0", "cheerio": "^1.0.0-rc.12", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "mime-types": "^2.1.35", "progress": "^2.0.3", "sharp": "^0.33.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.5", "@types/progress": "^2.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}