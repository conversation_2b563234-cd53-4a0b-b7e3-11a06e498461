import { Hono } from 'hono'
import { TwilioController } from '../controllers/twilioController'

const twilio = new Hono()

// Initialize controller
const twilioController = new TwilioController()

/**
 * Modern Hono.js Twilio Routes
 * Clean, type-safe route handlers with proper Hono.js patterns
 */

// GET /webhook - Webhook verification endpoint
twilio.get('/webhook', (c) => {
  console.log('[Twilio Router] GET webhook verification endpoint accessed')
  return c.text('Webhook OK')
})

// POST /webhook - Main webhook handler
twilio.post('/webhook', async (c) => {
  try {
    console.log('[Twilio Router] POST webhook handler called')

    // Extract siteId from request path or headers
    const siteId = c.req.header('x-site-id') || '1' // Default to site 1
    console.log(`[Twilio Router] Processing webhook for siteId: ${siteId}`)

    // Call controller with Hono context - controller handles DB initialization
    return await twilioController.handleWebhook(c, siteId)
  } catch (error: any) {
    console.error('[Twilio Router] Unexpected error:', {
      error: error.message,
      stack: error.stack,
    })

    return c.json(
      {
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      },
      500,
    )
  }
})

// GET /webhook-url - Get webhook URL endpoint
twilio.get('/webhook-url', async (c) => {
  try {
    console.log('[Twilio Router] GET webhook-url endpoint accessed')
    return await twilioController.getWebhookUrl(c)
  } catch (error: any) {
    console.error('[Twilio Router] Error getting webhook URL:', error)
    return c.json(
      {
        error: 'Failed to get webhook URL',
        timestamp: new Date().toISOString(),
      },
      500,
    )
  }
})

export default twilio
