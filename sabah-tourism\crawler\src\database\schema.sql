-- Social media posts (shared across platforms)
CREATE TABLE IF NOT EXISTS social_posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform TEXT NOT NULL,           -- 'douyin', 'tiktok', 'instagram', etc.
    post_id TEXT NOT NULL,            -- Platform-specific post ID
    url TEXT NOT NULL,                -- Original post URL
    title TEXT,                       -- Post title/caption
    content TEXT,                     -- Post text content
    author_username TEXT,             -- Author username
    author_display_name TEXT,         -- Author display name
    author_avatar_url TEXT,           -- Author profile picture
    posted_at DATETIME,               -- When post was published
    crawled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    tags TEXT,                        -- JSON array of hashtags
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    metadata TEXT,                    -- JSON metadata specific to platform
    UNIQUE(platform, post_id)
);

-- Media files associated with posts
CREATE TABLE IF NOT EXISTS media_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER REFERENCES social_posts(id),
    media_type TEXT NOT NULL,         -- 'image', 'video', 'audio'
    file_name TEXT NOT NULL,          -- Local file name
    file_path TEXT NOT NULL,          -- Relative path to file
    original_url TEXT,                -- Original media URL
    file_size INTEGER,                -- File size in bytes
    duration INTEGER,                 -- For video/audio (seconds)
    width INTEGER,                    -- For images/videos
    height INTEGER,                   -- For images/videos
    downloaded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Crawling sessions and status tracking
CREATE TABLE IF NOT EXISTS crawl_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform TEXT NOT NULL,
    keywords TEXT NOT NULL,           -- JSON array of search keywords
    status TEXT NOT NULL,             -- 'running', 'completed', 'failed', 'paused'
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    total_posts INTEGER DEFAULT 0,
    successful_posts INTEGER DEFAULT 0,
    failed_posts INTEGER DEFAULT 0,
    error_message TEXT,
    config TEXT                       -- JSON crawl configuration
);

-- Individual crawl attempts (for resume functionality)
CREATE TABLE IF NOT EXISTS crawl_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER REFERENCES crawl_sessions(id),
    post_url TEXT NOT NULL,
    keyword TEXT,                     -- Which keyword led to this post
    status TEXT NOT NULL,             -- 'pending', 'success', 'failed', 'skipped'
    error_message TEXT,
    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
);

-- Search keywords tracking
CREATE TABLE IF NOT EXISTS search_keywords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    keyword TEXT NOT NULL UNIQUE,
    platform TEXT NOT NULL,
    last_crawled DATETIME,
    total_posts_found INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_posts_platform ON social_posts(platform);
CREATE INDEX IF NOT EXISTS idx_social_posts_crawled_at ON social_posts(crawled_at);
CREATE INDEX IF NOT EXISTS idx_social_posts_author ON social_posts(author_username);
CREATE INDEX IF NOT EXISTS idx_media_files_post_id ON media_files(post_id);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_platform ON crawl_sessions(platform);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_status ON crawl_sessions(status);
CREATE INDEX IF NOT EXISTS idx_crawl_attempts_session_id ON crawl_attempts(session_id);
CREATE INDEX IF NOT EXISTS idx_crawl_attempts_status ON crawl_attempts(status);
CREATE INDEX IF NOT EXISTS idx_search_keywords_platform ON search_keywords(platform);
