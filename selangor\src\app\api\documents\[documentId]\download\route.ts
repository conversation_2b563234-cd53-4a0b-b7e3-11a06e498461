import { type NextRequest, NextResponse } from 'next/server'
import { R2RService } from '@/lib/r2r'

export const runtime = 'edge'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ documentId: string }> }
) {
  try {
    const { documentId } = await params

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    const r2rService = new R2RService()

    // Get document information first
    const documentInfo = await r2rService.getDocument(documentId)

    if (!documentInfo?.results) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 })
    }

    const document = documentInfo.results

    // Try to get the document content/file from R2R
    try {
      // Try to construct a direct URL to the R2R file endpoint
      const r2rBaseUrl = process.env.R2R_URL || 'http://localhost:7272'
      const fileUrl = `${r2rBaseUrl}/v2/documents/${documentId}/download`

      const response = await fetch(fileUrl, {
        headers: {
          Authorization: `Bearer ${process.env.R2R_API_KEY || ''}`,
        },
      })

      if (response.ok) {
        const fileBuffer = await response.arrayBuffer()
        const headers = new Headers()
        headers.set('Content-Type', 'application/pdf')
        headers.set(
          'Content-Disposition',
          `attachment; filename="${document.title || 'document.pdf'}"`
        )

        return new NextResponse(fileBuffer, {
          status: 200,
          headers,
        })
      }
      console.error(
        'R2R download failed:',
        response.status,
        response.statusText
      )
    } catch (error) {
      console.error('R2R download error:', error)
    }

    // If direct download fails, try to redirect to the original URL if available
    if (document.metadata?.url) {
      return NextResponse.redirect(document.metadata.url)
    }

    // If no download method works, return document info as JSON
    return NextResponse.json(
      {
        error: 'Document file not available for download',
        document: {
          id: document.id,
          title: document.title,
          summary: document.summary,
          documentType: document.documentType,
          sizeInBytes: document.sizeInBytes,
        },
        message:
          'The PDF file is not directly downloadable. You can view the document content in the search results.',
      },
      { status: 404 }
    )
  } catch (error) {
    console.error('Document download error:', error)
    return NextResponse.json(
      { error: 'Failed to download document' },
      { status: 500 }
    )
  }
}
