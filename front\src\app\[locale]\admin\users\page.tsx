'use client'

export const runtime = 'edge'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Edit3,
  MoreHorizontal,
  PlusCircle,
  ShieldCheck,
  Trash2,
  <PERSON>r<PERSON><PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  UserX,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { <PERSON>, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'
import type { AdminUserResponse } from '@/types' // Assuming types are updated
import type { UserRole } from '@/types/roles'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

// Helper to determine current user's role (example, adapt as needed)
const _getCurrentUserRole = (): UserRole | null => {
  // This would ideally come from a global state/context after login
  // For now, let's assume it's fetched or available somehow.
  // As a placeholder, this component will fetch /me if not available globally.
  return null
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<AdminUserResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const _router = useRouter()
  const [currentUser, setCurrentUser] = useState<{ role?: UserRole } | null>(
    null
  )

  useEffect(() => {
    const fetchCurrentUserAndUsers = async () => {
      setLoading(true)
      try {
        // Fetch current user to check role for page access
        // This check should ideally be in a layout or higher-order component for proper route protection
        const meResponse = await api.admin.getMe()
        if (meResponse.user?.role !== 'ADMIN') {
          setError(
            'Access Denied: You do not have permission to view this page.'
          )
          setLoading(false)
          // Optionally redirect: router.push('/admin/dashboard');
          return
        }
        setCurrentUser(meResponse.user)

        const response = await api.admin.listUsers()
        // The actual data is often in a 'data' property if ApiResponse wraps it
        // Adjust based on actual apiRequest and ApiResponse structure
        if (Array.isArray(response)) {
          // Direct array response
          setUsers(response)
        } else if (response.data && Array.isArray(response.data)) {
          // Wrapped in { data: [...] }
          setUsers(response.data)
        } else if (response.success && Array.isArray(response.data)) {
          // Wrapped in { success: true, data: [...] }
          setUsers(response.data)
        } else {
          console.warn('Unexpected response structure for listUsers:', response)
          setUsers([]) // Set to empty array if structure is not as expected
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch users.')
        console.error(err)
      } finally {
        setLoading(false)
      }
    }
    fetchCurrentUserAndUsers()
  }, [])

  const handleDeleteUser = async (userId: number, username: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete user "${username}"? This action cannot be undone.`
      )
    ) {
      try {
        await api.admin.deleteUser(userId)
        setUsers(prevUsers => prevUsers.filter(user => user.id !== userId))
        // Add success notification if available
      } catch (err: any) {
        setError(err.message || 'Failed to delete user.')
        console.error(err)
        // Add error notification
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  // If not ADMIN, this part should not be reached due to the check above.
  // But as a safeguard or if the check is moved, this can be added.
  if (currentUser?.role !== 'ADMIN') {
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <AlertTriangle className="inline-block mr-2" />
          Access Denied. You need administrator privileges to manage users.
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          User Management
        </h1>
        <Link
          href="/admin/users/new"
          className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out"
        >
          <PlusCircle size={20} className="mr-2" />
          Add New User
        </Link>
      </div>
      {users.length === 0 ? (
        <div className="text-center py-10">
          <UserCog size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500 text-lg">No users found.</p>
          <p className="text-gray-400 mt-1">Why not add the first one?</p>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Username
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Email
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Roles
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Created At
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Last Login
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map(user => (
                <tr
                  key={user.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {user.username}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {user.firstName || user.lastName
                        ? `${user.firstName || ''} ${user.lastName || ''}`.trim()
                        : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {user.email || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {user.roles?.map(role => (
                        <span
                          key={role}
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            role === 'ADMIN'
                              ? 'bg-red-100 text-red-800'
                              : role === 'SUPERVISOR'
                                ? 'bg-purple-100 text-purple-800'
                                : role === 'AGENT'
                                  ? 'bg-blue-100 text-blue-800'
                                  : role === 'EDITOR'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {role === 'ADMIN' && (
                            <ShieldCheck
                              size={14}
                              className="mr-1 inline-block"
                            />
                          )}
                          {role}
                        </span>
                      )) || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {user.isActive ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        <UserCheck size={14} className="mr-1 inline-block" />
                        Active
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                        <UserX size={14} className="mr-1 inline-block" />
                        Inactive
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.lastLoginAt
                      ? new Date(user.lastLoginAt).toLocaleString()
                      : 'Never'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <Link
                        href={`/admin/users/${user.id}`}
                        className="text-indigo-600 hover:text-indigo-900 transition duration-150"
                        title="Edit User"
                      >
                        <Edit3 size={18} className="inline-block" />
                      </Link>
                      <Link
                        href={`/admin/users/${user.id}/clone`}
                        className="text-green-600 hover:text-green-900 transition duration-150"
                        title="Clone User"
                      >
                        <Copy size={18} className="inline-block" />
                      </Link>
                      <button
                        type="button"
                        onClick={() => handleDeleteUser(user.id, user.username)}
                        className="text-red-600 hover:text-red-900 transition duration-150"
                        title="Delete User"
                      >
                        <Trash2 size={18} className="inline-block" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <div className="mt-6">
        <Link
          href="/admin/dashboard"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <title>Back arrow</title>
            <path
              fillRule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
          Back to Dashboard
        </Link>
      </div>
    </div>
  )
}
