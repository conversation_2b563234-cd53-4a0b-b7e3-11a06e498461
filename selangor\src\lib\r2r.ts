import type {
  ParseR2rOptions,
  R2rSearchResult,
  TextResult,
} from '@/types/search'

export class R2RService {
  private baseUrl: string
  private apiKey: string

  constructor() {
    this.baseUrl = process.env.R2R_URL || ''
    this.apiKey = process.env.R2R_API_KEY || ''

    if (!this.baseUrl) {
      console.error('R2R_URL is not defined')
      throw new Error('R2R_URL is required')
    }
  }

  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`
    const headers = {
      'Content-Type': 'application/json',
      ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
      ...options.headers,
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      throw new Error(
        `R2R API error: ${response.status} ${response.statusText}`
      )
    }

    return response.json()
  }

  async search(
    query: string,
    options: {
      retrieveDocument?: boolean
      maxWordCount?: number
      collectionId?: string
      limit?: number
    } = {}
  ): Promise<{
    chunks: TextResult[]
  }> {
    const collectionId = options.collectionId || process.env.R2R_COLLECTION_ID

    try {
      const searchPayload = {
        query: query,
        search_settings: {
          limit: options.limit || 20,
          ...(collectionId && {
            filters: {
              collection_ids: {
                $in: [collectionId],
              },
            },
          }),
        },
      }

      const r2rSearchRes = await this.makeRequest('/v3/retrieval/search', {
        method: 'POST',
        body: JSON.stringify(searchPayload),
      })

      // Extract chunks from the search response (excluding graph search as requested)
      let chunks: TextResult[] =
        r2rSearchRes.results?.chunk_search_results?.map((c: any) => ({
          ...c,
          text: c.text,
          type: 'vector',
          document_id: c.document_id || c.documentId,
          score: c.score,
          wordCount: 0, // Will be calculated in parseR2rResult
          metadata: {
            title: c.metadata?.title,
            url: c.metadata?.url || c.metadata?.source_url,
            source: c.metadata?.source,
            chunk_index: c.metadata?.chunk_index,
            document_type: c.metadata?.document_type,
            ...c.metadata,
          },
        })) || []

      // Enrich with document information if requested
      if (options.retrieveDocument && chunks.length > 0) {
        chunks = await this.enrichWithDocumentInfo(chunks)
      }

      return { chunks }
    } catch (error) {
      console.error('R2R search error:', error)
      throw error
    }
  }

  async getDocument(documentId: string): Promise<any> {
    try {
      // Try to get document details using the documents endpoint
      const document = await this.makeRequest(`/v3/documents/${documentId}`)
      return document
    } catch (error) {
      console.error('R2R get document error for ID', documentId, ':', error)
      return null
    }
  }

  async enrichWithDocumentInfo(chunks: TextResult[]): Promise<TextResult[]> {
    // Get unique document IDs
    const uniqueDocIds = [
      ...new Set(chunks.map(chunk => chunk.document_id).filter(Boolean)),
    ]

    // Fetch document info for each unique document ID
    const documentInfoMap = new Map()

    for (const docId of uniqueDocIds) {
      try {
        const docInfo = await this.getDocument(docId as string)
        if (docInfo?.results) {
          documentInfoMap.set(docId, docInfo.results)
        }
      } catch (error) {
        console.error(`Failed to fetch document info for ${docId}:`, error)
      }
    }

    // Enrich chunks with document information
    return chunks.map(chunk => {
      const docInfo = documentInfoMap.get(chunk.document_id)
      if (docInfo) {
        return {
          ...chunk,
          metadata: {
            ...chunk.metadata,
            title:
              docInfo.title || docInfo.metadata?.title || chunk.metadata?.title,
            summary: docInfo.summary || docInfo.metadata?.summary,
            url: docInfo.metadata?.url || chunk.metadata?.url,
            source: docInfo.metadata?.source || chunk.metadata?.source,
          },
        }
      }
      return chunk
    })
  }

  async parseR2rResult(
    chunks: TextResult[],
    options: ParseR2rOptions
  ): Promise<R2rSearchResult> {
    let allResults: TextResult[] = [...chunks]

    // Filter by minimum score
    allResults = allResults.filter(result => result.score >= options.minScore)

    // Sort by score (highest first)
    allResults.sort((a, b) => b.score - a.score)

    // Calculate word counts and apply word limit
    let totalWordCount = 0
    const filteredResults: TextResult[] = []

    for (const result of allResults) {
      const wordCount = result.text.split(/\s+/).length
      result.wordCount = wordCount

      if (totalWordCount + wordCount <= options.maxWordCount) {
        filteredResults.push(result)
        totalWordCount += wordCount
      } else {
        // If adding this result would exceed the limit, truncate it
        const remainingWords = options.maxWordCount - totalWordCount
        if (remainingWords > 0) {
          const words = result.text.split(/\s+/)
          const truncatedText = words.slice(0, remainingWords).join(' ')
          filteredResults.push({
            ...result,
            text: truncatedText,
            wordCount: remainingWords,
          })
          totalWordCount = options.maxWordCount
        }
        break
      }
    }

    // Apply limit
    const limitedResults = filteredResults.slice(0, options.limit)

    return {
      texts: limitedResults,
      totalWordCount,
    }
  }
}

export default R2RService
