'use client'

import { useState, useEffect } from 'react'
import { ChevronDown, X } from 'lucide-react'

// Malaysian states in ISO 3166-2:MY format
const MALAYSIAN_STATES = [
  { code: 'MY-01', name: '<PERSON><PERSON>' },
  { code: 'MY-02', name: '<PERSON><PERSON>' },
  { code: 'MY-03', name: '<PERSON><PERSON><PERSON>' },
  { code: 'MY-04', name: '<PERSON><PERSON>' },
  { code: 'MY-05', name: '<PERSON><PERSON><PERSON> Se<PERSON>n' },
  { code: 'MY-06', name: '<PERSON><PERSON>' },
  { code: 'MY-07', name: 'Penang' },
  { code: 'MY-08', name: '<PERSON><PERSON>' },
  { code: 'MY-09', name: '<PERSON><PERSON>' },
  { code: 'MY-10', name: 'Selangor' },
  { code: 'MY-11', name: 'Terengganu' },
  { code: 'MY-12', name: 'Sabah' },
  { code: 'MY-13', name: 'Sarawak' },
  { code: 'MY-14', name: 'Kuala Lumpur' },
  { code: 'MY-15', name: '<PERSON><PERSON>' },
  { code: 'MY-16', name: '<PERSON><PERSON><PERSON>' },
]

interface StateFilterProps {
  selectedState: string
  onStateChange: (state: string) => void
  className?: string
}

interface StateCount {
  state: string
  count: number
}

export default function StateFilter({
  selectedState,
  onStateChange,
  className = '',
}: StateFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [stateCounts, setStateCounts] = useState<StateCount[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch state counts from API
  useEffect(() => {
    async function fetchStateCounts() {
      try {
        setLoading(true)
        const response = await fetch('/api/states?type=companies')
        if (!response.ok) {
          throw new Error('Failed to fetch state data')
        }
        const data = await response.json()

        // Handle both old and new response formats
        if (data.success === false) {
          throw new Error(data.error || 'Failed to fetch state data')
        }

        // Extract data from standardized response or use legacy format
        const stateData = data.success ? data.data : data
        setStateCounts(stateData?.states || [])
      } catch (err) {
        console.error('Error fetching state counts:', err)
        setError('Failed to load state data')
      } finally {
        setLoading(false)
      }
    }

    fetchStateCounts()
  }, [])

  // Get display name for selected state
  const getSelectedStateName = () => {
    if (!selectedState) return 'All States'

    const state = MALAYSIAN_STATES.find(s => s.code === selectedState)
    return state ? state.name : selectedState
  }

  // Get count for a state
  const getStateCount = (stateCode: string) => {
    const stateData = stateCounts.find(s => s.state === stateCode)
    return stateData ? stateData.count : 0
  }

  // Handle state selection
  const handleStateSelect = (stateCode: string) => {
    onStateChange(stateCode)
    setIsOpen(false)
  }

  // Clear selection
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onStateChange('')
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent transition-colors"
          disabled={loading}
        >
          <span className="text-left flex-1">
            {loading ? (
              <span className="text-gray-500">Loading states...</span>
            ) : error ? (
              <span className="text-red-500">Error loading states</span>
            ) : (
              <span
                className={selectedState ? 'text-gray-900' : 'text-gray-500'}
              >
                {getSelectedStateName()}
              </span>
            )}
          </span>

          <ChevronDown
            className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Clear button - separate from main button to avoid nesting */}
        {selectedState && (
          <button
            onClick={handleClear}
            className="absolute right-8 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded-full transition-colors focus:outline-none z-10"
            title="Clear selection"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        )}

        {/* Dropdown */}
        {isOpen && !loading && !error && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-64 overflow-y-auto">
            {/* All States option */}
            <button
              onClick={() => handleStateSelect('')}
              className={`w-full px-4 py-2 text-left hover:bg-gray-50 transition-colors ${
                !selectedState
                  ? 'bg-primary-green text-white hover:bg-primary-green-dark'
                  : ''
              }`}
            >
              <span className="font-medium">All States</span>
              <span className="text-sm text-gray-500 ml-2">
                ({stateCounts.reduce((total, state) => total + state.count, 0)})
              </span>
            </button>

            {/* Individual states */}
            {MALAYSIAN_STATES.map(state => {
              const count = getStateCount(state.code)
              const isSelected = selectedState === state.code

              return (
                <button
                  key={state.code}
                  onClick={() => handleStateSelect(state.code)}
                  className={`w-full px-4 py-2 text-left hover:bg-gray-50 transition-colors ${
                    isSelected
                      ? 'bg-primary-green text-white hover:bg-primary-green-dark'
                      : ''
                  }`}
                  disabled={count === 0}
                >
                  <span
                    className={`font-medium ${count === 0 ? 'text-gray-400' : ''}`}
                  >
                    {state.name}
                  </span>
                  <span
                    className={`text-sm ml-2 ${
                      isSelected
                        ? 'text-green-100'
                        : count === 0
                          ? 'text-gray-400'
                          : 'text-gray-500'
                    }`}
                  >
                    ({count})
                  </span>
                </button>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
