import { NextRequest, NextResponse } from 'next/server'
import R2RService from '@/lib/r2r'

export const runtime = 'edge'

interface SummaryChunk {
  text: string
  score: number
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')

  console.log('🤖 Summary: Received summary search query:', query)

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is missing' },
      { status: 400 }
    )
  }

  const { R2R_URL, R2R_COLLECTION_ID, OPENAI_API_KEY } = process.env

  if (!R2R_URL || !R2R_COLLECTION_ID) {
    console.error('R2R credentials not configured')
    return NextResponse.json(
      { error: 'R2R credentials not configured' },
      { status: 500 }
    )
  }

  if (!OPENAI_API_KEY) {
    console.error('OpenAI API key not configured')
    // Return a placeholder summary when OpenAI is not configured
    return NextResponse.json({
      query,
      summary:
        'AI is not available. Please configure the OpenAI API key to enable AI-powered summaries of search results.',
      chunks: [],
      totalChunks: 0,
      wordsUsed: 0,
    })
  }

  try {
    const r2rService = new R2RService()
    console.log('🤖 Summary: R2r searching...', {
      query,
      R2R_COLLECTION_ID,
      R2R_URL: process.env.R2R_URL,
    })
    // Search for chunks with minimum score of 0.2 and max 5000 words
    const searchResult = await r2rService.search(query, {
      retrieveDocument: true,
      maxWordCount: 5000,
      collectionId: R2R_COLLECTION_ID,
      limit: 50, // Get more chunks to filter by score
    })

    // Filter chunks by minimum score of 0.2
    const filteredChunks = searchResult.chunks
      .filter(chunk => chunk.score >= 0.2)
      .sort((a, b) => b.score - a.score) // Sort by highest score first

    console.log(
      `🤖 Summary:Found ${filteredChunks.length} chunks with score >= 0.2`
    )

    if (filteredChunks.length === 0) {
      return NextResponse.json({
        query,
        summary:
          '🤖 Summary: No relevant information found for your search query.',
        chunks: [],
      })
    }

    // Prepare chunks for LLM with total word limit of 5000
    let totalWords = 0
    const chunksForLLM: SummaryChunk[] = []

    for (const chunk of filteredChunks) {
      const wordCount = chunk.text.split(/\s+/).length
      if (totalWords + wordCount <= 5000) {
        chunksForLLM.push({
          text: chunk.text,
          score: chunk.score,
        })
        totalWords += wordCount
      } else {
        // Truncate the last chunk if needed
        const remainingWords = 5000 - totalWords
        if (remainingWords > 0) {
          const words = chunk.text.split(/\s+/)
          const truncatedText = words.slice(0, remainingWords).join(' ')
          chunksForLLM.push({
            text: truncatedText,
            score: chunk.score,
          })
        }
        break
      }
    }

    console.log(
      `🤖 Summary: Prepared ${chunksForLLM.length} chunks for LLM (${totalWords} words)`
    )

    // Prepare the prompt for OpenAI
    const chunksText = chunksForLLM
      .map(chunk => JSON.stringify({ text: chunk.text, score: chunk.score }))
      .join('\n')

    const prompt = `You are an AI assistant for Halal Selangor, an organization that handles halal certification and compliance in Selangor, Malaysia. Provide answer based of the facts and findings from the results (answer only, do not mention about vector search) of question: '${query}', here are the vector results below:

${chunksText}

GUARD RAILS AND GUIDELINES:
1. SCOPE: Only provide information related to halal certification, halal compliance, halal products, halal establishments, and halal regulations in Selangor or Malaysia.
2. ACCURACY: Base your responses strictly on the search results provided. Do not make up information about halal certifications, expiry dates, or certification statuses.
3. DISCLAIMERS: For certification status inquiries, always remind users to verify current certification status directly with Halal Selangor or check the official halal directory.
4. SENSITIVE TOPICS: If asked about religious rulings (fatwa) or complex Islamic jurisprudence, direct users to consult with qualified Islamic scholars or religious authorities.
5. COMMERCIAL ADVICE: Do not provide business advice or recommendations for specific halal certification consultants or services.
6. LEGAL MATTERS: Do not provide legal advice regarding halal compliance violations or regulatory matters.

RESPONSE GUIDELINES:
- If the search term is not related to halal matters or the vector search results, respond with "No relevant halal information found for your search query. Please search for topics related to halal certification, halal products, or halal establishments in Selangor."
- If presented with a question, provide an answer based on the search results and general halal knowledge, but always include appropriate disclaimers.
- Focus on factual information about halal certification processes, requirements, certified establishments, and halal compliance guidelines.
- Present information in a clear, organized manner suitable for non-technical users.
- Use simple language and avoid technical jargon.
- Always maintain a helpful and respectful tone appropriate for serving the Muslim community and those seeking halal information.`

    // Call OpenAI API
    const openaiResponse = await fetch(
      'https://api.openai.com/v1/chat/completions',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: 1000,
          temperature: 0.7,
        }),
      }
    )

    if (!openaiResponse.ok) {
      throw new Error(
        `OpenAI API error: ${openaiResponse.status} ${openaiResponse.statusText}`
      )
    }

    const openaiResult = await openaiResponse.json()
    const summary =
      openaiResult.choices?.[0]?.message?.content ||
      'Unable to generate summary.'

    console.log('🤖 Summary: Generated summary for query:', query)

    return NextResponse.json({
      query,
      summary,
      chunks: chunksForLLM,
      totalChunks: filteredChunks.length,
      wordsUsed: totalWords,
    })
  } catch (error) {
    console.error('Summary generation error:', error)
    return NextResponse.json(
      {
        error: 'Failed to generate summary',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query } = body

    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 })
    }

    // Use the same logic as GET but with POST body parameters
    const searchParams = new URLSearchParams({
      q: query,
    })

    // Create a new request with the search params
    const getRequest = new NextRequest(
      `${request.url}?${searchParams.toString()}`,
      { method: 'GET' }
    )

    return await GET(getRequest)
  } catch (error) {
    console.error('Summary POST error:', error)
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
  }
}
