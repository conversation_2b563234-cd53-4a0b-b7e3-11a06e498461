# HalalMono Docker Environment Configuration
# Copy this file to .env.docker and fill in your values

# ===========================================
# REQUIRED: OpenAI Configuration
# ===========================================
OPENAI_API_KEY=your_openai_api_key_here

# ===========================================
# WhatsApp Business API (Optional)
# ===========================================
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id

# ===========================================
# R2R Service (Optional)
# ===========================================
R2R_URL=http://your-r2r-service-url

# ===========================================
# External APIs (Optional)
# ===========================================
# These are pre-configured with defaults, change if needed
NEXT_PUBLIC_EADUAN_URL=https://eaduan.islam.gov.my
NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=https://myehalal.halal.gov.my/domestik/v1/
NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=https://myehalal.halal.gov.my/international/v1/

# ===========================================
# Analytics (Optional)
# ===========================================
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# ===========================================
# Security Settings (Change in Production)
# ===========================================
JWT_SECRET=halal-chat-secret-key-2025-docker-change-in-production
ADMIN_DEFAULT_PASSWORD=admin123-change-in-production

# ===========================================
# Database Configuration (Auto-configured)
# ===========================================
# These are automatically set by Docker Compose
# DATABASE_URL=*********************************************************/halal_chat
# REDIS_URL=redis://:redis_password_2025@redis:6379

# ===========================================
# Service URLs (Auto-configured)
# ===========================================
# These are automatically set by Docker Compose
# FRONTEND_URL=http://localhost:16000
# ADMIN_URL=http://localhost:16005
# WEBHOOK_BASE_URL=http://localhost:16001

# ===========================================
# File Upload Configuration
# ===========================================
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# ===========================================
# Development Settings
# ===========================================
NODE_ENV=production
PORT=16001
JWT_EXPIRES_IN=365d
