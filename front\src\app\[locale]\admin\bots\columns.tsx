'use client'

import type { ColumnDef } from '@tanstack/react-table'
import { Edit, MoreHorizontal } from 'lucide-react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { Bot } from '@/types/bot'

function BotActionsCell({
  bot,
  onDelete,
}: {
  bot: Bot
  onDelete?: () => void
}) {
  const params = useParams()
  const locale = params.locale as string

  return (
    <DropdownMenu className="bg-white">
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent class="bg-white" align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(bot.id.toString())}
        >
          Copy Bot ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => window.open(`/${locale}/chat/${bot.slug}/`, '_blank')}
        >
          View Bot
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.location.href = `/${locale}/admin/bots/${bot.id}`
          }}
        >
          Edit Bot
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            window.location.href = `/${locale}/admin/bots/${bot.id}/clone`
          }}
        >
          Clone Bot
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={async () => {
            if (window.confirm('Are you sure you want to delete this bot?')) {
              try {
                const response = await fetch(`/api/admin/bots/${bot.id}`, {
                  method: 'DELETE',
                })

                if (response.ok) {
                  const result = await response.json()
                  if (result.success) {
                    onDelete?.()
                  } else {
                    alert(result.error || 'Failed to delete bot')
                  }
                } else {
                  const errorData = await response.json()
                  alert(errorData.error || 'Failed to delete bot')
                }
              } catch (error) {
                console.error('Error deleting bot:', error)
                alert('Error deleting bot')
              }
            }
          }}
          className="text-red-600 hover:text-red-700"
        >
          Delete Bot
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export const createColumns = (onDelete?: () => void): ColumnDef<Bot>[] => {
  const params = useParams()
  const locale = params.locale as string

  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'slug',
      header: 'Slug',
    },
    {
      accessorKey: 'provider',
      header: 'Provider',
      cell: ({ row }) => {
        const provider = row.getValue('provider') as string
        return <span className="capitalize">{provider}</span>
      },
    },
    {
      accessorKey: 'model',
      header: 'Model',
    },
    {
      accessorKey: 'temperature',
      header: 'Temperature',
      cell: ({ row }) => {
        const temp = row.getValue('temperature') as number
        return `${temp}%`
      },
    },
    {
      accessorKey: 'isDefault',
      header: 'Default',
      cell: ({ row }) => {
        const isDefault = row.getValue('isDefault') as boolean
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs ${
              isDefault
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            {isDefault ? 'Yes' : 'No'}
          </span>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => {
        const date = new Date(row.getValue('createdAt'))
        return date.toLocaleDateString()
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div>
          <Button
            variant="outline"
            onClick={() => {
              window.location.href = `/${locale}/admin/bots/${row.original.id}`
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <BotActionsCell bot={row.original} onDelete={onDelete} />
        </div>
      ),
    },
  ]
}
