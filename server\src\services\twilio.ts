import twilio from 'twilio'
import type {
  TwilioConfig, // Use the more specific runtime config type
  TwilioDbMessage,
  TwilioMessage,
  TwilioWebhookPayload,
} from '../types'
import type DatabaseService from './database' // Import DatabaseService type
import MediaProcessingService, {
  type AuthConfig,
  type StandardMediaMessage,
} from './mediaProcessingService'

// Removed local TwilioConfig interface, will use TwilioConfig from types

class TwilioService {
  private config: TwilioConfig | null = null
  private twilioClient: InstanceType<typeof twilio.Twilio> | null = null
  private dbService: DatabaseService | null = null
  private siteId: string | number | null = null
  private env: any = null
  private mediaService: MediaProcessingService | null = null

  constructor(
    dbService?: DatabaseService,
    siteId?: string | number,
    env?: any,
  ) {
    this.dbService = dbService || null
    this.siteId = siteId || null
    this.env = env || null

    // Initialize media service if database service is available
    if (this.dbService) {
      this.mediaService = new MediaProcessingService(this.dbService)
    }

    // loadConfig is now async, so we can't call it directly here without making constructor async
    // or using an async initialization pattern.
    // For now, config will be loaded on first use or via an explicit init method.
    // Or, make loadConfig synchronous for ENV fallback and async for DB.
    this.loadConfigSync() // Initial sync load from ENV as fallback
  }

  // public async initialize(): Promise<void> {
  //   await this.loadConfig();
  // }

  private loadConfigSync(): void {
    // Fallback to environment variables if DB service/siteId not present or DB load fails
    // Use env parameter (works with both Cloudflare Workers and Node.js/Bun)
    const envAccountSid = this.env?.TWILIO_ACCOUNT_SID
    const envAuthToken = this.env?.TWILIO_AUTH_TOKEN
    const envPhoneNumber = this.env?.TWILIO_PHONE_NUMBER

    if (envAccountSid && envAuthToken && envPhoneNumber) {
      this.config = {
        accountSid: envAccountSid,
        authToken: envAuthToken,
        phoneNumber: envPhoneNumber,
      }
      this.initializeTwilioClient()
      console.log(
        'TwilioService: Configuration loaded successfully from ENV as fallback.',
      )
    } else {
      console.warn(
        'TwilioService: ENV Configuration incomplete. Database config will be primary.',
      )
    }
  }

  public async loadConfig(): Promise<void> {
    if (this.dbService && this.siteId) {
      try {
        const dbConfig = await this.dbService.getTwilioConfig(this.siteId)
        if (dbConfig && dbConfig.isActive) {
          this.config = {
            accountSid: dbConfig.accountSid,
            authToken: dbConfig.authToken,
            phoneNumber: dbConfig.phoneNumber,
          }
          this.initializeTwilioClient()
          console.log(
            `TwilioService: Configuration loaded successfully from DB for site ${this.siteId}.`,
          )
          return
        }
        console.warn(
          `TwilioService: No active configuration found in DB for site ${this.siteId}.`,
        )
      } catch (error) {
        console.error(
          `TwilioService: Error loading configuration from DB for site ${this.siteId}:`,
          error,
        )
      }
    }
    // If DB load fails or no DB service/siteId, rely on ENV vars already loaded by loadConfigSync or nothing
    if (!this.config) {
      console.warn(
        'TwilioService: Configuration could not be loaded from DB or ENV. Service will not be operational.',
      )
    }
  }

  private initializeTwilioClient(): void {
    if (this.config) {
      this.twilioClient = twilio(this.config.accountSid, this.config.authToken)
    } else {
      this.twilioClient = null
    }
  }

  isConfigured(): boolean {
    return (
      !!this.config &&
      !!this.config.accountSid &&
      !!this.config.authToken &&
      !!this.config.phoneNumber &&
      !!this.twilioClient
    )
  }

  getConfig(): TwilioConfig | null {
    return this.config
  }

  // Method to ensure config is loaded, especially if relying on async loading
  private async ensureConfigLoaded(): Promise<void> {
    if (!this.config && this.dbService && this.siteId) {
      // If only ENV was loaded or nothing yet
      await this.loadConfig() // Attempt to load from DB
    }
    if (!this.isConfigured()) {
      // Attempt one last sync load from ENV if all else failed
      this.loadConfigSync()
      if (!this.isConfigured()) {
        console.error(
          'TwilioService: Configuration is not loaded or incomplete.',
        )
        // Optionally throw an error or handle gracefully
      }
    }
  }

  // Note: Webhook verification is now handled by the webhook utilities
  // This keeps the service focused on Twilio API operations

  /**
   * Initialize media service with S3 configuration
   * Automatically detects available S3 service if none specified
   * @param s3ConfigId - Optional S3 configuration ID
   */
  async initializeMediaService(s3ConfigId?: number): Promise<boolean> {
    if (!this.mediaService) {
      console.warn(
        'TwilioService: Media service not available (no database service)',
      )
      return false
    }

    // If specific config ID provided, use it
    if (s3ConfigId) {
      return await this.mediaService.initializeS3Service(s3ConfigId)
    }

    // Smart auto-detection: use first available S3 configuration
    if (this.dbService) {
      try {
        const allS3Configs = await this.dbService.getAllS3Configurations()
        if (allS3Configs.length > 0) {
          const firstConfig = allS3Configs[0]
          console.log(
            `TwilioService: Auto-detected S3 service: ${firstConfig.serviceName}`,
          )
          return await this.mediaService.initializeS3Service(firstConfig.id)
        }
        console.warn('TwilioService: No S3 configurations found in database')
        return false
      } catch (error) {
        console.error('TwilioService: Error detecting S3 configuration:', error)
        return false
      }
    }

    return false
  }

  /**
   * Process media message - download from Twilio and upload to S3
   * @param message - Twilio message with media
   * @returns Updated message with S3 URL
   */
  async processMediaMessage(
    message: TwilioDbMessage,
  ): Promise<TwilioDbMessage> {
    if (!this.mediaService || !message.mediaUrl || !this.config?.authToken) {
      return message
    }

    // Ensure media service is initialized
    if (!this.mediaService.isS3ServiceInitialized()) {
      const initialized = await this.initializeMediaService()
      if (!initialized) {
        console.error('TwilioService: Failed to initialize media service')
        return message
      }
    }

    // Convert Twilio message to standard format
    const standardMessage: StandardMediaMessage = {
      id: message.id,
      mediaUrl: message.mediaUrl,
      mediaContentType: message.mediaContentType || 'application/octet-stream',
      platform: 'twilio',
      timestamp: message.timestamp,
      from: message.from,
      to: message.to,
    }

    // Prepare Twilio auth configuration
    const authConfig: AuthConfig = {
      headers: {
        Authorization: `Basic ${Buffer.from(`${this.config.accountSid}:${this.config.authToken}`).toString('base64')}`,
      },
      method: 'GET',
    }

    // Process media and get updated standard message
    const processedStandardMessage =
      await this.mediaService.processMediaMessage(standardMessage, authConfig)

    // Convert back to Twilio format and return
    return {
      ...message,
      mediaUrl: processedStandardMessage.mediaUrl,
    }
  }

  // Send text message via Twilio API
  async sendTextMessage(
    to: string, // User's WhatsApp number, e.g., whatsapp:+**********
    body: string,
    // from?: string // Optional: Twilio WhatsApp number, e.g., whatsapp:+**********. If not provided, uses configured one.
  ): Promise<{ success: boolean; messageSid?: string; error?: string }> {
    await this.ensureConfigLoaded()
    if (!this.isConfigured() || !this.twilioClient) {
      console.error(
        'TwilioService: Cannot send message, service not configured properly after ensureConfigLoaded.',
      )
      return {
        success: false,
        error: 'Twilio not configured',
      }
    }

    const fromNumber = this.config!.phoneNumber // e.g., whatsapp:+***********
    const recipientNumber = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`

    try {
      //console.log(`TwilioService: Sending message from ${fromNumber} to ${recipientNumber}: "${body}"`);
      const message = await this.twilioClient.messages.create({
        from: fromNumber,
        to: recipientNumber,
        body,
      })

      console.log(
        `TwilioService: Message sent successfully. SID: ${message.sid}`,
      )

      if (this.dbService && this.siteId) {
        console.log(
          'TwilioService: Logging successful outbound message to database',
        )
        const logEntry: Omit<
          TwilioDbMessage,
          'createdAt' | 'updatedAt' | 'accountSid'
        > & { id: string; accountSid?: string | null } = {
          id: message.sid, // Use Twilio's message SID as the ID
          siteId: Number(this.siteId),
          accountSid: this.config?.accountSid,
          from: fromNumber,
          to: recipientNumber,
          body: body,
          type: 'text', // Assuming this method only sends text for now
          status: 'delivered', // API succeeded, consider it delivered
          direction: 'outbound',
          timestamp: new Date(), // Timestamp of sending
          sessionId: recipientNumber, // Using recipient number as session ID for now
        }
        try {
          await Promise.race([
            this.dbService.logTwilioMessage(logEntry),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('DB timeout')), 3000),
            ),
          ])
          console.log(
            'TwilioService: Successfully logged outbound message to database',
          )
        } catch (dbError) {
          console.error(
            'TwilioService: Failed to log outbound message to database:',
            dbError,
          )
        }
      } else {
        console.log(
          `TwilioService: Skipping database logging - dbService: ${!!this.dbService}, siteId: ${this.siteId}`,
        )
      }

      return {
        success: true,
        messageSid: message.sid,
      }
    } catch (error: any) {
      console.error('TwilioService: Failed to send message:', error.message)
      if (this.dbService && this.siteId) {
        console.log(
          'TwilioService: Logging failed outbound message to database',
        )
        const logEntry: Omit<
          TwilioDbMessage,
          'createdAt' | 'updatedAt' | 'accountSid'
        > & { id: string; accountSid?: string | null } = {
          // Attempt to create a temporary ID or handle error logging differently
          id: `failed-${Date.now()}`,
          siteId: Number(this.siteId),
          accountSid: this.config?.accountSid,
          from: fromNumber,
          to: recipientNumber,
          body: body,
          type: 'text',
          status: 'failed',
          direction: 'outbound',
          errorCode: error.code,
          errorMessage: error.message,
          timestamp: new Date(),
          sessionId: recipientNumber,
        }
        try {
          await this.dbService.logTwilioMessage(logEntry)
          console.log(
            'TwilioService: Successfully logged failed outbound message to database',
          )
        } catch (dbError) {
          console.error(
            'TwilioService: Failed to log failed outbound message to database:',
            dbError,
          )
        }
      } else {
        console.log(
          `TwilioService: Skipping failed message database logging - dbService: ${!!this.dbService}, siteId: ${this.siteId}`,
        )
      }
      return {
        success: false,
        error: error.message || 'Twilio API error',
      }
    }
  }

  // Process incoming webhook payload from Twilio
  async processWebhookPayload(
    payload: TwilioWebhookPayload,
  ): Promise<TwilioMessage[]> {
    // Note: ensureConfigLoaded might not be necessary here if this method is only called
    // after a successful webhook verification which itself should ensure config.
    // However, adding it for safety or if called directly.
    // await this.ensureConfigLoaded(); // Config should be loaded by the handler that calls this

    const messages: TwilioMessage[] = []

    // Example Twilio payload structure for a text message:
    // {
    //   SmsMessageSid: 'SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   NumMedia: '0',
    //   SmsSid: 'SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   SmsStatus: 'received',
    //   Body: 'Hello from user',
    //   To: 'whatsapp:+***********', // Your Twilio number
    //   NumSegments: '1',
    //   MessageSid: 'SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   AccountSid: 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   From: 'whatsapp:+**********1', // User's number
    //   ApiVersion: '2010-04-01'
    // }
    // Media message:
    // {
    //   MediaContentType0: 'image/jpeg',
    //   SmsMessageSid: 'MMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   NumMedia: '1',
    //   SmsSid: 'MMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   SmsStatus: 'received',
    //   Body: '', // May or may not have body/caption
    //   To: 'whatsapp:+***********',
    //   NumSegments: '1',
    //   MessageSid: 'MMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   AccountSid: 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
    //   From: 'whatsapp:+**********1',
    //   ApiVersion: '2010-04-01',
    //   MediaUrl0: 'https://api.twilio.com/2010-04-01/Accounts/ACxxxx/Messages/MMxxxx/Media/MExxxx'
    // }

    try {
      console.log(
        'TwilioService: Processing webhook payload:',
        JSON.stringify(payload, null, 2),
      )

      const messageSid = payload.MessageSid
      const from = payload.From // User's number (e.g., whatsapp:+**********)
      const to = payload.To // Your Twilio number (e.g., whatsapp:+***********)
      const body = payload.Body || '' // Message text, could be empty for media

      let type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'unknown' =
        'text'
      let mediaUrl: string | undefined
      let mediaContentType: string | undefined

      if (payload.NumMedia && Number.parseInt(payload.NumMedia, 10) > 0) {
        // For simplicity, handling only the first media item.
        // Twilio sends MediaUrl0, MediaContentType0, etc.
        mediaUrl = payload.MediaUrl0
        mediaContentType = payload.MediaContentType0

        if (mediaContentType) {
          if (mediaContentType.startsWith('image/')) {
            type = 'image'
          } else if (mediaContentType.startsWith('audio/')) {
            type = 'audio'
          } else if (mediaContentType.startsWith('video/')) {
            type = 'video'
          } else if (
            mediaContentType === 'application/pdf' ||
            mediaContentType.startsWith('application/')
          ) {
            // Basic document check
            type = 'document'
          } else {
            type = 'unknown' // Or handle as a generic file
          }
        } else {
          type = 'unknown' // Fallback if content type is missing but media exists
        }
        console.log(
          `TwilioService: Detected media. URL: ${mediaUrl}, Type: ${mediaContentType}`,
        )
      }

      const twilioMessage: TwilioMessage = {
        id: messageSid,
        from,
        to,
        type,
        content: body || (type !== 'text' ? `${type} received` : ''), // Use body or a placeholder for media
        timestamp: new Date(), // Twilio doesn't provide a timestamp in this payload directly; use arrival time.
        mediaUrl,
        mediaContentType,
        originalPayload: payload, // Store the original payload for potential further processing
      }

      messages.push(twilioMessage)
      console.log(
        'TwilioService: Processed message:',
        JSON.stringify(twilioMessage, null, 2),
      )

      // Log incoming message to DB
      if (this.dbService && this.siteId) {
        const logEntry: Omit<
          TwilioDbMessage,
          'createdAt' | 'updatedAt' | 'accountSid'
        > & { id: string; accountSid?: string | null } = {
          id: twilioMessage.id, // MessageSid from Twilio
          siteId: Number(this.siteId),
          accountSid: payload.AccountSid,
          from: twilioMessage.from,
          to: twilioMessage.to,
          body: twilioMessage.content, // Or specific body if different from content
          type: twilioMessage.type,
          mediaUrl: twilioMessage.mediaUrl,
          mediaContentType: twilioMessage.mediaContentType,
          status: 'received', // All inbound messages are 'received'
          direction: 'inbound',
          timestamp: twilioMessage.timestamp, // This is current Date(), consider if payload has a better timestamp
          sessionId: twilioMessage.from, // Using sender's number as session ID for now
        }

        // Always log the message first with Twilio URL (ensures we don't lose the message)
        this.dbService.logTwilioMessage(logEntry).catch((err) => {
          console.error(
            'TwilioService: Failed to log incoming message to DB:',
            err,
          )
        })

        // Process media if present - upload to S3 SYNCHRONOUSLY for AI processing
        if (logEntry.mediaUrl && logEntry.type !== 'text') {
          try {
            console.log(
              `TwilioService: Processing media synchronously for message ${logEntry.id}, original URL: ${logEntry.mediaUrl}`,
            )

            // Process media synchronously - try S3 upload
            const processedEntry = await this.processMediaMessage(logEntry)

            if (processedEntry.mediaUrl !== logEntry.mediaUrl) {
              console.log(
                `TwilioService: Media processed successfully, S3 URL: ${processedEntry.mediaUrl}`,
              )

              // Update the TwilioMessage object that will be returned to controller
              twilioMessage.mediaUrl = processedEntry.mediaUrl

              // Update database with S3 URL (async, non-blocking)
              this.dbService
                ?.updateTwilioMessageMediaUrl(
                  processedEntry.id,
                  processedEntry.mediaUrl,
                )
                .catch((err) => {
                  console.error(
                    'TwilioService: Failed to update message with S3 URL:',
                    err,
                  )
                })
            } else {
              console.log(
                `TwilioService: Media processing completed but URL unchanged for message ${processedEntry.id}`,
              )
            }
          } catch (error) {
            console.error(
              `TwilioService: S3 media processing failed for message ${logEntry.id}:`,
              error,
            )
            console.log(
              `TwilioService: Keeping original Twilio URL: ${logEntry.mediaUrl}`,
            )
            console.log(
              'TwilioService: OpenAI will likely fail to access this URL, but error handling will manage it',
            )
            // Keep original URL in twilioMessage, let existing error handling work
          }
        }
      }
    } catch (error) {
      console.error('TwilioService: Error processing webhook payload:', error)
    }

    return messages
  }
}

export default TwilioService // Export class itself
