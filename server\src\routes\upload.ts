import fs from 'node:fs'
import path from 'node:path'
import express from 'express'
import { handleUpload } from '../middleware/upload'
import openaiService from '../services/openai'

const router: express.Router = express.Router()

// Upload and process file (image or audio)
// @ts-ignore
router.post('/', handleUpload, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' })
    }

    const file = req.file
    const fileUrl = `${req.protocol}://${req.get('host')}/uploads/${file.filename}`

    // Check if it's an audio file for transcription
    const audioTypes = [
      'audio/mpeg',
      'audio/wav',
      'audio/webm',
      'audio/ogg',
      'video/webm',
    ]

    if (audioTypes.includes(file.mimetype)) {
      // Transcribe audio file
      const audioFile = fs.createReadStream(file.path)
      ;(audioFile as any).path = file.path // Required by OpenAI

      const transcription = await openaiService.transcribeAudio(audioFile)

      if (!transcription.success) {
        // Clean up file on error
        fs.unlinkSync(file.path)
        return res
          .status(500)
          .json({ error: transcription.error || 'Transcription failed' })
      }

      // Clean up audio file after transcription
      fs.unlinkSync(file.path)

      res.json({
        type: 'audio',
        transcription: transcription.text || '',
        originalFilename: file.originalname,
      })
    } else {
      // Return image file info
      res.json({
        type: 'image',
        url: fileUrl,
        filename: file.filename,
        originalFilename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
      })
    }
  } catch (error) {
    console.error('Upload processing error:', error)

    // Clean up file on error
    if (req.file?.path) {
      try {
        fs.unlinkSync(req.file.path)
      } catch (unlinkError) {
        console.error('Error cleaning up file:', unlinkError)
      }
    }

    res.status(500).json({ error: 'Internal server error' })
  }
})

// Delete uploaded file
// @ts-ignore
router.delete('/:filename', (req, res) => {
  try {
    const { filename } = req.params
    if (!filename) {
      return res.status(400).json({ error: 'Filename is required' })
    }
    const filePath = path.join(
      __dirname,
      '..',
      '..',
      process.env.UPLOAD_DIR || 'uploads',
      filename,
    )

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' })
    }

    fs.unlinkSync(filePath)
    res.json({ message: 'File deleted successfully' })
  } catch (error) {
    console.error('File deletion error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

export default router
