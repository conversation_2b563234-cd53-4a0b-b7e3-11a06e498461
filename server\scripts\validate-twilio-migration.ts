/// <reference types="node" />
import { eq } from 'drizzle-orm'
import { initializeDatabase } from '../src/db/connection'
import { s3Configurations, twilioConfigs } from '../src/db/schema'

/**
 * Twilio Migration Validation Script
 *
 * This script validates that Twilio migration was successful and all configurations are working.
 *
 * Usage:
 * dotenv -e .env.production -- bun scripts/validate-twilio-migration.ts
 */
async function validateTwilioMigration() {
  console.log('🔍 Validating Twilio Migration...')

  const db = initializeDatabase()

  try {
    let allValid = true

    // Check Twilio configuration
    console.log('📞 Checking Twilio configuration...')
    const twilioConfig = await db
      .select()
      .from(twilioConfigs)
      .where(eq(twilioConfigs.siteId, 1))
      .limit(1)

    if (!twilioConfig[0]) {
      console.error('❌ Twilio configuration not found in database')
      allValid = false
    } else {
      const config = twilioConfig[0]
      console.log('✅ Twilio configuration found')
      console.log(`   Account SID: ${config.accountSid.substring(0, 10)}...`)
      console.log(`   Phone Number: ${config.phoneNumber}`)
      console.log(`   Webhook URL: ${config.webhookUrl || 'Not set'}`)
      console.log(`   Active: ${config.isActive}`)

      // Validate required fields
      if (!config.accountSid || !config.authToken || !config.phoneNumber) {
        console.error(
          '❌ Twilio configuration incomplete - missing required fields',
        )
        allValid = false
      }
    }

    // Check S3 configuration
    console.log('')
    console.log('☁️  Checking S3 configuration...')
    const s3Config = await db.select().from(s3Configurations).limit(1)

    if (!s3Config[0]) {
      console.warn(
        '⚠️  S3 configuration not found - media processing will not work',
      )
    } else {
      const config = s3Config[0]
      console.log('✅ S3 configuration found')
      console.log(`   Service: ${config.serviceName}`)
      console.log(`   Bucket: ${config.bucketName}`)
      console.log(`   Region: ${config.region}`)

      // Validate required fields
      if (
        !config.accessKeyId ||
        !config.secretAccessKey ||
        !config.bucketName
      ) {
        console.error(
          '❌ S3 configuration incomplete - missing required fields',
        )
        allValid = false
      }
    }

    // Check environment variables
    console.log('')
    console.log('🔧 Checking environment variables...')
    const requiredEnvVars = [
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN',
      'TWILIO_PHONE_NUMBER',
      'WEBHOOK_BASE_URL',
    ]

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.error(`❌ Missing environment variable: ${envVar}`)
        allValid = false
      } else {
        console.log(`✅ ${envVar}: Set`)
      }
    }

    // Summary
    console.log('')
    if (allValid) {
      console.log('🎉 Twilio migration validation PASSED!')
      console.log('')
      console.log('🔗 Next steps:')
      console.log('  1. Update Twilio Console webhook URL')
      console.log('  2. Test with a WhatsApp message')
      console.log('  3. Monitor server logs for any issues')
    } else {
      console.log('❌ Twilio migration validation FAILED!')
      console.log('')
      console.log('🔧 Fix the issues above and run the validation again.')
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Error during validation:', error)
    process.exit(1)
  } finally {
    await db.$client.end()
  }
}

// Run validation
validateTwilioMigration()
  .then(() => {
    console.log('✅ Validation completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Validation failed:', error)
    process.exit(1)
  })

export { validateTwilioMigration }
