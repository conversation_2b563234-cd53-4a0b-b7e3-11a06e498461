'use client'

export const runtime = 'edge'

import { ArrowLeft, Calendar, Download, FileText, Share2 } from 'lucide-react'
import { useParams } from 'next/navigation'
import { PageWrapper } from '@/components/page-wrapper'
import { useAnnouncement } from '@/hooks/use-api'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'

export default function AnnouncementDetailPage() {
  const { language } = useLanguage()
  const params = useParams()
  const id = params.id as string

  const { data: announcement, loading, error } = useAnnouncement(id)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-green" />
      </div>
    )
  }

  if (error || !announcement) {
    return (
      <PageWrapper
        title="Announcement Not Found"
        titleBM="Pengumuman Tidak Ditemui"
        showBreadcrumbs={false}
      >
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {language === 'en'
              ? 'Announcement Not Found'
              : 'Pengumuman Tidak Ditemui'}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === 'en'
              ? 'The announcement you are looking for does not exist or has been removed.'
              : 'Pengumuman yang anda cari tidak wujud atau telah dialih keluar.'}
          </p>
          <Link href="/announcements" className="btn-primary">
            {language === 'en'
              ? 'Back to Announcements'
              : 'Kembali ke Pengumuman'}
          </Link>
        </div>
      </PageWrapper>
    )
  }

  const title = language === 'bm' ? announcement.titleBM : announcement.title
  const content =
    language === 'bm' ? announcement.contentBM : announcement.content

  const breadcrumbs = [
    {
      label: 'Announcements',
      labelBM: 'Pengumuman',
      href: '/announcements',
    },
    {
      label: title,
      labelBM: title,
    },
  ]

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'media-statement':
        return '📢'
      case 'withdrawal':
        return '⚠️'
      case 'announcement':
        return '📋'
      default:
        return '📄'
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels = {
      'media-statement': { en: 'Media Statement', bm: 'Kenyataan Media' },
      withdrawal: { en: 'Withdrawal', bm: 'Penarikan Balik' },
      announcement: { en: 'Announcement', bm: 'Pengumuman' },
      general: { en: 'General', bm: 'Umum' },
    }
    return labels[category as keyof typeof labels]?.[language] || category
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(language === 'bm' ? 'ms-MY' : 'en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: `${content.substring(0, 100)}...`,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  return (
    <PageWrapper
      title={title}
      titleBM={title}
      breadcrumbs={breadcrumbs}
      showTitle={false}
      headerActions={
        <div className="flex items-center gap-3">
          <button
            onClick={handleShare}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-primary-green transition-colors"
          >
            <Share2 className="w-4 h-4" />
            {language === 'en' ? 'Share' : 'Kongsi'}
          </button>
          {announcement.pdfUrl && (
            <a
              href={announcement.pdfUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-4 py-2 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
            >
              <Download className="w-4 h-4" />
              {language === 'en' ? 'Download PDF' : 'Muat Turun PDF'}
            </a>
          )}
        </div>
      }
    >
      {/* Back Button */}
      <div className="mb-6">
        <Link
          href="/announcements"
          className="inline-flex items-center gap-2 text-primary-green hover:text-primary-green-dark transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          {language === 'en'
            ? 'Back to Announcements'
            : 'Kembali ke Pengumuman'}
        </Link>
      </div>

      {/* Article Header */}
      <article className="max-w-4xl mx-auto">
        <header className="mb-8">
          {/* Category and Date */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <span
              className={cn(
                'inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium',
                announcement.category === 'media-statement' &&
                  'bg-blue-100 text-blue-800',
                announcement.category === 'withdrawal' &&
                  'bg-red-100 text-red-800',
                announcement.category === 'announcement' &&
                  'bg-green-100 text-green-800',
                announcement.category === 'general' &&
                  'bg-gray-100 text-gray-800'
              )}
            >
              <span>{getCategoryIcon(announcement.category)}</span>
              {getCategoryLabel(announcement.category)}
            </span>

            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="w-4 h-4" />
              <time dateTime={announcement.date}>
                {formatDate(announcement.date)}
              </time>
            </div>

            {announcement.featured && (
              <span className="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                ⭐ {language === 'en' ? 'Featured' : 'Ditampilkan'}
              </span>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight mb-6">
            {title}
          </h1>
        </header>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
            {content}
          </div>
        </div>

        {/* PDF Download Section */}
        {announcement.pdfUrl && (
          <div className="mt-12 p-6 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0 w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-red-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">
                  {language === 'en' ? 'Official Document' : 'Dokumen Rasmi'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Download the official PDF document for complete details.'
                    : 'Muat turun dokumen PDF rasmi untuk butiran lengkap.'}
                </p>
              </div>
              <a
                href={announcement.pdfUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary"
              >
                <Download className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Download' : 'Muat Turun'}
              </a>
            </div>
          </div>
        )}

        {/* Footer Actions */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
            <div className="text-sm text-gray-600">
              {language === 'en' ? 'Published on' : 'Diterbitkan pada'}{' '}
              {formatDate(announcement.date)}
            </div>

            <div className="flex items-center gap-3">
              <button onClick={handleShare} className="btn-secondary">
                <Share2 className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Share' : 'Kongsi'}
              </button>

              <Link href="/announcements" className="btn-primary">
                {language === 'en'
                  ? 'More Announcements'
                  : 'Lebih Banyak Pengumuman'}
              </Link>
            </div>
          </div>
        </div>
      </article>
    </PageWrapper>
  )
}
