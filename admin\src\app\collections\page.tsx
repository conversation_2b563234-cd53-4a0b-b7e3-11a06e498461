'use client'

import { Database, Search } from 'lucide-react'
import { useEffect, useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { useAuthStore } from '@/stores/auth'

interface Collection {
  id: number
  name: string
  description?: string
  status: string
  createdAt: string
  updatedAt: string
}

export default function CollectionsPage() {
  const { token } = useAuthStore()
  const [collections, setCollections] = useState<Collection[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

  useEffect(() => {
    const fetchCollections = async () => {
      if (!token) return

      try {
        setIsLoading(true)
        const response = await fetch(`${API_BASE_URL}/api/admin/collections`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        if (response.ok) {
          const data = await response.json()
          setCollections(data)
        } else {
          setError('Failed to fetch collections')
        }
      } catch (error) {
        setError('Network error')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCollections()
  }, [token, API_BASE_URL])

  const filteredCollections = collections.filter(
    collection =>
      collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (collection.description &&
        collection.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()))
  )

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Collections</h1>
          <p className="text-gray-600">
            View all document collections in the system
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>All Collections</CardTitle>
            <CardDescription>
              Document collections across all sites
            </CardDescription>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search collections..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading collections...</p>
              </div>
            ) : filteredCollections.length === 0 ? (
              <div className="text-center py-8">
                <Database className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No collections found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery
                    ? 'No collections match your search.'
                    : 'No collections have been created yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredCollections.map(collection => (
                  <div
                    key={collection.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {collection.name}
                          </h3>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              collection.status === 'ACTIVE'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {collection.status}
                          </span>
                        </div>
                        {collection.description && (
                          <p className="text-sm text-gray-600 mt-1">
                            {collection.description}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 mt-2">
                          Created:{' '}
                          {new Date(collection.createdAt).toLocaleDateString()}
                          {collection.updatedAt !== collection.createdAt && (
                            <span>
                              {' '}
                              • Updated:{' '}
                              {new Date(
                                collection.updatedAt
                              ).toLocaleDateString()}
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
