

db-reset:
	@echo "⚠️  WARNING: This will completely clear all database tables and reseed with fresh data!"
	@read -p "Are you sure you want to proceed? (y/N): " confirm && [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ] || (echo "❌ Operation cancelled." && exit 1)
	@echo ""
	@echo "🧹 Clearing database and reseeding..."
	@pnpm run db:seed
	@echo ""
	@echo "✅ Database reset complete!"


db-migrate-up:
	pnpm run env -- npx drizzle-kit migrate up
	echo "✅ Migration completed successfully!"
	
db-migrate-add:
	@if [ -z "$(name)" ]; then \
		echo "Error: name parameter is required"; \
		exit 1; \
	fi
	pnpm run env -- npx drizzle-kit generate --name=$(name) --custom

db-migrate-autoadd:
	@if [ -z "$(name)" ]; then \
		echo "Error: name parameter is required"; \
		exit 1; \
	fi
	pnpm run env -- npx drizzle-kit generate --name=$(name) 
