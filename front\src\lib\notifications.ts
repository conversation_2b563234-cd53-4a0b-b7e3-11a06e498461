'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Notification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  urgent?: boolean
  actionUrl?: string
  timestamp: string
  read?: boolean
  dismissed?: boolean
}

interface NotificationState {
  notifications: Notification[]
  unreadCount: number
  soundEnabled: boolean
  browserNotificationsEnabled: boolean
  addNotification: (
    notification: Omit<Notification, 'id' | 'timestamp'>
  ) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  dismissNotification: (id: string) => void
  clearAll: () => void
  setSoundEnabled: (enabled: boolean) => void
  setBrowserNotificationsEnabled: (enabled: boolean) => void
  requestBrowserPermission: () => Promise<boolean>
}

export const useNotificationStore = create<NotificationState>()(
  persist(
    (set, get) => ({
      notifications: [],
      unreadCount: 0,
      soundEnabled: true,
      browserNotificationsEnabled: false,

      addNotification: notification => {
        const newNotification: Notification = {
          ...notification,
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          read: false,
          dismissed: false,
        }

        set(state => {
          const notifications = [newNotification, ...state.notifications]
          const unreadCount = notifications.filter(
            n => !n.read && !n.dismissed
          ).length

          return {
            notifications,
            unreadCount,
          }
        })

        // Play sound for urgent notifications
        if (notification.urgent && get().soundEnabled) {
          playNotificationSound()
        }

        // Show browser notification
        if (get().browserNotificationsEnabled) {
          showBrowserNotification(newNotification)
        }
      },

      markAsRead: id => {
        set(state => {
          const notifications = state.notifications.map(n =>
            n.id === id ? { ...n, read: true } : n
          )
          const unreadCount = notifications.filter(
            n => !n.read && !n.dismissed
          ).length

          return { notifications, unreadCount }
        })
      },

      markAllAsRead: () => {
        set(state => ({
          notifications: state.notifications.map(n => ({ ...n, read: true })),
          unreadCount: 0,
        }))
      },

      dismissNotification: id => {
        set(state => {
          const notifications = state.notifications.map(n =>
            n.id === id ? { ...n, dismissed: true } : n
          )
          const unreadCount = notifications.filter(
            n => !n.read && !n.dismissed
          ).length

          return { notifications, unreadCount }
        })
      },

      clearAll: () => {
        set({ notifications: [], unreadCount: 0 })
      },

      setSoundEnabled: enabled => {
        set({ soundEnabled: enabled })
      },

      setBrowserNotificationsEnabled: enabled => {
        set({ browserNotificationsEnabled: enabled })
      },

      requestBrowserPermission: async () => {
        if (!('Notification' in window)) {
          return false
        }

        if (Notification.permission === 'granted') {
          set({ browserNotificationsEnabled: true })
          return true
        }

        if (Notification.permission === 'denied') {
          return false
        }

        const permission = await Notification.requestPermission()
        const granted = permission === 'granted'

        if (granted) {
          set({ browserNotificationsEnabled: true })
        }

        return granted
      },
    }),
    {
      name: 'notification-store',
      partialize: state => ({
        soundEnabled: state.soundEnabled,
        browserNotificationsEnabled: state.browserNotificationsEnabled,
      }),
    }
  )
)

// Utility functions
function playNotificationSound() {
  try {
    // Create a simple beep sound using Web Audio API
    const audioContext = new (window.AudioContext ||
      (window as any).webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
    oscillator.type = 'sine'

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      audioContext.currentTime + 0.5
    )

    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.5)
  } catch (error) {
    console.warn('Could not play notification sound:', error)
  }
}

function showBrowserNotification(notification: Notification) {
  if (!('Notification' in window) || Notification.permission !== 'granted') {
    return
  }

  try {
    const browserNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: notification.id,
      requireInteraction: notification.urgent,
    })

    browserNotification.onclick = () => {
      window.focus()
      if (notification.actionUrl) {
        window.location.href = notification.actionUrl
      }
      browserNotification.close()
    }

    // Auto-close non-urgent notifications after 5 seconds
    if (!notification.urgent) {
      setTimeout(() => {
        browserNotification.close()
      }, 5000)
    }
  } catch (error) {
    console.warn('Could not show browser notification:', error)
  }
}

// Hook for easy notification management
export function useNotifications() {
  const store = useNotificationStore()

  return {
    ...store,
    // Helper methods
    showSuccess: (title: string, message: string, actionUrl?: string) => {
      store.addNotification({ type: 'success', title, message, actionUrl })
    },
    showError: (title: string, message: string, actionUrl?: string) => {
      store.addNotification({
        type: 'error',
        title,
        message,
        actionUrl,
        urgent: true,
      })
    },
    showWarning: (title: string, message: string, actionUrl?: string) => {
      store.addNotification({ type: 'warning', title, message, actionUrl })
    },
    showInfo: (title: string, message: string, actionUrl?: string) => {
      store.addNotification({ type: 'info', title, message, actionUrl })
    },
    showUrgent: (title: string, message: string, actionUrl?: string) => {
      store.addNotification({
        type: 'error',
        title,
        message,
        actionUrl,
        urgent: true,
      })
    },
  }
}

// Export types
export type { NotificationState }
