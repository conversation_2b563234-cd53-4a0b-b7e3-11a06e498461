'use client'

import {
  Calendar,
  FileText,
  Megaphone,
  TrendingDown,
  TrendingUp,
} from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { useLanguage } from '@/hooks/use-language'

interface ContentStats {
  overview: {
    total: {
      announcements: number
      news: number
      content: number
    }
    period: {
      announcements: number
      news: number
      content: number
      timeframe: string
    }
    featured: {
      announcements: number
    }
  }
  categories: {
    announcements: Record<string, number>
    news: Record<string, number>
  }
  trends: {
    monthly: Array<{
      month: string
      announcements: number
      news: number
    }>
    growth: {
      announcements: number
      news: number
    }
  }
  recentActivity: Array<{
    type: 'announcement' | 'news'
    id: string
    title: string
    date: string
    category: string
  }>
}

interface ContentAnalyticsProps {
  className?: string
  period?: 'week' | 'month' | 'quarter' | 'year'
  showCategories?: boolean
  showTrends?: boolean
  compact?: boolean
}

export function ContentAnalytics({
  className,
  period = 'month',
  showCategories = true,
  showTrends = true,
  compact = false,
}: ContentAnalyticsProps) {
  const { language } = useLanguage()
  const [stats, setStats] = useState<ContentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchContentStats = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        period,
        includeCategories: showCategories.toString(),
        includeTrends: showTrends.toString(),
      })

      const response = await fetch(`/api/content/stats?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch content stats')
      }

      const data = await response.json()
      setStats(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }, [period, showCategories, showTrends])

  useEffect(() => {
    fetchContentStats()
  }, [fetchContentStats])

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: compact ? 2 : 4 }).map((_, i) => (
          <Card key={i} className="p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
              <div className="h-8 bg-gray-200 rounded w-1/2" />
            </div>
          </Card>
        ))}
      </div>
    )
  }

  if (error || !stats) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center text-red-600">
          {language === 'en'
            ? 'Failed to load analytics'
            : 'Gagal memuatkan analitik'}
        </div>
      </Card>
    )
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat(language === 'bm' ? 'ms-MY' : 'en-MY').format(
      num
    )
  }

  const formatGrowth = (growth: number) => {
    const isPositive = growth > 0
    const Icon = isPositive ? TrendingUp : TrendingDown
    const color = isPositive ? 'text-green-600' : 'text-red-600'

    return (
      <div className={`flex items-center gap-1 ${color}`}>
        <Icon className="w-4 h-4" />
        <span className="text-sm font-medium">{Math.abs(growth)}%</span>
      </div>
    )
  }

  const getPeriodLabel = () => {
    const labels = {
      en: {
        week: 'This Week',
        month: 'This Month',
        quarter: 'This Quarter',
        year: 'This Year',
      },
      bm: {
        week: 'Minggu Ini',
        month: 'Bulan Ini',
        quarter: 'Suku Tahun Ini',
        year: 'Tahun Ini',
      },
    }
    return labels[language][period]
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div
        className={`grid gap-4 ${compact ? 'grid-cols-2' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'}`}
      >
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {language === 'en' ? 'Total Content' : 'Jumlah Kandungan'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(stats.overview.total.content)}
              </p>
            </div>
            <FileText className="w-8 h-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {language === 'en' ? 'Announcements' : 'Pengumuman'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(stats.overview.total.announcements)}
              </p>
              {showTrends && stats.trends && (
                <div className="mt-1">
                  {formatGrowth(stats.trends.growth.announcements)}
                </div>
              )}
            </div>
            <Megaphone className="w-8 h-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {language === 'en' ? 'News' : 'Berita'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(stats.overview.total.news)}
              </p>
              {showTrends && stats.trends && (
                <div className="mt-1">
                  {formatGrowth(stats.trends.growth.news)}
                </div>
              )}
            </div>
            <FileText className="w-8 h-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {getPeriodLabel()}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {formatNumber(stats.overview.period.content)}
              </p>
            </div>
            <Calendar className="w-8 h-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Categories Breakdown */}
      {showCategories && stats.categories && !compact && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              {language === 'en'
                ? 'Announcement Categories'
                : 'Kategori Pengumuman'}
            </h3>
            <div className="space-y-3">
              {Object.entries(stats.categories.announcements).map(
                ([category, count]) => (
                  <div
                    key={category}
                    className="flex justify-between items-center"
                  >
                    <span className="text-sm text-gray-600">{category}</span>
                    <span className="font-medium">{formatNumber(count)}</span>
                  </div>
                )
              )}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              {language === 'en' ? 'News Categories' : 'Kategori Berita'}
            </h3>
            <div className="space-y-3">
              {Object.entries(stats.categories.news).map(
                ([category, count]) => (
                  <div
                    key={category}
                    className="flex justify-between items-center"
                  >
                    <span className="text-sm text-gray-600">{category}</span>
                    <span className="font-medium">{formatNumber(count)}</span>
                  </div>
                )
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Recent Activity */}
      {!compact && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            {language === 'en' ? 'Recent Activity' : 'Aktiviti Terkini'}
          </h3>
          <div className="space-y-3">
            {stats.recentActivity.slice(0, 5).map(item => (
              <div
                key={`${item.type}-${item.id}`}
                className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
              >
                <div
                  className={`p-2 rounded-full ${
                    item.type === 'announcement'
                      ? 'bg-green-100 text-green-600'
                      : 'bg-purple-100 text-purple-600'
                  }`}
                >
                  {item.type === 'announcement' ? (
                    <Megaphone className="w-4 h-4" />
                  ) : (
                    <FileText className="w-4 h-4" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {item.category} •{' '}
                    {new Date(item.date).toLocaleDateString(
                      language === 'bm' ? 'ms-MY' : 'en-MY'
                    )}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Trends Chart (Simple visualization) */}
      {showTrends && stats.trends && !compact && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            {language === 'en' ? 'Content Trends' : 'Trend Kandungan'}
          </h3>
          <div className="space-y-4">
            {stats.trends.monthly.slice(-6).map(month => (
              <div key={month.month} className="flex items-center gap-4">
                <div className="w-16 text-sm text-gray-600">
                  {new Date(`${month.month}-01`).toLocaleDateString(
                    language === 'bm' ? 'ms-MY' : 'en-MY',
                    { month: 'short' }
                  )}
                </div>
                <div className="flex-1 flex gap-2">
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 bg-green-500 rounded"
                      style={{
                        width: `${Math.max(month.announcements * 10, 4)}px`,
                      }}
                    />
                    <span className="text-xs text-gray-600">
                      {month.announcements}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 bg-purple-500 rounded"
                      style={{ width: `${Math.max(month.news * 10, 4)}px` }}
                    />
                    <span className="text-xs text-gray-600">{month.news}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 flex gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded" />
              <span>{language === 'en' ? 'Announcements' : 'Pengumuman'}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded" />
              <span>{language === 'en' ? 'News' : 'Berita'}</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
