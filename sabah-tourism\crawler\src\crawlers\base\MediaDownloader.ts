import axios, { AxiosResponse } from "axios";
import fs, { createReadStream, createWriteStream } from "fs";
import mime from "mime-types";
import path from "path";
import sharp from "sharp";
import { pipeline } from "stream/promises";
import type { DownloadProgress } from "../../types/crawler.js";
import type { MediaItem } from "../../types/social-media.js";
import { downloaderLogger, logMediaDownload } from "../../utils/logger.js";
import { createRetryConfig, withRetry } from "../../utils/retry.js";
import { sanitizeFileName, validateFileName } from "../../utils/validation.js";

export interface DownloadConfig {
  outputDir: string;
  maxFileSize: number; // in bytes
  allowedTypes: string[];
  concurrency: number;
  timeout: number;
  generateThumbnails: boolean;
  thumbnailSize: { width: number; height: number };
}

export interface DownloadResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  dimensions?: { width: number; height: number };
  error?: string;
  thumbnailPath?: string;
}

export class MediaDownloader {
  private config: DownloadConfig;
  private activeDownloads = new Map<string, Promise<DownloadResult>>();
  private downloadQueue: Array<{
    url: string;
    outputPath: string;
    resolve: Function;
    reject: Function;
  }> = [];
  private isProcessingQueue = false;

  constructor(config: DownloadConfig) {
    this.config = config;
    this.ensureOutputDirectory();
  }

  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.config.outputDir)) {
      fs.mkdirSync(this.config.outputDir, { recursive: true });
      downloaderLogger.info("Created output directory", {
        path: this.config.outputDir,
      });
    }
  }

  public async downloadMedia(
    mediaItem: MediaItem,
    postId: string,
    platform: string,
    onProgress?: (progress: DownloadProgress) => void,
  ): Promise<DownloadResult> {
    const outputDir = path.join(this.config.outputDir, platform, postId);

    // Ensure post-specific directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Generate filename
    const fileName = this.generateFileName(
      mediaItem.url,
      mediaItem.type,
      mediaItem.format,
    );
    const outputPath = path.join(outputDir, fileName);

    // Check if file already exists
    if (fs.existsSync(outputPath)) {
      downloaderLogger.info("File already exists, skipping download", {
        path: outputPath,
      });
      return {
        success: true,
        filePath: outputPath,
        fileName,
        fileSize: fs.statSync(outputPath).size,
      };
    }

    // Add to queue if we're at concurrency limit
    if (this.activeDownloads.size >= this.config.concurrency) {
      return new Promise((resolve, reject) => {
        this.downloadQueue.push({
          url: mediaItem.url,
          outputPath,
          resolve,
          reject,
        });
        this.processQueue();
      });
    }

    return this.performDownload(mediaItem, outputPath, fileName, onProgress);
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.downloadQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (
      this.downloadQueue.length > 0 &&
      this.activeDownloads.size < this.config.concurrency
    ) {
      const { url, outputPath, resolve, reject } = this.downloadQueue.shift()!;

      try {
        const mediaItem: MediaItem = { type: "image", url }; // Simplified for queue processing
        const fileName = path.basename(outputPath);
        const result = await this.performDownload(
          mediaItem,
          outputPath,
          fileName,
        );
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }

    this.isProcessingQueue = false;
  }

  private async performDownload(
    mediaItem: MediaItem,
    outputPath: string,
    fileName: string,
    onProgress?: (progress: DownloadProgress) => void,
  ): Promise<DownloadResult> {
    const downloadPromise = this.downloadWithRetry(
      mediaItem,
      outputPath,
      fileName,
      onProgress,
    );
    this.activeDownloads.set(mediaItem.url, downloadPromise);

    try {
      const result = await downloadPromise;
      return result;
    } finally {
      this.activeDownloads.delete(mediaItem.url);
      this.processQueue(); // Process next item in queue
    }
  }

  private async downloadWithRetry(
    mediaItem: MediaItem,
    outputPath: string,
    fileName: string,
    onProgress?: (progress: DownloadProgress) => void,
  ): Promise<DownloadResult> {
    const retryConfig = createRetryConfig({
      maxRetries: 3,
      initialDelay: 1000,
      backoffMultiplier: 2,
    });

    const result = await withRetry(
      () => this.downloadFile(mediaItem, outputPath, fileName, onProgress),
      retryConfig,
      `download-${fileName}`,
    );

    if (!result.success) {
      logMediaDownload(
        mediaItem.url,
        fileName,
        "failed",
        result.error?.message,
      );
      return {
        success: false,
        error: result.error?.message || "Download failed",
      };
    }

    logMediaDownload(mediaItem.url, fileName, "completed");
    return result.result!;
  }

  private async downloadFile(
    mediaItem: MediaItem,
    outputPath: string,
    fileName: string,
    onProgress?: (progress: DownloadProgress) => void,
  ): Promise<DownloadResult> {
    try {
      const response = await axios({
        method: "GET",
        url: mediaItem.url,
        responseType: "stream",
        timeout: this.config.timeout,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        },
      });

      // Validate content type
      const contentType = response.headers["content-type"];
      if (!this.isAllowedContentType(contentType, mediaItem.type)) {
        throw new Error(
          `Invalid content type: ${contentType} for media type: ${mediaItem.type}`,
        );
      }

      // Check file size
      const contentLength = Number.parseInt(
        response.headers["content-length"] || "0",
      );
      if (contentLength > this.config.maxFileSize) {
        throw new Error(
          `File too large: ${contentLength} bytes (max: ${this.config.maxFileSize})`,
        );
      }

      // Download file
      const writeStream = createWriteStream(outputPath);
      let downloadedBytes = 0;
      const startTime = Date.now();

      response.data.on("data", (chunk: Buffer) => {
        downloadedBytes += chunk.length;

        if (onProgress) {
          const percentage =
            contentLength > 0 ? (downloadedBytes / contentLength) * 100 : 0;
          const elapsed = Date.now() - startTime;
          const speed = downloadedBytes / (elapsed / 1000);
          const estimatedTimeRemaining =
            contentLength > 0
              ? ((contentLength - downloadedBytes) / speed) * 1000
              : 0;

          onProgress({
            url: mediaItem.url,
            fileName,
            totalBytes: contentLength,
            downloadedBytes,
            percentage,
            speed,
            estimatedTimeRemaining,
            status: "downloading",
          });
        }
      });

      await pipeline(response.data, writeStream);

      // Get file stats
      const stats = fs.statSync(outputPath);
      let dimensions: { width: number; height: number } | undefined;
      let thumbnailPath: string | undefined;

      // Get dimensions for images and generate thumbnail if needed
      if (mediaItem.type === "image" && this.config.generateThumbnails) {
        try {
          const metadata = await sharp(outputPath).metadata();
          dimensions = {
            width: metadata.width || 0,
            height: metadata.height || 0,
          };

          // Generate thumbnail
          thumbnailPath = await this.generateThumbnail(outputPath, fileName);
        } catch (error) {
          downloaderLogger.warn("Failed to process image metadata", {
            error,
            file: outputPath,
          });
        }
      }

      if (onProgress) {
        onProgress({
          url: mediaItem.url,
          fileName,
          totalBytes: stats.size,
          downloadedBytes: stats.size,
          percentage: 100,
          speed: 0,
          estimatedTimeRemaining: 0,
          status: "completed",
        });
      }

      return {
        success: true,
        filePath: outputPath,
        fileName,
        fileSize: stats.size,
        dimensions,
        thumbnailPath,
      };
    } catch (error) {
      // Clean up partial download
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }

      if (onProgress) {
        onProgress({
          url: mediaItem.url,
          fileName,
          totalBytes: 0,
          downloadedBytes: 0,
          percentage: 0,
          speed: 0,
          estimatedTimeRemaining: 0,
          status: "failed",
          error: (error as Error).message,
        });
      }

      throw error;
    }
  }

  private generateFileName(
    url: string,
    mediaType: string,
    format?: string,
  ): string {
    try {
      const urlObj = new URL(url);
      let fileName = path.basename(urlObj.pathname);

      // If no filename or extension, generate one
      if (!fileName || !path.extname(fileName)) {
        const timestamp = Date.now();
        const extension = format || this.getExtensionFromType(mediaType);
        fileName = `${mediaType}_${timestamp}.${extension}`;
      }

      // Sanitize filename
      fileName = sanitizeFileName(fileName);

      // Validate filename
      if (!validateFileName(fileName)) {
        const timestamp = Date.now();
        const extension = format || this.getExtensionFromType(mediaType);
        fileName = `${mediaType}_${timestamp}.${extension}`;
      }

      return fileName;
    } catch (error) {
      // Fallback filename
      const timestamp = Date.now();
      const extension = format || this.getExtensionFromType(mediaType);
      return `${mediaType}_${timestamp}.${extension}`;
    }
  }

  private getExtensionFromType(mediaType: string): string {
    switch (mediaType) {
      case "image":
        return "jpg";
      case "video":
        return "mp4";
      case "audio":
        return "mp3";
      default:
        return "bin";
    }
  }

  private isAllowedContentType(
    contentType: string,
    mediaType: string,
  ): boolean {
    if (!contentType) return false;

    const type = contentType.split("/")[0];
    return type === mediaType || this.config.allowedTypes.includes(contentType);
  }

  private async generateThumbnail(
    imagePath: string,
    originalFileName: string,
  ): Promise<string> {
    const thumbnailDir = path.join(path.dirname(imagePath), "thumbnails");
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
    }

    const thumbnailFileName = `thumb_${originalFileName}`;
    const thumbnailPath = path.join(thumbnailDir, thumbnailFileName);

    await sharp(imagePath)
      .resize(
        this.config.thumbnailSize.width,
        this.config.thumbnailSize.height,
        {
          fit: "inside",
          withoutEnlargement: true,
        },
      )
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    return thumbnailPath;
  }

  public getActiveDownloads(): string[] {
    return Array.from(this.activeDownloads.keys());
  }

  public getQueueLength(): number {
    return this.downloadQueue.length;
  }

  public async waitForCompletion(): Promise<void> {
    while (this.activeDownloads.size > 0 || this.downloadQueue.length > 0) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }
}
