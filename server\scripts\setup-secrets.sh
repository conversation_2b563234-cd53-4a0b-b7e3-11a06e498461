#!/bin/bash

# <PERSON>ript to securely set up Cloudflare Workers secrets from .env.production
# This ensures sensitive data is not stored in wrangler.toml

set -e

echo "🔐 Setting up Cloudflare Workers secrets..."

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ .env.production file not found!"
    exit 1
fi

# Source the .env.production file
source .env.production

# Function to set secret if value is not empty
set_secret() {
    local key=$1
    local value=$2
    local env=$3
    
    if [ -n "$value" ]; then
        echo "Setting secret: $key for environment: $env"
        echo "$value" | npx wrangler secret put "$key" --env "$env"
    else
        echo "⚠️  Skipping empty secret: $key"
    fi
}

# Set production secrets
echo "📦 Setting production secrets..."
set_secret "OPENAI_API_KEY" "$OPENAI_API_KEY" "production"
set_secret "JWT_SECRET" "$JWT_SECRET" "production"
set_secret "ADMIN_DEFAULT_PASSWORD" "$ADMIN_DEFAULT_PASSWORD" "production"

# Set WhatsApp secrets if they exist
if [ -n "$WHATSAPP_ACCESS_TOKEN" ]; then
    set_secret "WHATSAPP_ACCESS_TOKEN" "$WHATSAPP_ACCESS_TOKEN" "production"
fi

if [ -n "$WHATSAPP_PHONE_NUMBER_ID" ]; then
    set_secret "WHATSAPP_PHONE_NUMBER_ID" "$WHATSAPP_PHONE_NUMBER_ID" "production"
fi

if [ -n "$WHATSAPP_WEBHOOK_VERIFY_TOKEN" ]; then
    set_secret "WHATSAPP_WEBHOOK_VERIFY_TOKEN" "$WHATSAPP_WEBHOOK_VERIFY_TOKEN" "production"
fi

if [ -n "$WHATSAPP_BUSINESS_ACCOUNT_ID" ]; then
    set_secret "WHATSAPP_BUSINESS_ACCOUNT_ID" "$WHATSAPP_BUSINESS_ACCOUNT_ID" "production"
fi

# Set Twilio secrets if they exist
if [ -n "$TWILIO_ACCOUNT_SID" ]; then
    set_secret "TWILIO_ACCOUNT_SID" "$TWILIO_ACCOUNT_SID" "production"
fi

if [ -n "$TWILIO_AUTH_TOKEN" ]; then
    set_secret "TWILIO_AUTH_TOKEN" "$TWILIO_AUTH_TOKEN" "production"
fi

if [ -n "$TWILIO_PHONE_NUMBER" ]; then
    set_secret "TWILIO_PHONE_NUMBER" "$TWILIO_PHONE_NUMBER" "production"
fi

# Set development secrets (using local JWT secret)
echo "📦 Setting development secrets..."
set_secret "OPENAI_API_KEY" "$OPENAI_API_KEY" "development"
set_secret "JWT_SECRET" "halal-chat-secret-key-2025-local" "development"
set_secret "ADMIN_DEFAULT_PASSWORD" "$ADMIN_DEFAULT_PASSWORD" "development"

# Set Twilio development secrets if they exist
if [ -n "$TWILIO_ACCOUNT_SID" ]; then
    set_secret "TWILIO_ACCOUNT_SID" "$TWILIO_ACCOUNT_SID" "development"
fi

if [ -n "$TWILIO_AUTH_TOKEN" ]; then
    set_secret "TWILIO_AUTH_TOKEN" "$TWILIO_AUTH_TOKEN" "development"
fi

if [ -n "$TWILIO_PHONE_NUMBER" ]; then
    set_secret "TWILIO_PHONE_NUMBER" "$TWILIO_PHONE_NUMBER" "development"
fi

echo "✅ All secrets have been set up successfully!"
echo ""
echo "🔍 You can verify secrets with:"
echo "  npx wrangler secret list --env production"
echo "  npx wrangler secret list --env development"
