'use client'

export const runtime = 'edge'

import { ArrowLeft, Save } from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { <PERSON>, useRouter } from '@/i18n/navigation'
import { api } from '@/lib/api'
import type { AdminUserCreationRequest } from '@/types'
import { UserRole } from '@/types/roles'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function NewAdminUserPage() {
  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [roles, setRoles] = useState<UserRole[]>([UserRole.EDITOR]) // Default to EDITOR
  const [isActive, setIsActive] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isAdmin, setIsAdmin] = useState(false) // For page access control
  const [checkingAuth, setCheckingAuth] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAdminRole = async () => {
      try {
        const meResponse = await api.admin.getMe()
        if (meResponse.user?.role === UserRole.ADMIN) {
          setIsAdmin(true)
        } else {
          setError('Access Denied: You do not have permission to create users.')
        }
      } catch (err) {
        setError('Failed to verify user role.')
        console.error(err)
      } finally {
        setCheckingAuth(false)
      }
    }
    checkAdminRole()
  }, [])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!username.trim() || !password.trim()) {
      setError('Username and password are required.')
      return
    }
    if (roles.length === 0) {
      setError('At least one role is required.')
      return
    }
    setIsLoading(true)
    setError(null)

    const userData: AdminUserCreationRequest = {
      username,
      email: email.trim() || undefined,
      password,
      firstName: firstName.trim() || undefined,
      lastName: lastName.trim() || undefined,
      roles,
      isActive,
    }

    try {
      await api.admin.createUser(userData)
      // Optionally, add a success message/toast notification
      router.push('/admin/users') // Redirect to users list on success
    } catch (err: any) {
      setError(
        err.response?.data?.error || err.message || 'Failed to create user.'
      )
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }

  if (checkingAuth) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    )
  }

  if (!isAdmin && !error) {
    // Still checking or no specific error, but not admin
    setError('Access Denied: You do not have permission to create users.') // Set error if not already set
  }

  if (error && !isAdmin) {
    // Show error prominently if access is denied or another auth error
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <Link
          href="/admin/users"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to User List
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <Link
          href="/admin/users"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to User List
        </Link>
      </div>
      <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-6">
        Add New Admin User
      </h1>

      <form
        onSubmit={handleSubmit}
        className="bg-white shadow-md rounded-lg p-6 md:p-8"
      >
        {error &&
          !isAdmin && ( // General error unrelated to auth, but still show if auth failed.
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
              role="alert"
            >
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}
        {error &&
          isAdmin && ( // Error during form submission, user is admin
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
              role="alert"
            >
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Username *
            </label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={e => setUsername(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label
              htmlFor="firstName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              value={firstName}
              onChange={e => setFirstName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="lastName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              value={lastName}
              onChange={e => setLastName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="mb-4">
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Password *
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
            disabled={isLoading}
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Roles *
          </label>
          <div className="space-y-2">
            {Object.values(UserRole).map(roleOption => (
              <label key={roleOption} className="flex items-center">
                <input
                  type="checkbox"
                  checked={roles.includes(roleOption)}
                  onChange={e => {
                    if (e.target.checked) {
                      setRoles([...roles, roleOption])
                    } else {
                      setRoles(roles.filter(r => r !== roleOption))
                    }
                  }}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-700">{roleOption}</span>
              </label>
            ))}
          </div>
          {roles.length === 0 && (
            <p className="text-red-500 text-sm mt-1">
              At least one role is required
            </p>
          )}
        </div>

        <div className="mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isActive}
              onChange={e => setIsActive(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <span className="text-sm font-medium text-gray-700">
              Active User
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Inactive users cannot log in to the system
          </p>
        </div>

        <div className="flex items-center justify-end">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out disabled:opacity-50"
            disabled={isLoading || !isAdmin}
          >
            <Save size={18} className="mr-2" />
            {isLoading ? 'Saving...' : 'Save User'}
          </button>
        </div>
      </form>
    </div>
  )
}
