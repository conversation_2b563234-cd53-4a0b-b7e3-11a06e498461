'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import type { AgentFilterCriteria } from '@/components/search/AgentSearchFilter'

interface AgentUser {
  id: number
  username: string
  email: string
  firstName: string
  lastName: string
  role: 'agent' | 'supervisor'
  isActive: boolean
  isOnline: boolean
  lastSeenAt?: string
  createdAt: string
  updatedAt: string
  // Performance metrics (would come from analytics API)
  totalSessions?: number
  averageResponseTime?: number
  customerSatisfactionScore?: number
  completionRate?: number
}

interface UseAgentSearchProps {
  agents: AgentUser[]
  initialFilters?: Partial<AgentFilterCriteria>
}

interface UseAgentSearchReturn {
  filteredAgents: AgentUser[]
  isSearching: boolean
  totalResults: number
  handleFilterChange: (filters: AgentFilterCriteria) => void
  currentFilters: AgentFilterCriteria
  searchStats: {
    totalAgents: number
    onlineAgents: number
    filteredCount: number
    averagePerformance: number
  }
}

const defaultFilters: AgentFilterCriteria = {
  searchTerm: '',
  status: 'all',
  role: 'all',
  performanceRating: 'all',
  availability: 'all',
  dateRange: {
    from: '',
    to: '',
  },
  sessionCount: {
    min: 0,
    max: 1000,
  },
  responseTime: {
    min: 0,
    max: 300,
  },
  satisfactionScore: {
    min: 1,
    max: 5,
  },
}

export function useAgentSearch({
  agents,
  initialFilters,
}: UseAgentSearchProps): UseAgentSearchReturn {
  const [filters, setFilters] = useState<AgentFilterCriteria>({
    ...defaultFilters,
    ...initialFilters,
  })
  const [isSearching, setIsSearching] = useState(false)

  const handleFilterChange = useCallback((newFilters: AgentFilterCriteria) => {
    setIsSearching(true)
    setFilters(newFilters)

    // Simulate search delay for better UX
    setTimeout(() => {
      setIsSearching(false)
    }, 200)
  }, [])

  const filteredAgents = useMemo(() => {
    let filtered = [...agents]

    // Text search
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase()
      filtered = filtered.filter(
        agent =>
          agent.firstName.toLowerCase().includes(searchTerm) ||
          agent.lastName.toLowerCase().includes(searchTerm) ||
          agent.email.toLowerCase().includes(searchTerm) ||
          agent.username.toLowerCase().includes(searchTerm) ||
          agent.id.toString().includes(searchTerm)
      )
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(agent => {
        if (filters.status === 'online') return agent.isOnline
        if (filters.status === 'offline') return !agent.isOnline
        return true
      })
    }

    // Role filter
    if (filters.role !== 'all') {
      filtered = filtered.filter(agent => agent.role === filters.role)
    }

    // Performance rating filter - only apply if agent has performance data
    if (filters.performanceRating !== 'all') {
      filtered = filtered.filter(agent => {
        const score = agent.customerSatisfactionScore
        // If no performance data, skip this filter
        if (score === undefined) return true
        switch (filters.performanceRating) {
          case 'excellent':
            return score >= 4.5
          case 'good':
            return score >= 4.0 && score < 4.5
          case 'average':
            return score >= 3.0 && score < 4.0
          case 'poor':
            return score < 3.0
          default:
            return true
        }
      })
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter(agent => {
        const agentDate = new Date(agent.lastSeenAt || agent.createdAt)
        const fromDate = filters.dateRange.from
          ? new Date(filters.dateRange.from)
          : null
        const toDate = filters.dateRange.to
          ? new Date(filters.dateRange.to)
          : null

        if (fromDate && agentDate < fromDate) return false
        if (toDate && agentDate > toDate) return false
        return true
      })
    }

    // Session count filter - only apply if agent has session data
    filtered = filtered.filter(agent => {
      const sessionCount = agent.totalSessions
      // If no session data, skip this filter
      if (sessionCount === undefined) return true
      return (
        sessionCount >= filters.sessionCount.min &&
        sessionCount <= filters.sessionCount.max
      )
    })

    // Response time filter - only apply if agent has response time data
    filtered = filtered.filter(agent => {
      const responseTime = agent.averageResponseTime
      // If no response time data, skip this filter
      if (responseTime === undefined) return true
      return (
        responseTime >= filters.responseTime.min &&
        responseTime <= filters.responseTime.max
      )
    })

    // Satisfaction score filter - only apply if agent has satisfaction score data
    filtered = filtered.filter(agent => {
      const score = agent.customerSatisfactionScore
      // If no satisfaction score data, skip this filter
      if (score === undefined) return true
      return (
        score >= filters.satisfactionScore.min &&
        score <= filters.satisfactionScore.max
      )
    })

    return filtered
  }, [agents, filters])

  const searchStats = useMemo(() => {
    const totalAgents = agents.length
    const onlineAgents = agents.filter(agent => agent.isOnline).length
    const filteredCount = filteredAgents.length
    const averagePerformance =
      filteredAgents.length > 0
        ? filteredAgents.reduce(
            (sum, agent) => sum + (agent.customerSatisfactionScore || 0),
            0
          ) / filteredAgents.length
        : 0

    return {
      totalAgents,
      onlineAgents,
      filteredCount,
      averagePerformance,
    }
  }, [agents, filteredAgents])

  return {
    filteredAgents,
    isSearching,
    totalResults: filteredAgents.length,
    handleFilterChange,
    currentFilters: filters,
    searchStats,
  }
}

// Helper function to get performance rating text
export function getPerformanceRating(score: number): string {
  if (score >= 4.5) return 'Excellent'
  if (score >= 4.0) return 'Good'
  if (score >= 3.0) return 'Average'
  return 'Poor'
}

// Helper function to get performance rating color
export function getPerformanceRatingColor(score: number): string {
  if (score >= 4.5) return 'text-green-600 bg-green-100'
  if (score >= 4.0) return 'text-blue-600 bg-blue-100'
  if (score >= 3.0) return 'text-yellow-600 bg-yellow-100'
  return 'text-red-600 bg-red-100'
}

// Helper function to format search results for export
export function formatSearchResults(
  agents: AgentUser[],
  filters: AgentFilterCriteria
) {
  return {
    filters,
    results: agents.map(agent => ({
      id: agent.id,
      name: `${agent.firstName} ${agent.lastName}`,
      email: agent.email,
      role: agent.role,
      status: agent.isOnline ? 'Online' : 'Offline',
      totalSessions: agent.totalSessions || 0,
      averageResponseTime: agent.averageResponseTime || 0,
      satisfactionScore: agent.customerSatisfactionScore || 0,
      completionRate: agent.completionRate || 0,
      lastSeen: agent.lastSeenAt || agent.updatedAt,
    })),
    summary: {
      totalResults: agents.length,
      exportedAt: new Date().toISOString(),
    },
  }
}
