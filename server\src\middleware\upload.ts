import path from 'node:path'
import type { NextFunction, Request, Response } from 'express'
import multer from 'multer'
import { v4 as uuidv4 } from 'uuid'

// Configure storage
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = path.join(
      __dirname,
      '..',
      '..',
      process.env.UPLOAD_DIR || 'uploads',
    )
    cb(null, uploadDir)
  },
  filename: (_req, file, cb) => {
    // Generate unique filename with original extension
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`
    cb(null, uniqueName)
  },
})

// File filter for images, audio, and documents
const fileFilter = (
  _req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback,
) => {
  const allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ]
  const allowedAudioTypes = [
    'audio/mpeg',
    'audio/wav',
    'audio/webm',
    'audio/ogg',
  ]
  const allowedDocumentTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/pdf', // .pdf
    'text/plain', // .txt
    'text/markdown', // .md
    'text/asciidoc', // .asciidoc
    'text/csv', // .csv
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  ]

  if (
    allowedImageTypes.includes(file.mimetype) ||
    allowedAudioTypes.includes(file.mimetype) ||
    allowedDocumentTypes.includes(file.mimetype)
  ) {
    cb(null, true)
  } else {
    console.error('Invalid file type:', file.mimetype)
    cb(
      new Error(
        'Invalid file type. Allowed types: images (JPEG, PNG, GIF, WebP), audio files (MP3, WAV, WebM, OGG), and documents (DOCX, PDF, TXT, MD, ASCIIDOC, CSV, XLSX).',
      ),
    )
  }
}

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize:
      Number.parseInt(process.env.MAX_FILE_SIZE || '52428800') ||
      50 * 1024 * 1024, // 50MB default for documents
    files: 1, // Only allow one file at a time
  },
  fileFilter: fileFilter,
})

// Middleware for single file upload
const uploadSingle = upload.single('file')

// Error handling wrapper
const handleUpload = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  uploadSingle(req, res, (err: any) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        res.status(400).json({
          error: 'File too large',
          message: 'File size exceeds the maximum limit of 10MB',
        })
        return
      }
      if (err.code === 'LIMIT_FILE_COUNT') {
        res.status(400).json({
          error: 'Too many files',
          message: 'Only one file can be uploaded at a time',
        })
        return
      }
      res.status(400).json({
        error: 'Upload error',
        message: err.message,
      })
      return
    }
    if (err) {
      res.status(400).json({
        error: 'Invalid file',
        message: err.message,
      })
      return
    }
    next()
  })
}

export { handleUpload, upload }
