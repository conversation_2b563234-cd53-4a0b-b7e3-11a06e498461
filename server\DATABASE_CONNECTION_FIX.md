# PostgreSQL "Too Many Clients" Error - Fix Documentation

## Problem

The server was encountering `PostgresError: sorry, too many clients already` errors due to improper database connection management.

## Root Causes Identified

1. **No Connection Pooling Configuration**: The `postgres-js` connections were created without proper pooling limits
2. **Multiple Database Connection Creation**: The `DatabaseService` class was creating new connections for every operation without reusing them
3. **No Connection Cleanup**: Connections were not being properly closed, leading to connection leaks
4. **Multiple Server Instances**: Multiple server processes were running simultaneously, each creating their own connections

## Solutions Implemented

### 1. Connection Pooling Configuration

Updated all database connection files to include proper pooling options:

```typescript
const sql = postgres(databaseUrl, {
  prepare: false, // Required for compatibility
  max: 5, // Maximum connections in pool (reduced for local dev)
  idle_timeout: 20, // Close idle connections after 20 seconds
  max_lifetime: 60 * 30, // Close connections after 30 minutes
  connect_timeout: 10, // Connection timeout in seconds
  debug: false, // Disable debug logging
})
```

### 2. Connection Manager

Created a centralized `ConnectionManager` utility (`server/src/utils/connectionManager.ts`) that:

- Manages connection pools as singletons
- Provides proper cleanup mechanisms
- Monitors connection counts
- Handles graceful shutdown

### 3. Updated Files

The following files were updated with proper connection pooling:

- `server/src/db/connection.ts` - Main database connection with ConnectionManager
- `selangor/src/lib/db/index.ts` - Frontend database connection (max: 3)
- `crawl-product/src/db/connection.ts` - Crawling database connection (max: 3)
- `crawl-org/src/db/connection.ts` - Crawling database connection (max: 3)
- `selangor/src/app/api/test-db/route.ts` - Test endpoint with proper cleanup

### 4. Health Monitoring

Added comprehensive health check endpoints:

- `GET /health` - Basic server health
- `GET /health/db` - Database connection status
- `GET /health/connections` - Connection pool status

### 5. Process Management

Killed existing server processes that were holding connections:

```bash
pkill -f "tsx.*server.ts"
pkill -f "bun.*server.ts"
```

## Results

### Before Fix

- Database connections: 100+ (hitting PostgreSQL max_connections limit)
- Server: Frequent "too many clients" errors
- Connection leaks: Yes

### After Fix

- Database connections: 6 total (5 background + 1 active)
- Server: Running stable without errors
- Connection leaks: None
- Connection pool: Properly managed with limits

## Connection Pool Configuration by Service

| Service             | Max Connections | Purpose                              |
| ------------------- | --------------- | ------------------------------------ |
| Server (main)       | 5               | API requests and database operations |
| Frontend (Selangor) | 3               | Next.js API routes                   |
| Crawl Product       | 3               | Product crawling operations          |
| Crawl Org           | 3               | Organization crawling operations     |
| Test Endpoints      | 1               | Testing with immediate cleanup       |

## Monitoring

Use the health endpoints to monitor connection status:

```bash
# Check overall health
curl http://localhost:16001/health

# Check database connection
curl http://localhost:16001/health/db

# Check connection pool status
curl http://localhost:16001/health/connections
```

## Best Practices Implemented

1. **Connection Reuse**: Use singleton pattern for database connections
2. **Proper Pooling**: Configure appropriate pool sizes for each service
3. **Graceful Shutdown**: Handle SIGTERM/SIGINT to close connections properly
4. **Connection Monitoring**: Track active connections for debugging
5. **Error Handling**: Ensure connections are closed even on errors
6. **Timeout Configuration**: Set appropriate timeouts to prevent hanging connections

## Future Recommendations

1. Consider using a dedicated connection pooler like PgBouncer for production
2. Monitor connection metrics in production
3. Implement connection retry logic for transient failures
4. Consider using read replicas for read-heavy operations
5. Set up alerts for connection pool exhaustion

## Testing

The fix has been tested and verified:

- Server starts without errors
- Database connections remain stable
- Health endpoints return proper status
- Connection count stays within limits
- No more "too many clients" errors
