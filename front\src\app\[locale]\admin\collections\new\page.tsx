'use client';

export const runtime = 'edge';

import { ArrowLeft, Save } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { Link, useRouter } from '@/i18n/navigation';
import { useCollectionsStore } from '@/stores/collections';
import { CollectionStatus, type CollectionFormData } from '@/types/collection';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

const statusOptions = [
  { value: CollectionStatus.ACTIVE, label: 'Active' },
  { value: CollectionStatus.DISABLED, label: 'Disabled' },
];

export default function NewCollectionPage() {
  const router = useRouter();
  const { createCollection, isLoading, error, clearError } = useCollectionsStore();

  // Auth guard
  useAdminAuthGuard();

  const [formData, setFormData] = useState<CollectionFormData>({
    name: '',
    status: CollectionStatus.ACTIVE,
  });

  const handleInputChange = (field: keyof CollectionFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  const handleStatusChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      status: value as CollectionStatus,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.name.trim()) {
      return;
    }

    const success = await createCollection({
      name: formData.name.trim(),
      status: formData.status,
    });

    if (success) {
      router.push('/admin/collections');
    }
  };

  const isFormValid = formData.name.trim().length > 0;

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/collections">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Collections
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">New Collection</h1>
          <p className="text-gray-600">Create a new document collection</p>
        </div>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Collection Details</CardTitle>
          <CardDescription>
            Enter the details for the new document collection.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="mt-2"
              >
                Dismiss
              </Button>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Collection Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={handleInputChange('name')}
                placeholder="Enter collection name..."
                disabled={isLoading}
                required
              />
              <p className="text-sm text-gray-500">
                A descriptive name for your document collection
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={handleStatusChange}
                disabled={isLoading}
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
              <p className="text-sm text-gray-500">
                Active collections can have documents added to them
              </p>
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <Button type="submit" disabled={isLoading || !isFormValid}>
                <Save className="mr-2 h-4 w-4" />
                {isLoading ? 'Creating...' : 'Create Collection'}
              </Button>
              <Link href="/admin/collections">
                <Button type="button" variant="outline" disabled={isLoading}>
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
