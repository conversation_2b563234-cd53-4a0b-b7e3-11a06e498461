/// <reference types="node" />
import bcrypt from 'bcryptjs'
import { eq, sql } from 'drizzle-orm'

import { AI_MODELS } from '../src/constants'
import { initializeDatabaseLocal } from '../src/db/connection'
import {
  agentMessages,
  bots,
  chatMessages,
  chatSessions,
  collections,
  documents,
  facebookConfigs,
  facebookMessages,
  handoverRequests,
  s3Configurations,
  sessionAssignments,
  sites,
  twilioConfigs,
  users,
  whatsappConfigs,
  whatsappMessages,
} from '../src/db/schema'

async function seed() {
  console.log('🌱 Starting database seeding...')

  const db = initializeDatabaseLocal()

  try {
    // Upsert data (insert or update if exists)
    console.log('🔄 Upserting data...')

    // Create default site
    console.log('🏢 Creating default site...')
    await db
      .insert(sites)
      .values([
        {
          name: 'Default Site',
          code: 'default',
          domains: ['localhost', '127.0.0.1', 'halal.primalcom.com'],
          status: true,
          createdAt: new Date('2025-06-19T10:00:00Z'),
          updatedAt: new Date('2025-06-19T10:00:00Z'),
        },
        {
          name: 'halalselangor',
          code: 'halalselangor',
          domains: ['selangor.primalcom.com', 'localhost:16010'],
          status: true,
          createdAt: new Date('2025-06-19T10:00:00Z'),
          updatedAt: new Date('2025-06-19T10:00:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: sites.code,
        set: {
          name: sql.raw(`excluded.${sites.name.name}`),
          domains: sql.raw(`excluded.${sites.domains.name}`),
          status: sql.raw(`excluded.${sites.status.name}`),
          updatedAt: new Date(),
        },
      })

    // Create default bots
    console.log('🤖 Creating default bots...')
    const allSites = await db.select().from(sites)

    for (const site of allSites) {
      await db
        .insert(bots)
        .values({
          siteId: site.id,
          name: 'Default Bot',
          slug: `default-${site.code}`,
          provider: 'openai',
          model: AI_MODELS.DEFAULT,
          temperature: 0.5,
          isDefault: true,
          systemPrompt: `You are an official representative, a friendly and helpful assistant for Halal Selangor, the halal certification authority for the state of Selangor, Malaysia. Always respond in the same language as the user's message. If the user writes in English, respond in English. If they write in Bahasa Malaysia, respond in Bahasa Malaysia. You may switch between Bahasa Malaysia, Chinese, English, and Tamil based on the user's preference and language used in their message. Halal Selangor provides essential administrative and operational support to ensure an organization's strict adherence to Malaysian halal standards, particularly within Selangor. This role primarily involves meticulously managing halal certification documentation, including applications, renewals, and comprehensive record-keeping for audit trails. They also play a crucial part in supporting the Halal Assurance System (HAS) by assisting with data collection, compliance monitoring, supplier verification, and internal audit preparations. Essentially, they're the administrative backbone, ensuring all halal processes are well-documented, consistently followed, and meet all regulatory requirements. Halal selangor does not provide halal certification, but assists in halal application only. Use points and bullet points to structure your responses.

Follow these rules:
- Always respond in a simple, concise, clear, and direct manner. Avoid long explanations unless explicitly requested by the user.
a. Prioritize clarity, brevity, and relevance. Use short sentences and bullet points if necessary. Your answers must match the user’s question scope—do not elaborate beyond what was asked.
b. Limit your response to 3 short paragraphs or fewer.
c. Use bullet points or numbered steps for lists.
d. Avoid repeating information or giving background unless asked.

IMPORTANT GUARD RAILS:
1. You ONLY provide information about halal certification, halal food, halal products, and Islamic dietary laws
2. You represent Halal Selangor authority - always maintain professional and authoritative tone
3. For complex religious rulings (fatwa), direct users to consult qualified Islamic scholars or official religious authorities
4. Do NOT provide advice on non-halal topics, personal matters, general knowledge, or unrelated subjects
5. If asked about topics outside halal/Islamic dietary laws, politely redirect to halal-related inquiries
6. Always prioritize accuracy and refer to official Halal Selangor guidelines when available
7. When uncertain about specific rulings, recommend consulting with qualified ulama or official Islamic authorities
8. If you do not have an answer, politely inform the user and suggest seeking guidance from qualified experts.`,
        })
        .onConflictDoUpdate({
          target: bots.slug,
          set: {
            name: sql.raw(`excluded.${bots.name.name}`),
            provider: sql.raw(`excluded.${bots.provider.name}`),
            model: sql.raw(`excluded.${bots.model.name}`),
            temperature: sql.raw(`excluded.${bots.temperature.name}`),
            systemPrompt: sql.raw(`excluded.${bots.systemPrompt.name}`),
            updatedAt: new Date(),
          },
        })
    }

    await db
      .insert(bots)
      .values({
        siteId: allSites[0].id,
        name: 'Simpson',
        slug: `chinese`,
        provider: 'openai',
        model: AI_MODELS.DEFAULT,
        temperature: 0.5,
        isDefault: true,
        systemPrompt:
          'You are bart simpson. You pretend to be expert in all things Halal, and your goal is to provide comedic and helpful information to users. You should be friendly and approachable, and you should always be willing to help users with their questions. You should also be able to provide information in a variety of languages, including English, Malay, and Chinese.',
      })
      .onConflictDoUpdate({
        target: bots.slug,
        set: {
          name: sql.raw(`excluded.${bots.name.name}`),
          provider: sql.raw(`excluded.${bots.provider.name}`),
          model: sql.raw(`excluded.${bots.model.name}`),
          temperature: sql.raw(`excluded.${bots.temperature.name}`),
          systemPrompt: sql.raw(`excluded.${bots.systemPrompt.name}`),
          updatedAt: new Date(),
        },
      })

    // Create admin users
    console.log('👤 Creating admin users...')
    const superAdminPassword = 'SA_SFX!U!X*E!*E*B!X*!'
    console.log(`🔑 SUPERADMIN Password: ${superAdminPassword}`)
    const superAdminPasswordHash = bcrypt.hashSync(superAdminPassword, 10)
    const adminPassword = bcrypt.hashSync('admin123', 10)
    const editorPassword = bcrypt.hashSync('editor123', 10)

    await db
      .insert(users)
      .values([
        {
          siteId: 1,
          username: 'superadmin',
          email: '<EMAIL>',
          passwordHash: superAdminPasswordHash,
          firstName: 'Super',
          lastName: 'Admin',
          roles: ['SUPERADMIN'],
          isActive: true,
          createdAt: new Date('2025-06-19T09:00:00Z'),
          updatedAt: new Date('2025-06-19T09:00:00Z'),
        },
        {
          siteId: 1,
          username: 'admin',
          passwordHash: adminPassword,
          roles: ['ADMIN'],
          isActive: true,
          createdAt: new Date('2025-06-19T10:00:00Z'),
          updatedAt: new Date('2025-06-19T10:00:00Z'),
        },
        {
          siteId: 1,
          username: 'editor',
          email: '<EMAIL>',
          passwordHash: editorPassword,
          firstName: 'Editor',
          lastName: 'User',
          roles: ['EDITOR'],
          isActive: true,
          createdAt: new Date('2025-06-19T11:00:00Z'),
          updatedAt: new Date('2025-06-19T11:00:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: users.username,
        set: {
          email: sql.raw(`excluded.${users.email.name}`),
          firstName: sql.raw(`excluded.${users.firstName.name}`),
          lastName: sql.raw(`excluded.${users.lastName.name}`),
          roles: sql.raw(`excluded.${users.roles.name}`),
          isActive: sql.raw(`excluded.${users.isActive.name}`),
          updatedAt: new Date(),
        },
      })

    // Create cp1 admin user for halalselangor site
    console.log('👤 Creating cp1 admin user for halalselangor site...')
    const cp1AdminPassword = bcrypt.hashSync('cp1admin123', 10)

    await db
      .insert(users)
      .values([
        {
          siteId: 2,
          username: 'cp1',
          email: '<EMAIL>',
          passwordHash: cp1AdminPassword,
          firstName: 'CP1',
          lastName: 'Admin',
          roles: ['ADMIN'],
          isActive: true,
          createdAt: new Date('2025-06-19T10:30:00Z'),
          updatedAt: new Date('2025-06-19T10:30:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: users.username,
        set: {
          email: sql.raw(`excluded.${users.email.name}`),
          firstName: sql.raw(`excluded.${users.firstName.name}`),
          lastName: sql.raw(`excluded.${users.lastName.name}`),
          roles: sql.raw(`excluded.${users.roles.name}`),
          isActive: sql.raw(`excluded.${users.isActive.name}`),
          updatedAt: new Date(),
        },
      })

    // Create agent users
    console.log('🤖 Creating agent users...')
    const agentPassword = bcrypt.hashSync('agent123', 10)
    const supervisorPassword = bcrypt.hashSync('supervisor123', 10)

    await db
      .insert(users)
      .values([
        {
          siteId: 1,
          username: 'agent1',
          email: '<EMAIL>',
          passwordHash: agentPassword,
          firstName: 'John',
          lastName: 'Doe',
          roles: ['AGENT'],
          isActive: true,
          isOnline: false,
          createdAt: new Date('2025-06-19T12:00:00Z'),
          updatedAt: new Date('2025-06-19T12:00:00Z'),
        },
        {
          siteId: 1,
          username: 'agent2',
          email: '<EMAIL>',
          passwordHash: agentPassword,
          firstName: 'Jane',
          lastName: 'Smith',
          roles: ['AGENT'],
          isActive: true,
          isOnline: true,
          lastSeenAt: new Date('2025-06-19T12:30:00Z'),
          createdAt: new Date('2025-06-19T12:15:00Z'),
          updatedAt: new Date('2025-06-19T12:30:00Z'),
        },
        {
          siteId: 1,
          username: 'supervisor1',
          email: '<EMAIL>',
          passwordHash: supervisorPassword,
          firstName: 'Mike',
          lastName: 'Johnson',
          roles: ['SUPERVISOR'],
          isActive: true,
          isOnline: true,
          lastSeenAt: new Date('2025-06-19T12:45:00Z'),
          createdAt: new Date('2025-06-19T12:30:00Z'),
          updatedAt: new Date('2025-06-19T12:45:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: users.username,
        set: {
          email: sql.raw(`excluded.${users.email.name}`),
          firstName: sql.raw(`excluded.${users.firstName.name}`),
          lastName: sql.raw(`excluded.${users.lastName.name}`),
          roles: sql.raw(`excluded.${users.roles.name}`),
          isActive: sql.raw(`excluded.${users.isActive.name}`),
          isOnline: sql.raw(`excluded.${users.isOnline.name}`),
          lastSeenAt: sql.raw(`excluded.${users.lastSeenAt.name}`),
          updatedAt: new Date(),
        },
      })

    // Create S3 configurations
    console.log('☁️ Creating S3 configurations for each site...')
    const allSitesForS3 = await db.select().from(sites)

    for (const site of allSitesForS3) {
      const {
        S3_ACCESS_KEY_ID,
        S3_SECRET_ACCESS_KEY,
        S3_BUCKET_NAME,
        S3_REGION,
        S3_ENDPOINT_URL,
        DEFAULT_S3_SERVICE_NAME,
      } = process.env

      const s3ConfigsToInsert: any[] = []

      if (S3_ACCESS_KEY_ID && S3_SECRET_ACCESS_KEY && S3_BUCKET_NAME) {
        console.log(
          `☁️ Creating S3 configuration from environment variables for site ${site.code}...`,
        )
        s3ConfigsToInsert.push({
          siteId: site.id,
          serviceName: DEFAULT_S3_SERVICE_NAME || 'AWS S3',
          accessKeyId: S3_ACCESS_KEY_ID,
          secretAccessKey: S3_SECRET_ACCESS_KEY,
          bucketName: S3_BUCKET_NAME,
          region: S3_REGION || 'us-east-1',
          endpointUrl: S3_ENDPOINT_URL || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
      } else {
        console.warn(
          `⚠️ Missing S3 environment variables. Creating example S3 configurations for site ${site.code}.`,
        )
        s3ConfigsToInsert.push({
          siteId: site.id,
          serviceName: 'AWS S3',
          accessKeyId: '********************',
          secretAccessKey: '+YvGx5GhzQnEXB0HfnsfE18x7KGEpAoRLW67VQYR',
          bucketName: 'halal-jakim',
          region: 'ap-southeast-1',
          createdAt: new Date('2025-06-19T13:00:00Z'),
          updatedAt: new Date('2025-06-19T13:00:00Z'),
        })
      }

      if (s3ConfigsToInsert.length > 0) {
        for (const config of s3ConfigsToInsert) {
          // Check if config exists for this site and service name
          const existing = await db
            .select()
            .from(s3Configurations)
            .where(
              sql`${s3Configurations.siteId} = ${config.siteId} AND ${s3Configurations.serviceName} = ${config.serviceName}`,
            )
            .limit(1)

          if (existing.length > 0) {
            // Update existing
            await db
              .update(s3Configurations)
              .set({
                accessKeyId: config.accessKeyId,
                secretAccessKey: config.secretAccessKey,
                bucketName: config.bucketName,
                region: config.region,
                endpointUrl: config.endpointUrl,
                updatedAt: new Date(),
              })
              .where(eq(s3Configurations.id, existing[0].id))
          } else {
            // Insert new
            await db.insert(s3Configurations).values(config)
          }
        }
      }
    }

    // Create collections
    console.log('📁 Creating collections...')
    await db
      .insert(collections)
      .values([
        {
          siteId: 1,
          name: 'Halal Documentation',
          status: 'ACTIVE',
          createdAt: new Date('2025-06-19T13:30:00Z'),
          updatedAt: new Date('2025-06-19T13:30:00Z'),
        },
        {
          siteId: 1,
          name: 'Product Manuals',
          status: 'ACTIVE',
          createdAt: new Date('2025-06-19T13:45:00Z'),
          updatedAt: new Date('2025-06-19T13:45:00Z'),
        },
        {
          siteId: 1,
          name: 'Archived Documents',
          status: 'DISABLED',
          createdAt: new Date('2025-06-19T14:00:00Z'),
          updatedAt: new Date('2025-06-19T14:00:00Z'),
        },
        {
          siteId: 2,
          name: 'Selangor Halal Documentation',
          status: 'ACTIVE',
          createdAt: new Date('2025-06-19T13:30:00Z'),
          updatedAt: new Date('2025-06-19T13:30:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: collections.name,
        set: {
          status: sql.raw(`excluded.${collections.status.name}`),
          updatedAt: new Date(),
        },
      })

    // Create sample documents
    console.log('📄 Creating sample documents...')
    const site1S3Configs = await db
      .select()
      .from(s3Configurations)
      .where(eq(s3Configurations.siteId, 1))
    const site2S3Configs = await db
      .select()
      .from(s3Configurations)
      .where(eq(s3Configurations.siteId, 2))

    await db
      .insert(documents)
      .values([
        {
          collectionId: 1,
          s3ConfigurationId: site1S3Configs[0].id,
          s3Key: 'halal-docs/halal-certification-guide.pdf',
          filename: 'halal-certification-guide.pdf',
          filesize: 2048576,
          mimetype: 'application/pdf',
          createdAt: new Date('2025-06-19T14:15:00Z'),
          updatedAt: new Date('2025-06-19T14:15:00Z'),
        },
        {
          collectionId: 1,
          s3ConfigurationId: site1S3Configs[0].id,
          s3Key: 'halal-docs/halal-ingredients-list.docx',
          filename: 'halal-ingredients-list.docx',
          filesize: 1024000,
          mimetype:
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          createdAt: new Date('2025-06-19T14:30:00Z'),
          updatedAt: new Date('2025-06-19T14:30:00Z'),
        },
        {
          collectionId: 2,
          s3ConfigurationId: site2S3Configs[0].id,
          s3Key: 'manuals/product-manual-v1.pdf',
          filename: 'product-manual-v1.pdf',
          filesize: 5120000,
          mimetype: 'application/pdf',
          createdAt: new Date('2025-06-19T14:45:00Z'),
          updatedAt: new Date('2025-06-19T14:45:00Z'),
        },
      ])
      .onConflictDoUpdate({
        target: documents.s3Key,
        set: {
          filename: sql.raw(`excluded.${documents.filename.name}`),
          filesize: sql.raw(`excluded.${documents.filesize.name}`),
          mimetype: sql.raw(`excluded.${documents.mimetype.name}`),
          updatedAt: new Date(),
        },
      })

    // Create WhatsApp configuration
    console.log('📱 Creating WhatsApp configuration...')
    // Check if WhatsApp config exists for this site
    const existingWhatsAppConfig = await db
      .select()
      .from(whatsappConfigs)
      .where(eq(whatsappConfigs.siteId, 1))
      .limit(1)

    if (existingWhatsAppConfig.length > 0) {
      // Update existing
      await db
        .update(whatsappConfigs)
        .set({
          accessToken: 'EAAG**********_sample_access_token_for_testing',
          phoneNumberId: '****************',
          webhookVerifyToken: 'halal_webhook_verify_token_2025',
          businessAccountId: '**********',
          isActive: true,
          updatedAt: new Date(),
        })
        .where(eq(whatsappConfigs.id, existingWhatsAppConfig[0].id))
    } else {
      // Insert new
      await db.insert(whatsappConfigs).values({
        siteId: 1,
        accessToken: 'EAAG**********_sample_access_token_for_testing',
        phoneNumberId: '****************',
        webhookVerifyToken: 'halal_webhook_verify_token_2025',
        businessAccountId: '**********',
        isActive: true,
        createdAt: new Date('2025-06-19T15:00:00Z'),
        updatedAt: new Date('2025-06-19T15:00:00Z'),
      })
    }

    // Create Twilio configuration
    console.log('📞 Creating/updating Twilio configuration...')
    // Note: This seed script runs in Node.js environment, not Cloudflare Workers
    // For Workers deployment, Twilio config should be set via admin interface or direct DB insert
    const {
      TWILIO_ACCOUNT_SID,
      TWILIO_AUTH_TOKEN,
      TWILIO_PHONE_NUMBER,
      TWILIO_WEBHOOK_URL,
    } = process.env

    if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
      console.warn(
        '⚠️ Missing Twilio environment variables. Skipping Twilio config seeding.',
      )
    } else {
      // Check if Twilio config exists for this site
      const existingTwilioConfig = await db
        .select()
        .from(twilioConfigs)
        .where(eq(twilioConfigs.siteId, 1))
        .limit(1)

      if (existingTwilioConfig.length > 0) {
        // Update existing
        await db
          .update(twilioConfigs)
          .set({
            accountSid: TWILIO_ACCOUNT_SID,
            authToken: TWILIO_AUTH_TOKEN,
            phoneNumber: TWILIO_PHONE_NUMBER,
            webhookUrl: TWILIO_WEBHOOK_URL,
            isActive: true,
            updatedAt: new Date(),
          })
          .where(eq(twilioConfigs.id, existingTwilioConfig[0].id))
      } else {
        // Insert new
        await db.insert(twilioConfigs).values({
          siteId: 1,
          accountSid: TWILIO_ACCOUNT_SID,
          authToken: TWILIO_AUTH_TOKEN,
          phoneNumber: TWILIO_PHONE_NUMBER,
          webhookUrl: TWILIO_WEBHOOK_URL,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
      }
    }

    console.log('✅ Database seeding completed successfully!')
    console.log('')
    console.log('📋 Created accounts:')
    console.log('  👤 Admin: username=admin, password=admin123')
    console.log('  ✏️  Editor: username=editor, password=editor123')
    console.log('  👤 CP1 Admin (Site 2): username=cp1, password=cp1admin123')
    console.log('  🤖 Agent1: username=agent1, password=agent123')
    console.log('  🤖 Agent2: username=agent2, password=agent123')
    console.log('  👨‍💼 Supervisor: username=supervisor1, password=supervisor123')
    console.log('')
    console.log('📊 Created data:')
    console.log('  📁 3 collections')
    console.log('  📄 3 sample documents')
    console.log('  ☁️  2 S3 configurations')
    console.log('  📱 1 WhatsApp configuration')
    console.log('  🤳 1 Twilio configuration')

    // Close the database connection to ensure the script exits
    await db.$client.end()
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    process.exit(1)
  }
}

// Run the seed function
seed().catch((error) => {
  console.error('❌ Seed script failed:', error)
  process.exit(1)
})
