#!/usr/bin/env tsx

/**
 * Product Vectorization Script
 *
 * This script generates embeddings for all products in the database
 * and populates the embedding column for semantic search functionality.
 */

import { config } from 'dotenv'
import { sql } from 'drizzle-orm'
import { initializeDatabaseLocal } from '../src/db/connection'
import { products } from '../src/db/schema'

// Load environment variables
config({ path: '.env.local' })

// Initialize database connection
const db = initializeDatabaseLocal()

interface ProductRow {
  id: number
  productName: string
  companyName: string
  category: string | null
  subcategory: string | null
  certificateType: string | null
  status: string | null
  address: string | null
  vectorizedAt: string | null
}

interface EmbeddingResult {
  embedding: number[]
  model: string
  usage?: {
    prompt_tokens: number
    total_tokens: number
  }
}

interface VectorizationStats {
  total: number
  processed: number
  skipped: number
  errors: number
  startTime: Date
  endTime?: Date
}

class ProductVectorizer {
  private stats: VectorizationStats
  private batchSize = 10 // Process in batches to avoid rate limits
  private delayMs = 1000 // Delay between batches
  private apiKey: string
  private defaultModel = 'text-embedding-3-large'

  constructor() {
    this.stats = {
      total: 0,
      processed: 0,
      skipped: 0,
      errors: 0,
      startTime: new Date(),
    }
    this.apiKey = process.env.OPENAI_API_KEY || ''
    if (!this.apiKey) {
      throw new Error('OPENAI_API_KEY is required for vectorization')
    }
  }

  /**
   * Create searchable text from product data
   */
  private createProductSearchableText(product: ProductRow): string {
    const parts = [
      product.productName,
      product.companyName,
      product.category,
      product.subcategory,
      product.status,
      product.address,
    ].filter(Boolean)

    console.log('createProductSearchableText parts', parts)

    return parts.join(' ')
  }

  /**
   * Generate embedding for text using OpenAI API
   */
  private async generateEmbedding(text: string): Promise<EmbeddingResult> {
    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty for embedding generation')
    }

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: text,
          model: this.defaultModel,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenAI API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ''
          }`,
        )
      }

      const data = await response.json()

      return {
        embedding: data.data[0].embedding,
        model: this.defaultModel,
        usage: data.usage,
      }
    } catch (error) {
      console.error('Error generating embedding:', error)
      throw error
    }
  }

  /**
   * Main vectorization process
   */
  async vectorizeProducts(forceReprocess = false): Promise<void> {
    console.log('🚀 Starting product vectorization...')
    console.log(`📊 Batch size: ${this.batchSize}, Delay: ${this.delayMs}ms`)
    console.log(`🔄 Force reprocess: ${forceReprocess}`)
    console.log('')

    try {
      // Get all products that need vectorization
      const productsToProcess =
        await this.getProductsToVectorize(forceReprocess)
      this.stats.total = productsToProcess.length

      console.log(`📦 Found ${this.stats.total} products to vectorize`)

      if (this.stats.total === 0) {
        console.log('✅ All products are already vectorized!')
        return
      }

      // Process products in batches
      for (let i = 0; i < productsToProcess.length; i += this.batchSize) {
        const batch = productsToProcess.slice(i, i + this.batchSize)
        await this.processBatch(batch, i + 1)

        // Add delay between batches to respect rate limits
        if (i + this.batchSize < productsToProcess.length) {
          console.log(`⏳ Waiting ${this.delayMs}ms before next batch...`)
          await this.delay(this.delayMs)
        }
      }

      this.stats.endTime = new Date()
      this.printFinalStats()
    } catch (error) {
      console.error('❌ Vectorization failed:', error)
      throw error
    }
  }

  /**
   * Get products that need vectorization
   */
  private async getProductsToVectorize(
    forceReprocess: boolean,
  ): Promise<ProductRow[]> {
    const whereClause = forceReprocess
      ? sql`1=1` // Get all products
      : sql`vectorized_at IS NULL` // Only get products that haven't been vectorized

    const result = await db
      .select({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
        category: products.category,
        subcategory: products.subcategory,
        // certificateType: products.certificateType,
        status: products.status,
        address: products.address,
        vectorizedAt: products.vectorizedAt,
      })
      .from(products)
      .where(whereClause)
      .orderBy(products.id)

    return result as ProductRow[]
  }

  /**
   * Process a batch of products
   */
  private async processBatch(
    batch: ProductRow[],
    batchNumber: number,
  ): Promise<void> {
    console.log(
      `\n🔄 Processing batch ${Math.ceil(batchNumber / this.batchSize)} (${batch.length} products)...`,
    )

    for (const product of batch) {
      try {
        await this.vectorizeProduct(product)
        this.stats.processed++

        // Show progress
        const progress = (
          ((this.stats.processed + this.stats.skipped + this.stats.errors) /
            this.stats.total) *
          100
        ).toFixed(1)
        console.log(
          `  ✅ [${progress}%] Product ${product.id}: ${product.productName}`,
        )
      } catch (error) {
        this.stats.errors++
        console.error(`  ❌ Error processing product ${product.id}:`, error)
      }
    }
  }

  /**
   * Vectorize a single product
   */
  private async vectorizeProduct(product: ProductRow): Promise<void> {
    // Create searchable text from product data
    const searchableText = this.createProductSearchableText(product)

    if (!searchableText) {
      throw new Error(`No searchable text generated for product ${product.id}`)
    }

    // Generate embedding
    const embeddingResult = await this.generateEmbedding(searchableText)

    // Update product with embedding
    // Convert embedding array to PostgreSQL vector format
    const vectorString = `[${embeddingResult.embedding.join(',')}]`

    // Use raw SQL to properly insert the vector
    await db.execute(sql`
      UPDATE products
      SET
        embedding = ${vectorString}::vector,
        vectorized_at = NOW(),
        vectorization_model = ${embeddingResult.model},
        searchable_text = ${searchableText}
      WHERE id = ${product.id}
    `)
  }

  /**
   * Print final statistics
   */
  private printFinalStats(): void {
    const duration = this.stats.endTime
      ? (this.stats.endTime.getTime() - this.stats.startTime.getTime()) / 1000
      : 0

    console.log('\n📊 Vectorization Complete!')
    console.log('================================')
    console.log(`📦 Total products: ${this.stats.total}`)
    console.log(`✅ Successfully processed: ${this.stats.processed}`)
    console.log(`⏭️  Skipped: ${this.stats.skipped}`)
    console.log(`❌ Errors: ${this.stats.errors}`)
    console.log(`⏱️  Duration: ${duration.toFixed(2)} seconds`)

    if (this.stats.processed > 0) {
      console.log(
        `⚡ Average: ${(duration / this.stats.processed).toFixed(2)} seconds per product`,
      )
    }

    console.log('')
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Verify vectorization results
   */
  async verifyVectorization(): Promise<void> {
    console.log('🔍 Verifying vectorization results...')

    const stats = await db.execute(sql`
      SELECT 
        COUNT(*) as total_products,
        COUNT(embedding) as vectorized_products,
        COUNT(*) - COUNT(embedding) as missing_embeddings,
        MIN(vectorized_at) as first_vectorized,
        MAX(vectorized_at) as last_vectorized
      FROM products
    `)

    const result = stats[0]
    console.log('\n📊 Vectorization Status:')
    console.log('========================')
    console.log(`📦 Total products: ${result.total_products}`)
    console.log(`✅ Vectorized: ${result.vectorized_products}`)
    console.log(`❌ Missing embeddings: ${result.missing_embeddings}`)

    if (result.first_vectorized) {
      console.log(`🕐 First vectorized: ${result.first_vectorized}`)
      console.log(`🕐 Last vectorized: ${result.last_vectorized}`)
    }

    console.log('')
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2)
  const forceReprocess = args.includes('--force') || args.includes('-f')
  const verifyOnly = args.includes('--verify') || args.includes('-v')

  const vectorizer = new ProductVectorizer()

  try {
    if (verifyOnly) {
      await vectorizer.verifyVectorization()
    } else {
      await vectorizer.vectorizeProducts(forceReprocess)
      await vectorizer.verifyVectorization()
    }
    process.exit(0)
  } catch (error) {
    console.error('💥 Script failed:', error)
    process.exit(1)
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error)
}
