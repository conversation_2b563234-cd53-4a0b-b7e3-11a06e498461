export interface SocialMediaPost {
  id: string;
  platform: "douyin" | "tiktok" | "instagram" | "facebook" | "youtube";
  url: string;
  title?: string;
  content?: string;
  author: {
    username: string;
    displayName?: string;
    avatarUrl?: string;
    verified?: boolean;
    followerCount?: number;
  };
  publishedAt?: Date;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
    views?: number;
  };
  hashtags: string[];
  mentions: string[];
  media: MediaItem[];
  location?: {
    name: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  metadata: Record<string, any>;
}

export interface MediaItem {
  type: "image" | "video" | "audio";
  url: string;
  thumbnailUrl?: string;
  duration?: number; // in seconds for video/audio
  dimensions?: {
    width: number;
    height: number;
  };
  fileSize?: number; // in bytes
  format?: string; // e.g., 'mp4', 'jpg', 'mp3'
  quality?: string; // e.g., '720p', 'HD', 'original'
}

export interface SearchResult {
  posts: SocialMediaPost[];
  pagination: {
    hasNext: boolean;
    nextCursor?: string;
    totalCount?: number;
    currentPage: number;
  };
  searchMetadata: {
    keyword: string;
    platform: string;
    searchedAt: Date;
    resultsCount: number;
    searchDuration: number; // in milliseconds
  };
}

export interface CrawlProgress {
  sessionId: number;
  platform: string;
  keywords: string[];
  status:
    | "initializing"
    | "searching"
    | "extracting"
    | "downloading"
    | "completed"
    | "failed"
    | "paused";
  progress: {
    totalPosts: number;
    processedPosts: number;
    successfulPosts: number;
    failedPosts: number;
    downloadedMedia: number;
    totalMedia: number;
  };
  currentActivity?: string;
  estimatedTimeRemaining?: number; // in milliseconds
  errors: CrawlError[];
}

export interface CrawlError {
  type:
    | "network"
    | "parsing"
    | "download"
    | "validation"
    | "rate_limit"
    | "unknown";
  message: string;
  url?: string;
  timestamp: Date;
  retryable: boolean;
  retryCount?: number;
}

export interface PlatformConfig {
  name: string;
  baseUrl: string;
  searchEndpoint?: string;
  rateLimits: {
    requestsPerMinute: number;
    downloadConcurrency: number;
  };
  selectors: {
    searchBox?: string;
    searchButton?: string;
    postContainer?: string;
    postLink?: string;
    authorName?: string;
    postContent?: string;
    mediaElements?: string;
    engagementStats?: string;
  };
  waitTimes: {
    pageLoad: number;
    searchDelay: number;
    scrollDelay: number;
    elementTimeout: number;
  };
}
