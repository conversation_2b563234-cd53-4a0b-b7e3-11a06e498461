'use client'

import Head from 'next/head'
import { useLanguage } from '@/hooks/use-language'
import { generateStructuredData, type StructuredDataConfig } from '@/lib/seo'

interface SEOMetadataProps {
  title: string
  titleBM?: string
  description: string
  descriptionBM?: string
  keywords?: string[]
  keywordsBM?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  structuredData?: StructuredDataConfig[]
  noIndex?: boolean
  canonical?: string
}

export function SEOMetadata({
  title,
  titleBM,
  description,
  descriptionBM,
  keywords,
  keywordsBM,
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
  structuredData = [],
  noIndex = false,
  canonical,
}: SEOMetadataProps) {
  const { language } = useLanguage()

  const currentTitle = language === 'bm' && titleBM ? titleBM : title
  const currentDescription =
    language === 'bm' && descriptionBM ? descriptionBM : description
  const currentKeywords =
    language === 'bm' && keywordsBM ? keywordsBM : keywords

  const siteName = 'Halal Malaysia Portal'
  const siteUrl = 'https://myehalal.halal.gov.my'
  const defaultImage = '/images/logos/jakim_logo.png'

  const fullTitle = currentTitle.includes(siteName)
    ? currentTitle
    : `${currentTitle} | ${siteName}`

  const fullUrl = url ? `${siteUrl}${url}` : siteUrl
  const imageUrl = image
    ? image.startsWith('http')
      ? image
      : `${siteUrl}${image}`
    : `${siteUrl}${defaultImage}`

  // Generate structured data JSON-LD
  const structuredDataScripts = structuredData.map((config, index) => {
    const data = generateStructuredData(config)
    return (
      <script
        key={`structured-data-${index}`}
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
      />
    )
  })

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={currentDescription} />
      {currentKeywords && (
        <meta name="keywords" content={currentKeywords.join(', ')} />
      )}
      <meta
        name="author"
        content={author || 'JAKIM - Jabatan Kemajuan Islam Malaysia'}
      />

      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}

      {/* Robots */}
      <meta
        name="robots"
        content={noIndex ? 'noindex,nofollow' : 'index,follow'}
      />
      <meta
        name="googlebot"
        content={
          noIndex
            ? 'noindex,nofollow'
            : 'index,follow,max-video-preview:-1,max-image-preview:large,max-snippet:-1'
        }
      />

      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={currentDescription} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:site_name" content={siteName} />
      <meta
        property="og:locale"
        content={language === 'bm' ? 'ms_MY' : 'en_MY'}
      />
      <meta property="og:type" content={type} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={currentTitle} />

      {/* Article specific Open Graph */}
      {type === 'article' && publishedTime && (
        <>
          <meta property="article:published_time" content={publishedTime} />
          {modifiedTime && (
            <meta property="article:modified_time" content={modifiedTime} />
          )}
          {author && <meta property="article:author" content={author} />}
          {section && <meta property="article:section" content={section} />}
          {tags?.map(tag => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={currentDescription} />
      <meta name="twitter:image" content={imageUrl} />
      <meta name="twitter:creator" content="@halal_malaysia" />
      <meta name="twitter:site" content="@halal_malaysia" />

      {/* Language Alternates */}
      <link rel="alternate" hrefLang="en" href={`${siteUrl}${url || '/'}`} />
      <link rel="alternate" hrefLang="ms" href={`${siteUrl}${url || '/'}`} />
      <link
        rel="alternate"
        hrefLang="x-default"
        href={`${siteUrl}${url || '/'}`}
      />

      {/* Favicon and Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link
        rel="icon"
        type="image/png"
        sizes="32x32"
        href="/favicon-32x32.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/favicon-16x16.png"
      />
      <link
        rel="apple-touch-icon"
        sizes="180x180"
        href="/apple-touch-icon.png"
      />
      <link rel="manifest" href="/site.webmanifest" />
      <meta name="theme-color" content="#059669" />

      {/* Additional Meta Tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content={siteName} />

      {/* Structured Data */}
      {structuredDataScripts}
    </Head>
  )
}

// Utility component for common page types
interface PageSEOProps {
  title: string
  titleBM?: string
  description: string
  descriptionBM?: string
  path?: string
  image?: string
  noIndex?: boolean
}

export function PageSEO({
  title,
  titleBM,
  description,
  descriptionBM,
  path,
  image,
  noIndex,
}: PageSEOProps) {
  return (
    <SEOMetadata
      title={title}
      titleBM={titleBM}
      description={description}
      descriptionBM={descriptionBM}
      url={path}
      image={image}
      noIndex={noIndex}
      structuredData={[
        {
          type: 'WebPage',
          title,
          description,
          url: path,
          image,
        },
      ]}
    />
  )
}

interface ArticleSEOProps {
  title: string
  titleBM?: string
  description: string
  descriptionBM?: string
  path: string
  image?: string
  publishedTime: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
}

export function ArticleSEO({
  title,
  titleBM,
  description,
  descriptionBM,
  path,
  image,
  publishedTime,
  modifiedTime,
  author,
  section,
  tags,
}: ArticleSEOProps) {
  return (
    <SEOMetadata
      title={title}
      titleBM={titleBM}
      description={description}
      descriptionBM={descriptionBM}
      url={path}
      image={image}
      type="article"
      publishedTime={publishedTime}
      modifiedTime={modifiedTime}
      author={author}
      section={section}
      tags={tags}
      structuredData={[
        {
          type: 'Article',
          title,
          description,
          url: path,
          image,
          datePublished: publishedTime,
          dateModified: modifiedTime || publishedTime,
          author: author ? { name: author, type: 'Person' } : undefined,
        },
      ]}
    />
  )
}

interface NewsSEOProps {
  title: string
  titleBM?: string
  description: string
  descriptionBM?: string
  path: string
  image?: string
  publishedTime: string
  modifiedTime?: string
  author?: string
}

export function NewsSEO({
  title,
  titleBM,
  description,
  descriptionBM,
  path,
  image,
  publishedTime,
  modifiedTime,
  author,
}: NewsSEOProps) {
  return (
    <SEOMetadata
      title={title}
      titleBM={titleBM}
      description={description}
      descriptionBM={descriptionBM}
      url={path}
      image={image}
      type="article"
      publishedTime={publishedTime}
      modifiedTime={modifiedTime}
      author={author}
      section="News"
      structuredData={[
        {
          type: 'NewsArticle',
          title,
          description,
          url: path,
          image,
          datePublished: publishedTime,
          dateModified: modifiedTime || publishedTime,
          author: author ? { name: author, type: 'Person' } : undefined,
        },
      ]}
    />
  )
}
