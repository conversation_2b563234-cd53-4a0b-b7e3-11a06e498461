'use client'

import { Plus } from 'lucide-react'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Link } from '@/i18n/navigation'
import { useBotsStore } from '@/stores/bots'
import { createColumns } from './columns'
import { BotsDataTable } from './data-table'

export default function BotsPage() {
  const { bots, isLoading, error, fetchBots, clearError } = useBotsStore()

  useEffect(() => {
    fetchBots()
  }, [fetchBots])

  // Create columns with refresh callback
  const columns = createColumns(() => {
    fetchBots() // Refresh the bots list after deletion
  })

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ×
            </Button>
          </div>
        </div>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>All Bots</CardTitle>
              <CardDescription>
                View and manage all bots in the system
              </CardDescription>
            </div>
            <Link href="/admin/bots/new">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Bot
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Loading bots...</p>
            </div>
          ) : (
            <BotsDataTable columns={columns} data={bots} />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
