{"devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "concurrently": "^9.1.2", "eslint": "^9.31.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "tsx": "^4.0.0", "typescript": "~5.5.0", "wrangler": "^4.20.3"}, "scripts": {"dev": "concurrently \"cd server && bun dev\" \"cd front && bun dev\" \"cd admin && bun dev\"", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "lint:front": "cd front && bun run lint", "lint:admin": "cd admin && bun run lint", "lint:selangor": "cd selangor && bun run lint", "lint:server": "cd server && bun run lint", "format:front": "cd front && bun run format", "format:admin": "cd admin && bun run format", "format:selangor": "cd selangor && bun run format", "format:server": "cd server && bun run format", "db:seed": "cd server && pnpm db:seed", "db:push": "cd server && pnpm db:push", "db:migrate": "cd server && pnpm db:migrate && pnpm db:seed", "db:studio": "cd server && pnpm db:studio", "db:gen": "cd server && pnpm drizzle:generate", "deploy:server": "cd server && pnpm db:migrate:prod && wrangler deploy -e production", "deploy:front": "cd front && wrangler pages deploy --branch production", "deploy": "bun deploy:server && bun deploy:front", "server:logs": "cd server && wrangler -e production tail -f", "test": "bun test --bail --serial ."}}