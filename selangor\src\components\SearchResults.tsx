'use client'

import {
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  File,
  FileText,
  Globe,
  Loader2,
  Star,
} from 'lucide-react'
import type { SearchResponse, TextResult } from '@/types/search'

interface SearchResultsProps {
  searchResponse: SearchResponse
  onPageChange?: (page: number) => void
  isLoading?: boolean
}

interface SearchResultItemProps {
  result: TextResult
  query: string
}

function SearchResultItem({ result, query }: SearchResultItemProps) {
  const { text, score, metadata } = result

  // Highlight search terms in the text
  const highlightText = (text: string, query: string) => {
    if (!query) return text

    const words = query.toLowerCase().split(/\s+/)
    let highlightedText = text

    words.forEach(word => {
      if (word.length > 2) {
        // Only highlight words longer than 2 characters
        const regex = new RegExp(`(${word})`, 'gi')
        highlightedText = highlightedText.replace(
          regex,
          '<mark class="bg-yellow-200 px-1 rounded">$1</mark>'
        )
      }
    })

    return highlightedText
  }

  // Format score as percentage
  const scorePercentage = Math.round(score * 100)

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 bg-green-50'
    if (score >= 0.6) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  // Get file icon based on source or document type
  const getFileIcon = (metadata: TextResult['metadata']) => {
    const source = metadata?.source?.toLowerCase() || ''
    const docType = metadata?.document_type?.toLowerCase() || ''

    if (source.includes('.pdf') || docType.includes('pdf')) {
      return <FileText className="w-4 h-4 text-red-500" />
    }
    if (
      source.includes('.doc') ||
      source.includes('.docx') ||
      docType.includes('doc')
    ) {
      return <FileText className="w-4 h-4 text-blue-500" />
    }
    if (source.includes('http') || metadata?.url) {
      return <Globe className="w-4 h-4 text-green-500" />
    }
    return <File className="w-4 h-4 text-gray-500" />
  }

  // Extract filename from source path
  const getFileName = (metadata: TextResult['metadata']) => {
    const source = metadata?.source
    if (!source) return null

    // Extract filename from path (handle both forward and backward slashes)
    const parts = source.split(/[/\\]/)
    const filename = parts[parts.length - 1]

    // If it looks like a URL, return the domain
    if (source.startsWith('http')) {
      try {
        const url = new URL(source)
        return url.hostname
      } catch {
        return filename
      }
    }

    return filename
  }

  const fileName = getFileName(metadata)
  const fileIcon = getFileIcon(metadata)

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      {/* Document Header - Document ID, Title, and Link */}
      <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            {/* Document Title */}
            {metadata?.title ? (
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                📄 {metadata.title}
              </h3>
            ) : (
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                📄 Document
              </h3>
            )}

            {/* Document Summary */}
            {metadata?.summary && (
              <p className="text-sm text-gray-600 mb-2 italic">
                {metadata.summary}
              </p>
            )}

            {/* Document ID */}
            {result.document_id && (
              <div className="flex items-center mb-2 text-xs">
                <span className="font-medium text-gray-600 mr-2">
                  Document ID:
                </span>
                <span className="font-mono bg-gray-100 px-2 py-1 rounded border text-gray-800">
                  {result.document_id}
                </span>
              </div>
            )}
          </div>

          {/* Score badge */}
          <div
            className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}
          >
            <Star className="w-3 h-3 mr-1" />
            {scorePercentage}%
          </div>
        </div>

        {/* Document Link */}
        {metadata?.title?.toLowerCase().endsWith('.pdf') && (
          <div className="flex items-center">
            <a
              href={`/api/documents/${result.document_id}/download`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-green-600 hover:text-green-700 text-sm font-medium bg-white px-3 py-2 rounded border border-green-200 hover:bg-green-50 transition-colors"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Download PDF
            </a>
          </div>
        )}
      </div>

      {/* File source indicator */}
      {fileName && (
        <div className="flex items-center mb-3 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center flex-1">
            {fileIcon}
            <div className="ml-2">
              <div className="text-sm font-medium text-gray-900">
                Source File: {fileName}
              </div>
              {metadata?.document_type && (
                <div className="text-xs text-gray-500">
                  Type: {metadata.document_type}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Content preview */}
      {false && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Content Preview:
          </h4>
          <div
            className="text-gray-700 leading-relaxed bg-gray-50 p-3 rounded border"
            dangerouslySetInnerHTML={{
              __html: highlightText(text, query),
            }}
          />
        </div>
      )}

      {/* Additional metadata */}
      <div className="flex items-center text-xs text-gray-500 space-x-4 pt-2 border-t border-gray-200">
        {false && result.wordCount && <span>📊 {result.wordCount} words</span>}
        {metadata?.chunk_index !== undefined && (
          <span>🧩 Chunk {metadata.chunk_index + 1}</span>
        )}
        {result.type && <span>🔍 {result.type} search</span>}
      </div>
    </div>
  )
}

function Pagination({
  searchResponse,
  onPageChange,
}: {
  searchResponse: SearchResponse
  onPageChange?: (page: number) => void
}) {
  const { pagination } = searchResponse

  if (!pagination) return null

  const { page, total, hasMore } = pagination
  const totalPages = Math.ceil(total / pagination.limit)

  if (totalPages <= 1) return null

  return (
    <div className="flex items-center justify-between mt-8">
      <div className="text-sm text-gray-700">
        Showing page {page} of {totalPages} ({total} total results)
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={() => onPageChange?.(page - 1)}
          disabled={page <= 1}
          className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>

        <span className="px-3 py-2 text-sm font-medium text-gray-700">
          {page}
        </span>

        <button
          onClick={() => onPageChange?.(page + 1)}
          disabled={!hasMore}
          className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  )
}

export function SearchResults({
  searchResponse,
  onPageChange,
  isLoading,
}: SearchResultsProps) {
  const { query, results, totalChunks } = searchResponse

  // Filter results to only show the first document from each unique document_id
  const uniqueResults = results.reduce((acc: TextResult[], current) => {
    // If document_id is null or undefined, include the result
    if (!current.document_id) {
      acc.push(current)
      return acc
    }

    // Check if we already have a result with this document_id
    const existingIndex = acc.findIndex(
      result => result.document_id === current.document_id
    )

    // If no existing result with this document_id, add it
    if (existingIndex === -1) {
      acc.push(current)
      return acc
    }

    // If existing result has lower score, replace it with current (higher score)
    if (acc[existingIndex].score < current.score) {
      acc[existingIndex] = current
    }

    // Otherwise, keep the existing result (omit current duplicate)
    return acc
  }, [])

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <Loader2 className="w-12 h-12 text-green-600 mx-auto mb-4 animate-spin" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Searching...</h3>
        <p className="text-gray-600">
          Please wait while we search for the most relevant results.
        </p>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No results found
        </h3>
        <p className="text-gray-600">
          {query ? (
            <>
              No documents found for "
              <span className="font-medium text-gray-800">{query}</span>".
              <br />
              Try adjusting your search terms or check the spelling.
            </>
          ) : (
            'Try adjusting your search terms or check the spelling.'
          )}
        </p>
      </div>
    )
  }

  return (
    <div>
      {/* Results summary */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Search Results for "{query}"
        </h2>
        <p className="text-gray-600">
          Found {uniqueResults.length} unique documents from {results.length}{' '}
          total chunks ({totalChunks} total chunks)
        </p>
      </div>

      {/* Results list */}
      <div className="space-y-6">
        {uniqueResults.map((result, index) => (
          <SearchResultItem
            key={`${result.document_id}-${index}`}
            result={result}
            query={query}
          />
        ))}
      </div>

      {/* Pagination */}
      <Pagination searchResponse={searchResponse} onPageChange={onPageChange} />
    </div>
  )
}
