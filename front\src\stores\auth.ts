import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import type { UserRole } from '@/types/roles'

// Types for authentication state
export interface AdminUser {
  id: number
  username: string
  role: UserRole
}

export interface AgentUser {
  id: number
  username: string
  email: string
  firstName: string
  lastName: string
  role: string
  isActive: boolean
  isOnline?: boolean
  lastSeenAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface AuthState {
  // Admin authentication
  adminToken: string | null
  adminUser: AdminUser | null
  isAdminAuthenticated: boolean

  // Agent authentication
  agentToken: string | null
  agentUser: AgentUser | null
  isAgentAuthenticated: boolean

  // Loading states
  isLoading: boolean

  // Actions
  setAdminAuth: (token: string, user: AdminUser) => void
  setAgentAuth: (token: string, user: AgentUser) => void
  clearAdminAuth: () => void
  clearAgentAuth: () => void
  clearAllAuth: () => void
  setLoading: (loading: boolean) => void

  // Verification methods
  verifyAdminToken: () => Promise<boolean>
  verifyAgentToken: () => Promise<boolean>

  // Login methods
  loginAdmin: (
    username: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>
  loginAgent: (
    username: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>

  // Logout methods
  logoutAdmin: () => Promise<void>
  logoutAgent: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      adminToken: null,
      adminUser: null,
      isAdminAuthenticated: false,
      agentToken: null,
      agentUser: null,
      isAgentAuthenticated: false,
      isLoading: false,

      // Actions
      setAdminAuth: (token: string, user: AdminUser) => {
        set({
          adminToken: token,
          adminUser: user,
          isAdminAuthenticated: true,
        })
      },

      setAgentAuth: (token: string, user: AgentUser) => {
        set({
          agentToken: token,
          agentUser: user,
          isAgentAuthenticated: true,
        })
      },

      clearAdminAuth: () => {
        set({
          adminToken: null,
          adminUser: null,
          isAdminAuthenticated: false,
        })
      },

      clearAgentAuth: () => {
        set({
          agentToken: null,
          agentUser: null,
          isAgentAuthenticated: false,
        })
      },

      clearAllAuth: () => {
        set({
          adminToken: null,
          adminUser: null,
          isAdminAuthenticated: false,
          agentToken: null,
          agentUser: null,
          isAgentAuthenticated: false,
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Verification methods
      verifyAdminToken: async (): Promise<boolean> => {
        const { adminToken } = get()
        if (!adminToken) {
          return false
        }

        try {
          const API_BASE_URL =
            process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001'

          const response = await fetch(`${API_BASE_URL}/api/admin/verify`, {
            headers: {
              Authorization: `Bearer ${adminToken}`,
            },
          })
          console.log('verifyAdminToken response', response)
          if (response.ok) {
            const data = await response.json()
            console.log('verifyAdminToken data', data)
            if (data.success && data.user) {
              set({
                adminUser: data.user,
                isAdminAuthenticated: true,
              })
              return true
            }
          }

          // Token is invalid, clear auth
          get().clearAdminAuth()
          return false
        } catch (error) {
          console.error('Admin token verification failed:', error)
          get().clearAdminAuth()
          return false
        }
      },

      verifyAgentToken: async (): Promise<boolean> => {
        const { agentToken } = get()
        if (!agentToken) return false

        try {
          const response = await fetch('/api/agent/verify', {
            headers: {
              Authorization: `Bearer ${agentToken}`,
            },
          })

          if (response.ok) {
            const data = await response.json()
            if (data.success && data.agent) {
              set({
                agentUser: data.agent,
                isAgentAuthenticated: true,
              })
              return true
            }
          }

          // Token is invalid, clear auth
          get().clearAgentAuth()
          return false
        } catch (error) {
          console.error('Agent token verification failed:', error)
          get().clearAgentAuth()
          return false
        }
      },

      // Login methods
      loginAdmin: async (username: string, password: string) => {
        set({ isLoading: true })

        try {
          const API_BASE_URL =
            process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001'

          const response = await fetch(`${API_BASE_URL}/api/admin/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          })

          const data = await response.json()

          if (data.success && data.token) {
            // Verify the token to get user info
            const verifyResponse = await fetch(
              `${API_BASE_URL}/api/admin/verify`,
              {
                headers: {
                  Authorization: `Bearer ${data.token}`,
                },
              }
            )

            if (verifyResponse.ok) {
              const verifyData = await verifyResponse.json()
              if (verifyData.success && verifyData.user) {
                get().setAdminAuth(data.token, verifyData.user)
                return { success: true }
              }
            }
          }

          return { success: false, error: data.error || 'Login failed' }
        } catch (error) {
          console.error('Admin login error:', error)
          return { success: false, error: 'Network error. Please try again.' }
        } finally {
          set({ isLoading: false })
        }
      },

      loginAgent: async (username: string, password: string) => {
        set({ isLoading: true })

        try {
          const response = await fetch('/api/agent/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          })

          const data = await response.json()

          if (data.success && data.token && data.agent) {
            get().setAgentAuth(data.token, data.agent)
            return { success: true }
          }

          return { success: false, error: data.error || 'Login failed' }
        } catch (error) {
          console.error('Agent login error:', error)
          return { success: false, error: 'Network error. Please try again.' }
        } finally {
          set({ isLoading: false })
        }
      },

      // Logout methods
      logoutAdmin: async () => {
        get().clearAdminAuth()
      },

      logoutAgent: async () => {
        const { agentToken } = get()

        if (agentToken) {
          try {
            await fetch(
              `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/agent/logout`,
              {
                method: 'POST',
                headers: {
                  Authorization: `Bearer ${agentToken}`,
                },
              }
            )
          } catch (error) {
            console.error('Agent logout error:', error)
          }
        }

        get().clearAgentAuth()
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        adminToken: state.adminToken,
        adminUser: state.adminUser,
        isAdminAuthenticated: state.isAdminAuthenticated,
        agentToken: state.agentToken,
        agentUser: state.agentUser,
        isAgentAuthenticated: state.isAgentAuthenticated,
      }),
    }
  )
)

// Selectors for easier access (similar to Pinia getters)
export const useAdminAuth = () => {
  const store = useAuthStore()
  return {
    token: store.adminToken,
    user: store.adminUser,
    isAuthenticated: store.isAdminAuthenticated,
    isLoading: store.isLoading,
    login: store.loginAdmin,
    logout: store.logoutAdmin,
    verify: store.verifyAdminToken,
    setAuth: store.setAdminAuth,
    clearAuth: store.clearAdminAuth,
  }
}

export const useAgentAuth = () => {
  const store = useAuthStore()
  return {
    token: store.agentToken,
    user: store.agentUser,
    isAuthenticated: store.isAgentAuthenticated,
    isLoading: store.isLoading,
    login: store.loginAgent,
    logout: store.logoutAgent,
    verify: store.verifyAgentToken,
    setAuth: store.setAgentAuth,
    clearAuth: store.clearAgentAuth,
  }
}
