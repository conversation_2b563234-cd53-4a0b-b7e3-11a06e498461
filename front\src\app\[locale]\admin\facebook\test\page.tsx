'use client'

export const runtime = 'edge'

import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Image,
  MessageSquare,
  Send,
  User,
} from 'lucide-react'
import type React from 'react'
import { useCallback, useEffect, useState } from 'react'
import { useRouter } from '@/i18n/navigation'
import { useAuthStore } from '@/stores/auth'

interface TestMessage {
  to: string
  message: string
  type: 'text' | 'image'
  imageUrl?: string
}

interface TestResult {
  success: boolean
  messageId?: string
  error?: string
  timestamp: Date
}

interface Conversation {
  psid: string
  name: string
  updatedTime: string
  messageCount: number
}

export default function FacebookTestPage() {
  const [form, setForm] = useState<TestMessage>({
    to: '',
    message: '',
    type: 'text',
    imageUrl: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [loadingConversations, setLoadingConversations] = useState(false)
  const [message, setMessage] = useState<{
    type: 'success' | 'error'
    text: string
  } | null>(null)
  const router = useRouter()
  const { adminToken, isAdminAuthenticated } = useAuthStore()

  const checkAuth = useCallback(() => {
    if (!isAdminAuthenticated || !adminToken) {
      router.push('/admin')
    }
  }, [isAdminAuthenticated, adminToken, router])

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  const loadConversations = async () => {
    if (!adminToken) return

    setLoadingConversations(true)
    try {
      const response = await fetch('/api/admin/facebook/conversations', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      if (response.ok && data.success) {
        setConversations(data.conversations || [])
      } else {
        console.error('Failed to load conversations:', data.error)
      }
    } catch (error) {
      console.error('Error loading conversations:', error)
    } finally {
      setLoadingConversations(false)
    }
  }

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target
    setForm(prev => ({ ...prev, [name]: value }))
    if (message) {
      setMessage(null)
    }
  }

  const handleSendTest = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    if (!adminToken) {
      setMessage({
        type: 'error',
        text: 'Authentication required. Please log in again.',
      })
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/admin/facebook/test-message', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      })

      const data = await response.json()
      const result: TestResult = {
        success: response.ok,
        messageId: data.messageId,
        error: data.error,
        timestamp: new Date(),
      }

      setTestResults(prev => [result, ...prev])

      if (response.ok) {
        setMessage({
          type: 'success',
          text: `Test message sent successfully! Message ID: ${data.messageId}`,
        })
        // Reset form
        setForm(prev => ({ ...prev, message: '', imageUrl: '' }))
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'Failed to send test message',
        })
      }
    } catch (_error) {
      const result: TestResult = {
        success: false,
        error: 'Network error',
        timestamp: new Date(),
      }
      setTestResults(prev => [result, ...prev])
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Test Facebook Messenger Integration
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Status Message */}
        {message && (
          <div
            className={`mb-6 p-4 rounded-md flex items-center space-x-2 ${
              message.type === 'success'
                ? 'bg-green-50 border border-green-200'
                : 'bg-red-50 border border-red-200'
            }`}
          >
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
            <span
              className={`text-sm ${
                message.type === 'success' ? 'text-green-700' : 'text-red-700'
              }`}
            >
              {message.text}
            </span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Test Form */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Send Test Message
              </h2>
              <p className="text-sm text-gray-600">
                Test your Facebook Messenger integration
              </p>
            </div>

            <form onSubmit={handleSendTest} className="p-6 space-y-6">
              {/* User ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <User className="inline h-4 w-4 mr-1" />
                  Facebook User ID *
                </label>
                <input
                  type="text"
                  name="to"
                  value={form.to}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                  placeholder="Enter Facebook User ID"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter the Facebook User ID (PSID) of the recipient. The user
                  must have messaged your page first.
                </p>
              </div>

              {/* Message Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message Type
                </label>
                <select
                  name="type"
                  value={form.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                >
                  <option value="text">Text Message</option>
                  <option value="image">Image Message</option>
                </select>
              </div>

              {/* Message Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MessageSquare className="inline h-4 w-4 mr-1" />
                  Message {form.type === 'image' ? '(Caption)' : '*'}
                </label>
                <textarea
                  name="message"
                  value={form.message}
                  onChange={handleInputChange}
                  required={form.type === 'text'}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                  placeholder={
                    form.type === 'text'
                      ? 'Enter your test message...'
                      : 'Enter image caption (optional)...'
                  }
                />
              </div>

              {/* Image URL (if image type) */}
              {form.type === 'image' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Image className="inline h-4 w-4 mr-1" />
                    Image URL *
                  </label>
                  <input
                    type="url"
                    name="imageUrl"
                    value={form.imageUrl}
                    onChange={handleInputChange}
                    required={form.type === 'image'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                    placeholder="https://example.com/image.jpg"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Image must be publicly accessible via HTTPS
                  </p>
                </div>
              )}

              {/* Send Button */}
              <button
                type="submit"
                disabled={
                  isLoading ||
                  !form.to ||
                  !form.to.trim() ||
                  (form.type === 'text' &&
                    (!form.message || !form.message.trim())) ||
                  (form.type === 'image' &&
                    (!form.imageUrl || !form.imageUrl.trim()))
                }
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send Test Message
              </button>
            </form>
          </div>

          {/* Recent Conversations */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-medium text-gray-900">
                    Recent Conversations
                  </h2>
                  <p className="text-sm text-gray-600">
                    Find valid PSIDs from recent messages
                  </p>
                </div>
                <button
                  onClick={loadConversations}
                  disabled={loadingConversations}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  {loadingConversations ? 'Loading...' : 'Refresh'}
                </button>
              </div>
            </div>

            <div className="p-6">
              {conversations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No recent conversations</p>
                  <p className="text-sm">
                    Click Refresh to load recent conversations
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {conversations.map((conv, index) => (
                    <div
                      key={index}
                      className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                      onClick={() =>
                        setForm(prev => ({ ...prev, to: conv.psid }))
                      }
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">
                            {conv.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            PSID: {conv.psid}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-gray-500">
                            {conv.messageCount} messages
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(conv.updatedTime).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Test Results
              </h2>
              <p className="text-sm text-gray-600">
                History of test messages sent
              </p>
            </div>

            <div className="p-6">
              {testResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No test messages sent yet</p>
                  <p className="text-sm">
                    Send a test message to see results here
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        result.success
                          ? 'bg-green-50 border-green-200'
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          {result.success ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          )}
                          <span
                            className={`font-medium ${
                              result.success ? 'text-green-800' : 'text-red-800'
                            }`}
                          >
                            {result.success ? 'Success' : 'Failed'}
                          </span>
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {result.timestamp.toLocaleTimeString()}
                        </div>
                      </div>

                      {result.success && result.messageId && (
                        <p className="text-sm text-green-700 mt-2">
                          Message ID: {result.messageId}
                        </p>
                      )}

                      {!result.success && result.error && (
                        <p className="text-sm text-red-700 mt-2">
                          Error: {result.error}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">
            Testing Instructions
          </h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>
              • Make sure your Facebook Messenger integration is properly
              configured
            </li>
            <li>
              • <strong>Important:</strong> You can only send messages to users
              who have messaged your page first (Facebook policy)
            </li>
            <li>
              • Use a valid Facebook User ID (PSID) - you can find this in your
              page's message logs
            </li>
            <li>
              • Messages can only be sent within 24 hours of the user's last
              message (standard messaging)
            </li>
            <li>
              • For image messages, ensure the image URL is publicly accessible
              via HTTPS
            </li>
            <li>
              • Check the recipient's Facebook Messenger to verify message
              delivery
            </li>
            <li>
              • If you get "Parameter error: You cannot send messages to this
              id", the user hasn't messaged your page or the PSID is invalid
            </li>
          </ul>
        </div>
      </main>
    </div>
  )
}
