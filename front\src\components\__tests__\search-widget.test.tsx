import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SearchWidget } from '../search-widget'

// Create a realistic language context provider for testing
const TestLanguageProvider = ({ children }: { children: React.ReactNode }) => {
  const mockLanguageContext = {
    language: 'en' as const,
    t: (key: string, en: string, bm: string) => en,
    setLanguage: jest.fn(),
  }

  // Use React.createContext if the actual context is available
  return <div data-testid="language-provider">{children}</div>
}

// Mock search results for testing
const mockSearchResults = [
  {
    id: '1',
    companyName: 'Test Company',
    productName: 'Test Product',
    certificateNumber: 'CERT-001',
    status: 'valid',
    expiryDate: '2024-12-31',
  },
  {
    id: '2',
    companyName: 'Another Company',
    productName: 'Another Product',
    certificateNumber: 'CERT-002',
    status: 'valid',
    expiryDate: '2024-11-30',
  },
]

// Helper to render component with providers
const renderSearchWidget = (props = {}) => {
  const defaultProps = {
    onSearch: jest.fn(),
    onResultSelect: jest.fn(),
    ...props,
  }

  return {
    ...render(
      <TestLanguageProvider>
        <SearchWidget {...defaultProps} />
      </TestLanguageProvider>
    ),
    props: defaultProps,
  }
}

// Mock fetch for API calls
const mockFetch = (response: any, ok = true) => {
  global.fetch = jest.fn().mockResolvedValue({
    ok,
    json: jest.fn().mockResolvedValue(response),
  })
}

describe('SearchWidget', () => {
  beforeEach(() => {
    // Reset fetch mock
    global.fetch = jest.fn()
    jest.clearAllMocks()
  })

  it('should render search input with placeholder', () => {
    renderSearchWidget()

    const searchInput = screen.getByPlaceholderText(
      /enter company name or certificate number/i
    )
    expect(searchInput).toBeInTheDocument()
  })

  it('should render custom placeholder when provided', () => {
    const customPlaceholder = 'Custom search placeholder'
    render(<SearchWidget {...defaultProps} placeholder={customPlaceholder} />)

    const searchInput = screen.getByPlaceholderText(customPlaceholder)
    expect(searchInput).toBeInTheDocument()
  })

  it('should render search button', () => {
    render(<SearchWidget {...defaultProps} />)

    const searchButton = screen.getByRole('button', { name: /search/i })
    expect(searchButton).toBeInTheDocument()
  })

  it('should call onSearch when search button is clicked', async () => {
    const user = userEvent.setup()
    const { props } = renderSearchWidget()

    const searchInput = screen.getByRole('textbox')
    const searchButton = screen.getByRole('button', { name: /search/i })

    await user.type(searchInput, 'test query')
    await user.click(searchButton)

    expect(props.onSearch).toHaveBeenCalledWith('test query')
  })

  it('should call onSearch when Enter key is pressed', async () => {
    const user = userEvent.setup()
    const { props } = renderSearchWidget()

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test query')
    await user.keyboard('{Enter}')

    expect(props.onSearch).toHaveBeenCalledWith('test query')
  })

  it('should show loading state during search', async () => {
    mockFetchSuccess(mockSearchResults)
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')
    const searchButton = screen.getByRole('button', { name: /search/i })

    await user.type(searchInput, 'test')

    // Mock a delayed response
    global.fetch = jest.fn(
      () =>
        new Promise(resolve =>
          setTimeout(
            () =>
              resolve({
                ok: true,
                json: () =>
                  Promise.resolve({ success: true, data: mockSearchResults }),
              }),
            100
          )
        )
    )

    await user.click(searchButton)

    expect(screen.getByText(/searching/i)).toBeInTheDocument()
  })

  it('should display search results when API returns data', async () => {
    mockFetch({ success: true, data: mockSearchResults })
    const user = userEvent.setup()
    renderSearchWidget()

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    await waitFor(() => {
      expect(screen.getByText('Test Company')).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    mockFetch({ success: false, error: 'API Error' }, false)
    const user = userEvent.setup()
    renderSearchWidget()

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    await waitFor(() => {
      // Should show error message or handle gracefully
      expect(screen.queryByText('Test Company')).not.toBeInTheDocument()
    })
  })

  it('should show loading state during search', async () => {
    // Mock a delayed response
    global.fetch = jest.fn().mockImplementation(
      () =>
        new Promise(resolve =>
          setTimeout(
            () =>
              resolve({
                ok: true,
                json: () =>
                  Promise.resolve({ success: true, data: mockSearchResults }),
              }),
            100
          )
        )
    )

    const user = userEvent.setup()
    renderSearchWidget()

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    // Should show loading indicator
    expect(
      screen.getByText(/searching/i) || screen.getByRole('progressbar')
    ).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('Test Company')).toBeInTheDocument()
    })
  })

  it('should clear results when search input is cleared', async () => {
    mockFetch({ success: true, data: mockSearchResults })
    const user = userEvent.setup()
    renderSearchWidget()

    const searchInput = screen.getByRole('textbox')

    // Type and get results
    await user.type(searchInput, 'test')
    await waitFor(() => {
      expect(screen.getByText('Test Company')).toBeInTheDocument()
    })

    // Clear input
    await user.clear(searchInput)

    await waitFor(() => {
      expect(screen.queryByText('Test Company')).not.toBeInTheDocument()
    })
  })

  it('should call onResultSelect when a result is clicked', async () => {
    mockFetchSuccess(mockSearchResults)
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    await waitFor(() => {
      expect(screen.getByText('Test Company')).toBeInTheDocument()
    })

    await user.click(screen.getByText('Test Company'))

    expect(mockOnResultSelect).toHaveBeenCalledWith(mockSearchResults[0])
  })

  it('should show no results message when no data is returned', async () => {
    mockFetchSuccess([])
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'nonexistent')

    await waitFor(() => {
      expect(screen.getByText(/no results found/i)).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    mockFetchError('Network error')
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    await waitFor(() => {
      expect(screen.queryByText('Test Company')).not.toBeInTheDocument()
    })
  })

  it('should clear search input when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test query')

    const clearButton = screen.getByRole('button', { name: '' }) // X button
    await user.click(clearButton)

    expect(searchInput).toHaveValue('')
  })

  it('should close dropdown when Escape key is pressed', async () => {
    mockFetchSuccess(mockSearchResults)
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')

    await user.type(searchInput, 'test')

    await waitFor(() => {
      expect(screen.getByText('Test Company')).toBeInTheDocument()
    })

    await user.keyboard('{Escape}')

    expect(screen.queryByText('Test Company')).not.toBeInTheDocument()
  })

  it('should disable search button when input is empty', () => {
    render(<SearchWidget {...defaultProps} />)

    const searchButton = screen.getByRole('button', { name: /search/i })

    expect(searchButton).toBeDisabled()
  })

  it('should enable search button when input has value', async () => {
    const user = userEvent.setup()
    render(<SearchWidget {...defaultProps} />)

    const searchInput = screen.getByRole('textbox')
    const searchButton = screen.getByRole('button', { name: /search/i })

    await user.type(searchInput, 'test')

    expect(searchButton).not.toBeDisabled()
  })

  it('should show recent searches when enabled', () => {
    // Mock localStorage with recent searches
    const mockRecentSearches = ['previous search', 'another search']
    localStorage.setItem(
      'halal-recent-searches',
      JSON.stringify(mockRecentSearches)
    )

    render(<SearchWidget {...defaultProps} showRecentSearches={true} />)

    const searchInput = screen.getByRole('textbox')
    fireEvent.focus(searchInput)

    expect(screen.getByText(/recent searches/i)).toBeInTheDocument()
    expect(screen.getByText('previous search')).toBeInTheDocument()
  })
})
