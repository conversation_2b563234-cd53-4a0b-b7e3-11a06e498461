import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { cleanupMocks, mockLanguageContext } from '@/test-utils'
import SearchPage from '../page'

// Mock the language context
jest.mock('@/lib/language-context', () => ({
  useLanguage: () => mockLanguageContext,
}))

// Mock the search hook
const mockSearch = jest.fn()
const mockSearchResults = {
  search: mockSearch,
  results: [],
  loading: false,
  error: null,
}

jest.mock('@/hooks/use-api', () => ({
  useSearch: () => mockSearchResults,
}))

// Mock next/navigation
const mockSearchParams = new URLSearchParams()
jest.mock('next/navigation', () => ({
  useSearchParams: () => mockSearchParams,
}))

// Mock the PageWrapper component
jest.mock('@/components/page-wrapper', () => ({
  PageWrapper: ({
    children,
    title,
    titleBM,
    description,
    descriptionBM,
    breadcrumbs,
  }: any) => (
    <div data-testid="page-wrapper">
      <h1>{mockLanguageContext.language === 'en' ? title : titleBM}</h1>
      <p>
        {mockLanguageContext.language === 'en' ? description : descriptionBM}
      </p>
      <div data-testid="breadcrumbs">{JSON.stringify(breadcrumbs)}</div>
      {children}
    </div>
  ),
}))

// Mock the SearchWidget component
jest.mock('@/components/search-widget', () => ({
  SearchWidget: ({ onSearch, placeholder, showRecentSearches }: any) => (
    <div data-testid="search-widget">
      <input
        data-testid="search-input"
        placeholder={placeholder}
        onChange={e => onSearch(e.target.value)}
      />
      {showRecentSearches && (
        <div data-testid="recent-searches">Recent searches enabled</div>
      )}
    </div>
  ),
}))

describe('Search Page', () => {
  beforeEach(() => {
    cleanupMocks()
    mockSearch.mockClear()
    mockSearchParams.delete('q')
    mockSearchResults.results = []
    mockSearchResults.loading = false
    mockSearchResults.error = null
  })

  it('should render page title and description in English', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    expect(screen.getByText('Search Results')).toBeInTheDocument()
    expect(
      screen.getByText(/Search for Halal certified companies/)
    ).toBeInTheDocument()
  })

  it('should render page title and description in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<SearchPage />)

    expect(screen.getByText('Hasil Carian')).toBeInTheDocument()
    expect(
      screen.getByText(/Cari syarikat dan produk yang disijilkan Halal/)
    ).toBeInTheDocument()
  })

  it('should render correct breadcrumbs', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    const breadcrumbsElement = screen.getByTestId('breadcrumbs')
    const breadcrumbs = JSON.parse(breadcrumbsElement.textContent || '[]')

    expect(breadcrumbs).toHaveLength(2)
    expect(breadcrumbs[0]).toEqual({ label: 'Home', href: '/' })
    expect(breadcrumbs[1]).toEqual({
      label: 'Search Results',
      href: '/search',
    })
  })

  it('should render search widget with correct props', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    const searchWidget = screen.getByTestId('search-widget')
    expect(searchWidget).toBeInTheDocument()

    const searchInput = screen.getByTestId('search-input')
    expect(searchInput).toHaveAttribute(
      'placeholder',
      expect.stringContaining('Search by company name')
    )

    expect(screen.getByTestId('recent-searches')).toBeInTheDocument()
  })

  it('should render filter section', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    expect(screen.getByText('Filters')).toBeInTheDocument()
    expect(screen.getByLabelText('Status')).toBeInTheDocument()
    expect(screen.getByLabelText('Category')).toBeInTheDocument()
    expect(screen.getByLabelText('Country')).toBeInTheDocument()
  })

  it('should render filter options correctly', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    // Status filter options
    const statusSelect = screen.getByLabelText('Status')
    expect(statusSelect).toBeInTheDocument()

    // Category filter options
    const categorySelect = screen.getByLabelText('Category')
    expect(categorySelect).toBeInTheDocument()

    // Country filter options
    const countrySelect = screen.getByLabelText('Country')
    expect(countrySelect).toBeInTheDocument()
  })

  it('should perform search when query parameter is present', () => {
    mockSearchParams.set('q', 'test query')
    render(<SearchPage />)

    expect(mockSearch).toHaveBeenCalledWith({
      query: 'test query',
      status: '',
      category: '',
      country: '',
      sortBy: 'companyName',
      sortOrder: 'asc',
      page: 1,
      limit: 20,
    })
  })

  it('should handle filter changes', async () => {
    const user = userEvent.setup()
    render(<SearchPage />)

    const statusSelect = screen.getByLabelText('Status')
    await user.selectOptions(statusSelect, 'valid')

    // Should trigger search if query exists
    // Note: This would need the component to have a query set first
  })

  it('should display loading state', () => {
    mockSearchResults.loading = true
    render(<SearchPage />)

    expect(screen.getByText(/Searching/)).toBeInTheDocument()
  })

  it('should display error state', () => {
    mockSearchResults.error = 'Search failed'
    render(<SearchPage />)

    expect(screen.getByText('Search failed')).toBeInTheDocument()
  })

  it('should display no results message when no results found', () => {
    mockSearchResults.results = []
    mockLanguageContext.language = 'en'

    // Mock that a search was performed (query exists)
    const { rerender } = render(<SearchPage />)

    // Simulate having performed a search
    mockSearchParams.set('q', 'test')
    rerender(<SearchPage />)

    expect(screen.getByText('No results found')).toBeInTheDocument()
    expect(
      screen.getByText('Try adjusting your search terms or filters')
    ).toBeInTheDocument()
  })

  it('should display search results when available', () => {
    const mockResults = [
      {
        certificateNumber: 'CERT-001',
        companyName: 'Test Company',
        productName: 'Test Product',
        status: 'valid',
        issueDate: '2024-01-01',
        expiryDate: '2026-01-01',
        country: 'Malaysia',
      },
      {
        certificateNumber: 'CERT-002',
        companyName: 'Another Company',
        productName: 'Another Product',
        status: 'expired',
        issueDate: '2022-01-01',
        expiryDate: '2024-01-01',
        country: 'Singapore',
      },
    ]

    mockSearchResults.results = mockResults
    render(<SearchPage />)

    expect(screen.getByText('Test Company')).toBeInTheDocument()
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('CERT-001')).toBeInTheDocument()
    expect(screen.getByText('Another Company')).toBeInTheDocument()
    expect(screen.getByText('Another Product')).toBeInTheDocument()
    expect(screen.getByText('CERT-002')).toBeInTheDocument()
  })

  it('should show results count when results are available', () => {
    const mockResults = [
      {
        certificateNumber: 'CERT-001',
        companyName: 'Test Company',
        productName: 'Test Product',
        status: 'valid',
        issueDate: '2024-01-01',
        expiryDate: '2026-01-01',
        country: 'Malaysia',
      },
    ]

    mockSearchResults.results = mockResults
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    expect(screen.getByText('(1 results)')).toBeInTheDocument()
  })

  it('should render sort options', () => {
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    expect(screen.getByText('Sort by:')).toBeInTheDocument()

    const sortSelect = screen.getByDisplayValue('Company A-Z')
    expect(sortSelect).toBeInTheDocument()
  })

  it('should handle sort changes', async () => {
    const user = userEvent.setup()
    render(<SearchPage />)

    const sortSelect = screen.getByDisplayValue('Company A-Z')
    await user.selectOptions(sortSelect, 'companyName-desc')

    // Should update sort state
    expect(sortSelect).toHaveValue('companyName-desc')
  })

  it('should display status badges with correct colors', () => {
    const mockResults = [
      {
        certificateNumber: 'CERT-001',
        companyName: 'Valid Company',
        productName: 'Valid Product',
        status: 'valid',
        issueDate: '2024-01-01',
        expiryDate: '2026-01-01',
        country: 'Malaysia',
      },
      {
        certificateNumber: 'CERT-002',
        companyName: 'Expired Company',
        productName: 'Expired Product',
        status: 'expired',
        issueDate: '2022-01-01',
        expiryDate: '2024-01-01',
        country: 'Singapore',
      },
    ]

    mockSearchResults.results = mockResults
    render(<SearchPage />)

    // Check for status badges
    expect(screen.getByText('valid')).toBeInTheDocument()
    expect(screen.getByText('expired')).toBeInTheDocument()
  })

  it('should format dates correctly', () => {
    const mockResults = [
      {
        certificateNumber: 'CERT-001',
        companyName: 'Test Company',
        productName: 'Test Product',
        status: 'valid',
        issueDate: '2024-01-15',
        expiryDate: '2026-01-15',
        country: 'Malaysia',
      },
    ]

    mockSearchResults.results = mockResults
    mockLanguageContext.language = 'en'
    render(<SearchPage />)

    // Should format dates according to locale
    expect(screen.getByText(/Jan.*15.*2024.*Jan.*15.*2026/)).toBeInTheDocument()
  })

  it('should handle view details button clicks', async () => {
    const user = userEvent.setup()
    const mockResults = [
      {
        certificateNumber: 'CERT-001',
        companyName: 'Test Company',
        productName: 'Test Product',
        status: 'valid',
        issueDate: '2024-01-01',
        expiryDate: '2026-01-01',
        country: 'Malaysia',
      },
    ]

    mockSearchResults.results = mockResults
    render(<SearchPage />)

    const viewDetailsButton = screen.getByRole('button', {
      name: /view details/i,
    })
    expect(viewDetailsButton).toBeInTheDocument()

    await user.click(viewDetailsButton)
    // In a real implementation, this would navigate to certificate details
  })
})
