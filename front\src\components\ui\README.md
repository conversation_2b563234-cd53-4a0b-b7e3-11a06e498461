# Reusable UI Components

## ModelSelect Component

A reusable component for selecting AI models with provider filtering.

### Usage

```tsx
import { ModelSelect } from '@/components/ui/model-select';

// Basic usage
<ModelSelect
  value={selectedModel}
  onValueChange={setSelectedModel}
  placeholder="Select model"
/>

// Filter by provider
<ModelSelect
  value={selectedModel}
  onValueChange={setSelectedModel}
  provider="openai" // Only show OpenAI models
  disabled={isLoading}
/>
```

### Props

- `value?: string` - Currently selected model value
- `onValueChange?: (value: string) => void` - Callback when selection changes
- `placeholder?: string` - Placeholder text (default: "Select model")
- `disabled?: boolean` - Whether the select is disabled
- `provider?: string` - Filter models by provider ("openai", "anthropic", "google")
- `className?: string` - Additional CSS classes

### Available Models

- **OpenAI**: gpt-4.1, gpt-4.1 Turbo, GPT-3.5 Turbo
- **Anthropic**: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- **Google**: Gemini Pro

## ProviderSelect Component

A reusable component for selecting AI providers.

### Usage

```tsx
import { ProviderSelect } from '@/components/ui/provider-select';

// Basic usage
<ProviderSelect
  value={selectedProvider}
  onValueChange={setSelectedProvider}
  placeholder="Select provider"
/>

// Exclude Azure option
<ProviderSelect
  value={selectedProvider}
  onValueChange={setSelectedProvider}
  includeAzure={false}
/>
```

### Props

- `value?: string` - Currently selected provider value
- `onValueChange?: (value: string) => void` - Callback when selection changes
- `placeholder?: string` - Placeholder text (default: "Select provider")
- `disabled?: boolean` - Whether the select is disabled
- `className?: string` - Additional CSS classes
- `includeAzure?: boolean` - Whether to include Azure OpenAI option (default: true)

### Available Providers

- **OpenAI**: openai
- **Anthropic**: anthropic
- **Google**: google
- **Azure OpenAI**: azure (optional)

## Example: Bot Configuration Form

```tsx
import { useState } from 'react'
import { ModelSelect } from '@/components/ui/model-select'
import { ProviderSelect } from '@/components/ui/provider-select'

function BotConfigForm() {
  const [provider, setProvider] = useState('openai')
  const [model, setModel] = useState('gpt-4.1')

  return (
    <div className="space-y-4">
      <div>
        <label>Provider</label>
        <ProviderSelect value={provider} onValueChange={setProvider} />
      </div>

      <div>
        <label>Model</label>
        <ModelSelect
          value={model}
          onValueChange={setModel}
          provider={provider} // Filter models by selected provider
        />
      </div>
    </div>
  )
}
```
