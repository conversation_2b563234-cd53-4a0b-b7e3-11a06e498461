import { type RenderOptions, render } from '@testing-library/react'
import type React from 'react'
import type { ReactElement } from 'react'
import { LanguageProvider } from '@/lib/language-context'

// Mock data for testing
export const mockAnnouncements = [
  {
    id: '1',
    title: 'Test Announcement',
    titleBM: 'Pengumuman Ujian',
    content: 'This is a test announcement content.',
    contentBM: 'Ini adalah kandungan pengumuman ujian.',
    date: '2024-01-15',
    category: 'announcement' as const,
    featured: true,
  },
  {
    id: '2',
    title: 'Media Statement',
    titleBM: 'Kenyataan Media',
    content: 'This is a media statement content.',
    contentBM: 'Ini adalah kandungan kenyataan media.',
    date: '2024-01-10',
    category: 'media-statement' as const,
    featured: false,
  },
]

export const mockNews = [
  {
    id: '1',
    title: 'Test News',
    titleBM: 'Berita Ujian',
    excerpt: 'This is a test news excerpt.',
    excerptBM: 'Ini adalah petikan berita ujian.',
    content: 'This is a test news content.',
    contentBM: 'Ini adalah kandungan berita ujian.',
    date: '2024-01-15',
    category: 'Event',
    categoryBM: 'Acara',
    image: '/test-image.jpg',
  },
]

export const mockSearchResults = [
  {
    id: '1',
    type: 'company' as const,
    title: 'Test Company',
    subtitle: 'Food Manufacturing',
    status: 'valid' as const,
    certificateNumber: 'JAKIM-TEST-001',
    expiryDate: '2025-12-31',
  },
]

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <LanguageProvider>{children}</LanguageProvider>
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Helper functions for testing
export const createMockApiResponse = <T,>(data: T, success = true) => ({
  success,
  data: success ? data : undefined,
  error: success ? undefined : 'Test error',
  message: success ? 'Success' : 'Test error message',
})

export const createMockPaginatedResponse = <T,>(
  data: T[],
  page = 1,
  limit = 10,
  total?: number
) => ({
  success: true,
  data,
  pagination: {
    page,
    limit,
    total: total || data.length,
    totalPages: Math.ceil((total || data.length) / limit),
  },
})

// Mock fetch responses
export const mockFetchSuccess = <T,>(data: T) => {
  const mockResponse = createMockApiResponse(data)
  ;(global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: true,
    json: async () => mockResponse,
  })
}

export const mockFetchError = (error = 'Network error') => {
  ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error(error))
}

export const mockFetchPaginated = <T,>(data: T[], page = 1, limit = 10) => {
  const mockResponse = createMockPaginatedResponse(data, page, limit)
  ;(global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: true,
    json: async () => mockResponse,
  })
}

// Test helpers for user interactions
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0))
}

// Mock language context values
export const mockLanguageContext = {
  language: 'en' as const,
  setLanguage: jest.fn(),
  t: jest.fn((key: string, en: string, bm: string) => en),
}

// Component test helpers
export const getByTestId = (container: HTMLElement, testId: string) => {
  const element = container.querySelector(`[data-testid="${testId}"]`)
  if (!element) {
    throw new Error(`Unable to find element with data-testid: ${testId}`)
  }
  return element
}

export const queryByTestId = (container: HTMLElement, testId: string) => {
  return container.querySelector(`[data-testid="${testId}"]`)
}

// Assertion helpers
export const expectElementToBeVisible = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument()
  expect(element).toBeVisible()
}

export const expectElementToHaveText = (
  element: HTMLElement | null,
  text: string
) => {
  expect(element).toBeInTheDocument()
  expect(element).toHaveTextContent(text)
}

// Mock window methods
export const mockWindowLocation = (url: string) => {
  delete (window as any).location
  window.location = new URL(url) as any
}

export const mockWindowOpen = () => {
  const mockOpen = jest.fn()
  window.open = mockOpen
  return mockOpen
}

// Cleanup helpers
export const cleanupMocks = () => {
  jest.clearAllMocks()
  ;(global.fetch as jest.Mock).mockClear()
  localStorage.clear()
}
