'use client'

import { useCallback, useEffect, useState } from 'react'
import { api } from '@/lib/api'
import type {
  Announcement,
  NewsItem,
  SearchParams,
  SearchResult,
} from '@/types'

// Generic hook for API data fetching
export function useApiData<T>(
  apiCall: () => Promise<any>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiCall()

      if (api.utils.isSuccess(response)) {
        setData(response.data)
      } else {
        setError(response.error || 'Failed to fetch data')
      }
    } catch (err) {
      setError(api.utils.handleError(err))
    } finally {
      setLoading(false)
    }
  }, dependencies)

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  }
}

// Hook for announcements
export function useAnnouncements(
  params: {
    category?: string
    featured?: boolean
    page?: number
    limit?: number
  } = {}
) {
  return useApiData<Announcement[]>(
    () => api.announcements.getAll(params),
    [params.category, params.featured, params.page, params.limit]
  )
}

// Hook for single announcement
export function useAnnouncement(id: string) {
  return useApiData<Announcement>(() => api.announcements.getById(id), [id])
}

// Hook for news
export function useNews(
  params: {
    category?: string
    search?: string
    page?: number
    limit?: number
  } = {}
) {
  return useApiData<NewsItem[]>(
    () => api.news.getAll(params),
    [params.category, params.search, params.page, params.limit]
  )
}

// Hook for single news item
export function useNewsItem(id: string) {
  return useApiData<NewsItem>(() => api.news.getById(id), [id])
}

// Hook for search functionality
export function useSearch() {
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (params: SearchParams) => {
    try {
      setLoading(true)
      setError(null)

      const response = await api.search.search(params)

      if (api.utils.isSuccess(response)) {
        setResults(response.data || [])
      } else {
        setError(response.error || 'Search failed')
        setResults([])
      }
    } catch (err) {
      setError(api.utils.handleError(err))
      setResults([])
    } finally {
      setLoading(false)
    }
  }, [])

  const advancedSearch = useCallback(
    async (query: string, filters: Record<string, any>) => {
      try {
        setLoading(true)
        setError(null)

        const response = await api.search.advancedSearch(query, filters)

        if (api.utils.isSuccess(response)) {
          setResults(response.data || [])
        } else {
          setError(response.error || 'Advanced search failed')
          setResults([])
        }
      } catch (err) {
        setError(api.utils.handleError(err))
        setResults([])
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const clearResults = useCallback(() => {
    setResults([])
    setError(null)
  }, [])

  return {
    results,
    loading,
    error,
    search,
    advancedSearch,
    clearResults,
  }
}

// Hook for managing pagination
export function usePagination(initialPage = 1, initialLimit = 10) {
  const [page, setPage] = useState(initialPage)
  const [limit, setLimit] = useState(initialLimit)

  const nextPage = useCallback(() => {
    setPage(prev => prev + 1)
  }, [])

  const prevPage = useCallback(() => {
    setPage(prev => Math.max(1, prev - 1))
  }, [])

  const goToPage = useCallback((newPage: number) => {
    setPage(Math.max(1, newPage))
  }, [])

  const changeLimit = useCallback((newLimit: number) => {
    setLimit(newLimit)
    setPage(1) // Reset to first page when changing limit
  }, [])

  const reset = useCallback(() => {
    setPage(initialPage)
    setLimit(initialLimit)
  }, [initialPage, initialLimit])

  return {
    page,
    limit,
    nextPage,
    prevPage,
    goToPage,
    changeLimit,
    reset,
  }
}

// Hook for managing filters
export function useFilters<T extends Record<string, any>>(initialFilters: T) {
  const [filters, setFilters] = useState<T>(initialFilters)

  const updateFilter = useCallback(<K extends keyof T>(key: K, value: T[K]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }))
  }, [])

  const updateFilters = useCallback((newFilters: Partial<T>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
    }))
  }, [])

  const clearFilter = useCallback(<K extends keyof T>(key: K) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }, [])

  const clearAllFilters = useCallback(() => {
    setFilters(initialFilters)
  }, [initialFilters])

  const hasActiveFilters = useCallback(() => {
    return Object.keys(filters).some(
      key =>
        filters[key] !== undefined &&
        filters[key] !== null &&
        filters[key] !== ''
    )
  }, [filters])

  return {
    filters,
    updateFilter,
    updateFilters,
    clearFilter,
    clearAllFilters,
    hasActiveFilters,
  }
}

// Hook for managing loading states
export function useLoadingState() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const withLoading = useCallback(
    async <T>(asyncFunction: () => Promise<T>): Promise<T | null> => {
      try {
        setLoading(true)
        setError(null)
        const result = await asyncFunction()
        return result
      } catch (err) {
        setError(api.utils.handleError(err))
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    loading,
    error,
    withLoading,
    clearError,
  }
}

// Hook for debounced values (useful for search)
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
