// Performance optimization utilities for the Halal Malaysia Portal

import type { NextRequest, NextResponse } from 'next/server'

// Cache configuration
export const CACHE_CONFIG = {
  // Static assets cache duration (1 year)
  STATIC_ASSETS: 31536000,
  // API responses cache duration (5 minutes)
  API_RESPONSES: 300,
  // Page cache duration (1 hour)
  PAGES: 3600,
  // Images cache duration (1 week)
  IMAGES: 604800,
} as const

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // Record performance metric
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    const values = this.metrics.get(name)!
    values.push(value)

    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift()
    }
  }

  // Get average for a metric
  getAverage(name: string): number {
    const values = this.metrics.get(name)
    if (!values || values.length === 0) {
      return 0
    }

    const sum = values.reduce((acc, val) => acc + val, 0)
    return sum / values.length
  }

  // Get all metrics
  getAllMetrics(): Record<string, { average: number; count: number }> {
    const result: Record<string, { average: number; count: number }> = {}

    for (const [name, values] of this.metrics.entries()) {
      result[name] = {
        average: this.getAverage(name),
        count: values.length,
      }
    }

    return result
  }

  // Clear metrics
  clearMetrics(): void {
    this.metrics.clear()
  }
}

// Performance timing decorator
export function measurePerformance(name: string) {
  return (
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor
  ) => {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const startTime = performance.now()

      try {
        const result = await originalMethod.apply(this, args)
        const endTime = performance.now()
        const duration = endTime - startTime

        PerformanceMonitor.getInstance().recordMetric(name, duration)

        return result
      } catch (error) {
        const endTime = performance.now()
        const duration = endTime - startTime

        PerformanceMonitor.getInstance().recordMetric(`${name}_error`, duration)
        throw error
      }
    }

    return descriptor
  }
}

// API response caching
export function withCache(duration: number = CACHE_CONFIG.API_RESPONSES) {
  return (handler: (req: NextRequest) => Promise<NextResponse>) =>
    async (req: NextRequest): Promise<NextResponse> => {
      const response = await handler(req)

      // Add cache headers
      response.headers.set(
        'Cache-Control',
        `public, max-age=${duration}, s-maxage=${duration}`
      )
      response.headers.set('CDN-Cache-Control', `public, max-age=${duration}`)
      response.headers.set(
        'Vercel-CDN-Cache-Control',
        `public, max-age=${duration}`
      )

      return response
    }
}

// Image optimization utilities
export const imageOptimization = {
  // Get optimized image URL
  getOptimizedUrl(src: string, width?: number, quality?: number): string {
    if (!src.startsWith('/')) {
      return src
    }

    const params = new URLSearchParams()
    if (width) {
      params.set('w', width.toString())
    }
    if (quality) {
      params.set('q', quality.toString())
    }

    const queryString = params.toString()
    return queryString ? `${src}?${queryString}` : src
  },

  // Get responsive image sizes
  getResponsiveSizes(breakpoints: number[] = [640, 768, 1024, 1280]): string {
    return breakpoints
      .map((bp, index) => {
        if (index === breakpoints.length - 1) {
          return `${bp}px`
        }
        return `(max-width: ${bp}px) ${bp}px`
      })
      .join(', ')
  },

  // Preload critical images
  preloadImage(src: string, priority: 'high' | 'low' = 'low'): void {
    if (typeof window === 'undefined') {
      return
    }

    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    link.fetchPriority = priority

    document.head.appendChild(link)
  },
}

// Bundle analysis utilities
export const bundleAnalysis = {
  // Analyze component bundle size
  analyzeComponent(componentName: string): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Loading component: ${componentName}`)
    }
  },

  // Track dynamic imports
  trackDynamicImport(moduleName: string): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Dynamic import: ${moduleName}`)
    }
  },
}

// Memory management
export const memoryManagement = {
  // Cleanup function for components
  cleanup(refs: React.RefObject<any>[]): void {
    refs.forEach(ref => {
      if (ref.current) {
        ref.current = null
      }
    })
  },

  // Debounce function for performance
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout

    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },

  // Throttle function for performance
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  },
}

// Web Vitals monitoring
export const webVitals = {
  // Report Web Vitals
  reportWebVitals(metric: any): void {
    if (process.env.NODE_ENV === 'production') {
      // Send to analytics service
      console.log('Web Vital:', metric)

      // Example: Send to Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        ;(window as any).gtag('event', metric.name, {
          custom_map: { metric_id: 'custom_metric' },
          value: Math.round(
            metric.name === 'CLS' ? metric.value * 1000 : metric.value
          ),
          event_category: 'Web Vitals',
          event_label: metric.id,
          non_interaction: true,
        })
      }
    }
  },

  // Monitor Core Web Vitals
  monitorCoreWebVitals(): void {
    if (typeof window !== 'undefined') {
      import('web-vitals')
        .then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          getCLS(this.reportWebVitals)
          getFID(this.reportWebVitals)
          getFCP(this.reportWebVitals)
          getLCP(this.reportWebVitals)
          getTTFB(this.reportWebVitals)
        })
        .catch(error => {
          console.warn('Failed to load web-vitals:', error)
        })
    }
  },
}

// Service Worker utilities
export const serviceWorker = {
  // Register service worker
  register(): void {
    if (
      typeof window !== 'undefined' &&
      'serviceWorker' in navigator &&
      process.env.NODE_ENV === 'production'
    ) {
      navigator.serviceWorker
        .register('/sw.js')
        .then(registration => {
          console.log('SW registered: ', registration)
        })
        .catch(registrationError => {
          console.log('SW registration failed: ', registrationError)
        })
    }
  },

  // Update service worker
  update(): void {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(registration => {
        registration.update()
      })
    }
  },
}
