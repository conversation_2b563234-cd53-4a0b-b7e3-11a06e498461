'use client'

export const runtime = 'edge'

import { ArrowLeft, Calendar, Share2, Tag } from 'lucide-react'
import Image from 'next/image'
import { useParams } from 'next/navigation'
import { PageWrapper } from '@/components/page-wrapper'
import { useNewsItem } from '@/hooks/use-api'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'

export default function NewsDetailPage() {
  const { language } = useLanguage()
  const params = useParams()
  const id = params.id as string

  const { data: newsItem, loading, error } = useNewsItem(id)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-green" />
      </div>
    )
  }

  if (error || !newsItem) {
    return (
      <PageWrapper
        title="News Not Found"
        titleBM="Berita Tidak Ditemui"
        showBreadcrumbs={false}
      >
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {language === 'en' ? 'News Not Found' : 'Berita Tidak Ditemui'}
          </h2>
          <p className="text-gray-600 mb-6">
            {language === 'en'
              ? 'The news article you are looking for does not exist or has been removed.'
              : 'Artikel berita yang anda cari tidak wujud atau telah dialih keluar.'}
          </p>
          <Link href="/news" className="btn-primary">
            {language === 'en' ? 'Back to News' : 'Kembali ke Berita'}
          </Link>
        </div>
      </PageWrapper>
    )
  }

  const title = language === 'bm' ? newsItem.titleBM : newsItem.title
  const content = language === 'bm' ? newsItem.contentBM : newsItem.content
  const category = language === 'bm' ? newsItem.categoryBM : newsItem.category

  const breadcrumbs = [
    {
      label: 'News',
      labelBM: 'Berita',
      href: '/news',
    },
    {
      label: title,
      labelBM: title,
    },
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(language === 'bm' ? 'ms-MY' : 'en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: `${content.substring(0, 100)}...`,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  return (
    <PageWrapper
      title={title}
      titleBM={title}
      breadcrumbs={breadcrumbs}
      showTitle={false}
      headerActions={
        <button
          onClick={handleShare}
          className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-primary-green transition-colors"
        >
          <Share2 className="w-4 h-4" />
          {language === 'en' ? 'Share' : 'Kongsi'}
        </button>
      }
    >
      {/* Back Button */}
      <div className="mb-6">
        <Link
          href="/news"
          className="inline-flex items-center gap-2 text-primary-green hover:text-primary-green-dark transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          {language === 'en' ? 'Back to News' : 'Kembali ke Berita'}
        </Link>
      </div>

      {/* Article */}
      <article className="max-w-4xl mx-auto">
        <header className="mb-8">
          {/* Category and Date */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <span className="inline-flex items-center gap-2 px-3 py-1 bg-primary-green bg-opacity-10 text-primary-green rounded-full text-sm font-medium">
              <Tag className="w-3 h-3" />
              {category}
            </span>

            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="w-4 h-4" />
              <time dateTime={newsItem.date}>{formatDate(newsItem.date)}</time>
            </div>
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight mb-6">
            {title}
          </h1>

          {/* Featured Image */}
          {newsItem.image && (
            <div className="relative w-full h-64 md:h-96 mb-8 rounded-xl overflow-hidden">
              <Image
                src={newsItem.image}
                alt={title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 1200px"
                priority
              />
            </div>
          )}
        </header>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
            {content}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
            <div className="text-sm text-gray-600">
              {language === 'en' ? 'Published on' : 'Diterbitkan pada'}{' '}
              {formatDate(newsItem.date)}
            </div>

            <div className="flex items-center gap-3">
              <button onClick={handleShare} className="btn-secondary">
                <Share2 className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Share' : 'Kongsi'}
              </button>

              <Link href="/news" className="btn-primary">
                {language === 'en' ? 'More News' : 'Lebih Banyak Berita'}
              </Link>
            </div>
          </div>
        </div>
      </article>
    </PageWrapper>
  )
}
