# JAKIM Halal Portal

A comprehensive web portal for JAKIM's Halal Management Division, providing information about Halal certification, procedures, and services in Malaysia.

## 🌟 Features

### Core Functionality

- **Bilingual Support**: Full English and Bahasa Malaysia language support
- **Halal Certificate Search**: Advanced search functionality with filters
- **QR Code Scanner**: Scan QR codes for certificate verification
- **Contact Forms**: Interactive contact and complaint forms
- **Responsive Design**: Mobile-first responsive design
- **Accessibility**: WCAG 2.1 compliant accessibility features

### Content Management

- **Corporate Information**: About us, vision & mission, organization chart
- **Procedure Guides**: Application process, requirements, guidelines
- **Information Resources**: Circulars, press releases, journals
- **E-Aduan Integration**: Complaint filing system
- **News & Announcements**: Latest updates and news

### Technical Features

- **Performance Optimized**: Fast loading with code splitting
- **SEO Friendly**: Optimized for search engines
- **Progressive Web App**: PWA capabilities
- **Type Safety**: Full TypeScript implementation
- **Testing**: Comprehensive test coverage

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd jakim-halal-portal/front

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 📁 Project Structure

```
front/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (routes)/          # Route groups
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable components
│   │   ├── ui/               # Basic UI components
│   │   ├── forms/            # Form components
│   │   └── __tests__/        # Component tests
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utility libraries
│   ├── types/                # TypeScript type definitions
│   ├── data/                 # Static data and constants
│   └── test-utils/           # Testing utilities
├── public/                   # Static assets
├── e2e/                     # End-to-end tests
├── scripts/                 # Build and utility scripts
└── docs/                    # Documentation
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks

# Testing
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
npm run test:e2e     # Run end-to-end tests
npm run test:all     # Run all tests

# Utilities
npm run analyze      # Analyze bundle size
npm run clean        # Clean build artifacts
```

### Environment Variables

Create a `.env.local` file with the following variables:

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.halal.gov.my
NEXT_PUBLIC_SEARCH_API_URL=https://search.halal.gov.my

# External Services
NEXT_PUBLIC_EADUAN_URL=https://eaduan.islam.gov.my
NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=https://myehalal.halal.gov.my/domestik/v1/
NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=https://myehalal.halal.gov.my/international/v1/

# Analytics (optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Development
NODE_ENV=development
```

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
