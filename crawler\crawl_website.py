#!/usr/bin/env python3
"""
Website Crawler using Crawl4AI
Crawls websites recursively and downloads PDF/DOC/DOCX files
"""

import asyncio
import os
import re
import aiohttp
import aiofiles
import json
import sqlite3
import time
import ssl
import hashlib
from urllib.parse import urljoin, urlparse, unquote
from pathlib import Path
from typing import Set, List, Optional, Dict, Any, Tuple
from crawl4ai import AsyncWebCrawler
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CrawlerDatabase:
    """SQLite database manager for crawler state"""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()

    def _init_database(self) -> None:
        """Initialize database with required tables and indexes"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA foreign_keys = ON")

            # Create tables
            conn.executescript("""
                -- Metadata table for crawl configuration
                CREATE TABLE IF NOT EXISTS crawl_metadata (
                    id INTEGER PRIMARY KEY,
                    base_url TEXT NOT NULL,
                    max_depth INTEGER NOT NULL,
                    max_pages INTEGER NOT NULL,
                    start_time REAL NOT NULL,
                    last_updated REAL NOT NULL
                );

                -- Visited URLs table
                CREATE TABLE IF NOT EXISTS visited_urls (
                    url TEXT PRIMARY KEY,
                    visited_at REAL NOT NULL,
                    depth INTEGER NOT NULL
                );

                -- Crawled content table
                CREATE TABLE IF NOT EXISTS crawled_content (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL UNIQUE,
                    title TEXT,
                    content TEXT,
                    markdown TEXT,
                    depth INTEGER NOT NULL,
                    links_found INTEGER DEFAULT 0,
                    crawled_at REAL NOT NULL
                );

                -- Downloaded files table
                CREATE TABLE IF NOT EXISTS downloaded_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL UNIQUE,
                    file_path TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_hash TEXT,
                    downloaded_at REAL NOT NULL
                );

                -- Crawl queue table
                CREATE TABLE IF NOT EXISTS crawl_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL,
                    depth INTEGER NOT NULL,
                    added_at REAL NOT NULL
                );

                -- Create indexes for efficient lookups
                CREATE INDEX IF NOT EXISTS idx_visited_urls_url ON visited_urls(url);
                CREATE INDEX IF NOT EXISTS idx_crawled_content_url ON crawled_content(url);
                CREATE INDEX IF NOT EXISTS idx_downloaded_files_url ON downloaded_files(url);
                CREATE INDEX IF NOT EXISTS idx_downloaded_files_filename ON downloaded_files(filename);
                CREATE INDEX IF NOT EXISTS idx_downloaded_files_hash ON downloaded_files(file_hash);
                CREATE INDEX IF NOT EXISTS idx_crawl_queue_url ON crawl_queue(url);
                CREATE INDEX IF NOT EXISTS idx_crawl_queue_depth ON crawl_queue(depth);
            """)

            # Migrate existing database if needed
            self._migrate_database(conn)
            conn.commit()

    def _migrate_database(self, conn) -> None:
        """Migrate existing database to add new columns if they don't exist"""
        try:
            # Check if new columns exist in downloaded_files table
            cursor = conn.execute("PRAGMA table_info(downloaded_files)")
            columns = [row[1] for row in cursor.fetchall()]

            # Add missing columns
            if 'filename' not in columns:
                conn.execute("ALTER TABLE downloaded_files ADD COLUMN filename TEXT DEFAULT ''")
                logger.info("Added filename column to downloaded_files table")

            if 'file_size' not in columns:
                conn.execute("ALTER TABLE downloaded_files ADD COLUMN file_size INTEGER DEFAULT 0")
                logger.info("Added file_size column to downloaded_files table")

            if 'file_hash' not in columns:
                conn.execute("ALTER TABLE downloaded_files ADD COLUMN file_hash TEXT DEFAULT ''")
                logger.info("Added file_hash column to downloaded_files table")

            # Update existing records with missing data
            cursor = conn.execute("SELECT id, file_path FROM downloaded_files WHERE filename = '' OR filename IS NULL")
            for row in cursor.fetchall():
                file_id, file_path = row
                filename = os.path.basename(file_path) if file_path else f"file_{file_id}"
                conn.execute("UPDATE downloaded_files SET filename = ? WHERE id = ?", (filename, file_id))

        except Exception as e:
            logger.warning(f"Database migration failed: {e}")

    def clear_all_data(self) -> None:
        """Clear all data from the database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.executescript("""
                DELETE FROM crawl_queue;
                DELETE FROM downloaded_files;
                DELETE FROM crawled_content;
                DELETE FROM visited_urls;
                DELETE FROM crawl_metadata;
            """)
            conn.commit()

    def save_metadata(self, base_url: str, max_depth: int, max_pages: int, start_time: float) -> None:
        """Save or update crawl metadata"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO crawl_metadata
                (id, base_url, max_depth, max_pages, start_time, last_updated)
                VALUES (1, ?, ?, ?, ?, ?)
            """, (base_url, max_depth, max_pages, start_time, time.time()))
            conn.commit()

    def get_metadata(self) -> Optional[Dict[str, Any]]:
        """Get crawl metadata"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM crawl_metadata WHERE id = 1")
            row = cursor.fetchone()
            return dict(row) if row else None

    def is_url_visited(self, url: str) -> bool:
        """Check if URL has been visited"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT 1 FROM visited_urls WHERE url = ?", (url,))
            return cursor.fetchone() is not None

    def add_visited_url(self, url: str, depth: int) -> None:
        """Add URL to visited set"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR IGNORE INTO visited_urls (url, visited_at, depth)
                VALUES (?, ?, ?)
            """, (url, time.time(), depth))
            conn.commit()

    def get_visited_urls(self) -> List[str]:
        """Get all visited URLs"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT url FROM visited_urls")
            return [row[0] for row in cursor.fetchall()]

    def get_visited_count(self) -> int:
        """Get count of visited URLs"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM visited_urls")
            return cursor.fetchone()[0]

    def add_crawled_content(self, url: str, title: str, content: str, markdown: str,
                           depth: int, links_found: int) -> None:
        """Add crawled page content"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO crawled_content
                (url, title, content, markdown, depth, links_found, crawled_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (url, title, content, markdown, depth, links_found, time.time()))
            conn.commit()

    def get_crawled_content(self) -> List[Dict[str, Any]]:
        """Get all crawled content"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT url, title, content, markdown, depth, links_found, crawled_at
                FROM crawled_content ORDER BY crawled_at
            """)
            return [dict(row) for row in cursor.fetchall()]

    def get_crawled_count(self) -> int:
        """Get count of crawled pages"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM crawled_content")
            return cursor.fetchone()[0]

    def is_file_downloaded(self, url: str) -> bool:
        """Check if file URL has been downloaded"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT 1 FROM downloaded_files WHERE url = ?", (url,))
            return cursor.fetchone() is not None

    def is_file_downloaded_by_name(self, filename: str) -> Optional[str]:
        """Check if file with same name has been downloaded, return existing path if found"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT file_path FROM downloaded_files WHERE filename = ?", (filename,))
            result = cursor.fetchone()
            return result[0] if result else None

    def is_file_downloaded_by_hash(self, file_hash: str) -> Optional[str]:
        """Check if file with same hash has been downloaded, return existing path if found"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT file_path FROM downloaded_files WHERE file_hash = ?", (file_hash,))
            result = cursor.fetchone()
            return result[0] if result else None

    def add_downloaded_file(self, url: str, file_path: str, filename: str, file_size: int, file_hash: str) -> None:
        """Add downloaded file record"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR IGNORE INTO downloaded_files (url, file_path, filename, file_size, file_hash, downloaded_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (url, file_path, filename, file_size, file_hash, time.time()))
            conn.commit()

    def get_downloaded_files(self) -> List[str]:
        """Get all downloaded file paths"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT file_path FROM downloaded_files ORDER BY downloaded_at")
            return [row[0] for row in cursor.fetchall()]

    def get_downloaded_count(self) -> int:
        """Get count of downloaded files"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM downloaded_files")
            return cursor.fetchone()[0]

    def add_to_queue(self, url: str, depth: int) -> None:
        """Add URL to crawl queue"""
        with sqlite3.connect(self.db_path) as conn:
            # Check if URL is already in queue
            cursor = conn.execute("SELECT 1 FROM crawl_queue WHERE url = ?", (url,))
            if cursor.fetchone() is None:
                conn.execute("""
                    INSERT INTO crawl_queue (url, depth, added_at)
                    VALUES (?, ?, ?)
                """, (url, depth, time.time()))
                conn.commit()

    def get_next_from_queue(self) -> Optional[Tuple[str, int]]:
        """Get next URL from queue (FIFO)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, url, depth FROM crawl_queue
                ORDER BY added_at LIMIT 1
            """)
            row = cursor.fetchone()
            if row:
                # Remove from queue
                conn.execute("DELETE FROM crawl_queue WHERE id = ?", (row[0],))
                conn.commit()
                return (row[1], row[2])  # url, depth
            return None

    def get_queue_count(self) -> int:
        """Get count of URLs in queue"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM crawl_queue")
            return cursor.fetchone()[0]

    def get_queue_urls(self) -> List[Tuple[str, int]]:
        """Get all URLs in queue (for compatibility)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT url, depth FROM crawl_queue ORDER BY added_at")
            return cursor.fetchall()

class WebsiteCrawler:
    def __init__(self, base_url: str, max_depth: int = 3, max_pages: int = 100,
                 force_recrawl: bool = False, resume: bool = True):
        """
        Initialize the website crawler

        Args:
            base_url: The base URL to start crawling from
            max_depth: Maximum depth to crawl (default: 3)
            max_pages: Maximum number of pages to crawl (default: 100)
            force_recrawl: If True, ignore existing data and recrawl everything (default: False)
            resume: If True, resume from previous crawl state (default: True)
        """
        # Ensure URL has a protocol
        if not base_url.startswith(('http://', 'https://')):
            base_url = f"https://{base_url}"

        self.base_url = base_url.rstrip('/')
        self.domain = urlparse(self.base_url).netloc
        self.max_depth = max_depth
        self.max_pages = max_pages
        self.force_recrawl = force_recrawl
        self.resume = resume
        self.start_time = time.time()

        # Create output directories
        self.domain_clean = re.sub(r'[^\w\-_.]', '_', self.domain)
        self.output_dir = Path(f"output/{self.domain_clean}")
        self.files_dir = self.output_dir / "files"
        self.db_file = self.output_dir / "crawl_state.db"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.files_dir.mkdir(parents=True, exist_ok=True)

        # Initialize database
        self.db = CrawlerDatabase(self.db_file)

        # Load previous state if resuming
        if self.resume and not self.force_recrawl:
            self.load_state()
        elif self.force_recrawl:
            self.clear_previous_data()

        logger.info(f"Initialized crawler for {self.base_url}")
        logger.info(f"Output directory: {self.output_dir}")
        logger.info(f"Force recrawl: {self.force_recrawl}, Resume: {self.resume}")
        logger.info(f"Starting with {self.db.get_visited_count()} visited URLs, {self.db.get_crawled_count()} pages")

    def load_state(self) -> None:
        """Load previous crawl state from database"""
        try:
            metadata = self.db.get_metadata()
            if metadata:
                # Verify this is the same crawl
                if metadata['base_url'] == self.base_url:
                    logger.info(f"Loaded previous state: {self.db.get_visited_count()} visited URLs, "
                               f"{self.db.get_crawled_count()} pages, {self.db.get_downloaded_count()} downloaded files, "
                               f"{self.db.get_queue_count()} queued")
                else:
                    logger.warning(f"Base URL mismatch in database. Expected: {self.base_url}, Found: {metadata['base_url']}")
                    logger.info("Starting fresh crawl")
                    self.db.clear_all_data()
            else:
                logger.info("No previous state found, starting fresh crawl")
        except Exception as e:
            logger.warning(f"Failed to load previous state: {e}")
            logger.info("Starting fresh crawl")

    def save_state(self) -> None:
        """Save current crawl state to database"""
        try:
            self.db.save_metadata(self.base_url, self.max_depth, self.max_pages, self.start_time)
            logger.debug(f"Saved crawl state: {self.db.get_visited_count()} visited URLs")
        except Exception as e:
            logger.error(f"Failed to save state: {e}")

    def clear_previous_data(self) -> None:
        """Clear all previous crawl data"""
        try:
            # Clear database
            self.db.clear_all_data()

            # Remove database file if it exists
            if self.db_file.exists():
                self.db_file.unlink()
                # Reinitialize database
                self.db = CrawlerDatabase(self.db_file)

            # Remove markdown file
            markdown_file = self.output_dir / f"{self.domain_clean}.md"
            if markdown_file.exists():
                markdown_file.unlink()

            # Clear files directory
            if self.files_dir.exists():
                for file_path in self.files_dir.iterdir():
                    if file_path.is_file():
                        file_path.unlink()

            logger.info("Cleared previous crawl data")
        except Exception as e:
            logger.warning(f"Failed to clear previous data: {e}")

    def is_same_domain(self, url: str) -> bool:
        """Check if URL belongs to the same domain or subdomain"""
        try:
            parsed = urlparse(url)
            return parsed.netloc == self.domain or parsed.netloc.endswith(f'.{self.domain}')
        except:
            return False

    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and properly formatted"""
        try:
            # Skip obviously malformed URLs
            if not url or not isinstance(url, str):
                return False

            # Skip URLs containing wp-json/ (WordPress JSON API endpoints)
            if 'wp-json/' in url:
                logger.debug(f"Skipping WordPress JSON API URL: {url}")
                return False
            
            if 'xmlrpc.php' in url:
                logger.debug(f"Skipping XML-RPC URL: {url}")
                return False

            # Skip URLs containing JSON-like structures
            if '{' in url or '}' in url or "{'href':" in url:
                logger.debug(f"Skipping malformed URL with JSON structure: {url}")
                return False

            # Skip URLs with invalid characters
            invalid_chars = ['<', '>', '"', "'", '`', '\n', '\r', '\t']
            if any(char in url for char in invalid_chars):
                logger.debug(f"Skipping URL with invalid characters: {url}")
                return False

            # Try to parse the URL
            parsed = urlparse(url)

            # Must have a scheme and netloc
            if not parsed.scheme or not parsed.netloc:
                return False

            # Must be HTTP or HTTPS
            if parsed.scheme not in ['http', 'https']:
                return False

            # Check for reasonable URL length (avoid extremely long URLs)
            if len(url) > 2000:
                logger.debug(f"Skipping extremely long URL: {url[:100]}...")
                return False

            # Check for recursive path patterns
            if parsed.path:
                path_parts = [part for part in parsed.path.split('/') if part]
                if len(path_parts) > 1:
                    # Check for consecutive duplicate file names
                    for i in range(len(path_parts) - 1):
                        if (path_parts[i] == path_parts[i + 1] and
                            '.' in path_parts[i]):
                            logger.debug(f"Skipping URL with consecutive duplicate files: {url}")
                            return False

                    # Check for any duplicate file names in the path (not just consecutive)
                    file_parts = [part for part in path_parts if '.' in part]
                    if len(file_parts) != len(set(file_parts)):
                        logger.debug(f"Skipping URL with duplicate file names in path: {url}")
                        return False

                    # Check for excessive path depth (more than 8 segments)
                    if len(path_parts) > 8:
                        logger.debug(f"Skipping URL with excessive path depth: {url}")
                        return False

            return True
        except Exception as e:
            logger.debug(f"URL validation failed for {url}: {e}")
            return False

    def safe_urljoin(self, base_url: str, relative_url: str) -> str:
        """Safely join URLs with better handling of PHP files and relative paths"""
        try:
            # If relative_url is already absolute, return it
            if relative_url.startswith(('http://', 'https://')):
                return relative_url

            # Parse the base URL
            parsed_base = urlparse(base_url)

            # Check for potential recursive pattern before joining
            if parsed_base.path and '.' in parsed_base.path.split('/')[-1]:
                # Base URL ends with a file
                base_filename = parsed_base.path.split('/')[-1]
                # If relative URL is the same filename, this would create recursion
                if relative_url == base_filename:
                    logger.debug(f"Preventing recursive join: {base_url} + {relative_url}")
                    return ""

                # Base URL points to a file, so we need to get its directory
                base_dir = '/'.join(parsed_base.path.split('/')[:-1])
                if not base_dir.endswith('/'):
                    base_dir += '/'

                # Reconstruct base URL with directory
                clean_base = f"{parsed_base.scheme}://{parsed_base.netloc}{base_dir}"
            else:
                # Base URL is a directory, use as-is but ensure it ends with /
                clean_base = base_url
                if not clean_base.endswith('/'):
                    clean_base += '/'

            # Join the URLs
            result = urljoin(clean_base, relative_url)

            # Additional validation: check if the result makes sense
            parsed_result = urlparse(result)

            # Prevent obvious recursive patterns in the final result
            if parsed_result.path:
                path_parts = [part for part in parsed_result.path.split('/') if part]
                # Check for immediate duplicates
                for i in range(len(path_parts) - 1):
                    if path_parts[i] == path_parts[i + 1] and '.' in path_parts[i]:
                        logger.debug(f"Detected recursive pattern in joined URL: {result}")
                        return ""  # Return empty string to indicate invalid URL

            return result

        except Exception as e:
            logger.debug(f"Error in safe_urljoin for base='{base_url}', relative='{relative_url}': {e}")
            return ""

    def normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments, fixing common issues"""
        try:
            # Remove fragments
            url = url.split('#')[0]

            # Remove trailing slash for consistency (except for root)
            parsed = urlparse(url)
            if parsed.path != '/' and parsed.path.endswith('/'):
                url = url.rstrip('/')

            # Fix double slashes in path
            if '//' in parsed.path and parsed.path != '/':
                fixed_path = re.sub(r'/+', '/', parsed.path)
                url = url.replace(parsed.path, fixed_path)

            # Fix recursive path issues (e.g., /index.php/index.php/index.php)
            if parsed.path:
                path_parts = parsed.path.split('/')
                # Remove consecutive duplicate file names
                cleaned_parts = []
                for part in path_parts:
                    if not part:  # Skip empty parts
                        continue
                    # Check if this part is the same as the previous part and looks like a file
                    if (cleaned_parts and
                        part == cleaned_parts[-1] and
                        '.' in part):
                        logger.debug(f"Removing duplicate path segment: {part}")
                        continue
                    cleaned_parts.append(part)

                # Rebuild the path
                if cleaned_parts:
                    new_path = '/' + '/'.join(cleaned_parts)
                    url = url.replace(parsed.path, new_path)

            return url
        except Exception as e:
            logger.debug(f"URL normalization failed for {url}: {e}")
            return url

    def is_downloadable_file(self, url: str) -> bool:
        """Check if URL points to a downloadable file (PDF, DOC, DOCX)"""
        url_lower = url.lower()
        return any(url_lower.endswith(ext) for ext in ['.pdf', '.doc', '.docx'])

    def is_static_asset(self, url: str) -> bool:
        """Check if URL points to a static asset that should not be crawled as a web page"""
        try:
            # Parse URL to get the path without query parameters
            parsed = urlparse(url.lower())
            path = parsed.path

            static_extensions = [
                # Stylesheets
                '.css',
                # JavaScript
                '.js',
                # Images
                '.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico', '.bmp', '.tiff',
                # Fonts
                '.woff', '.woff2', '.ttf', '.otf', '.eot',
                # Data files
                '.xml', '.json', '.csv', '.txt',
                # Media files
                '.mp4', '.mp3', '.avi', '.mov', '.wav', '.ogg',
                # Archives
                '.zip', '.rar', '.tar', '.gz',
                # Other assets
                '.map'  # Source maps
            ]

            # Check if the path (without query parameters) ends with any static extension
            return any(path.endswith(ext) for ext in static_extensions)
        except Exception as e:
            logger.debug(f"Error checking static asset for {url}: {e}")
            # Fallback to original method if URL parsing fails
            url_lower = url.lower()
            static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico', '.bmp', '.tiff',
                               '.woff', '.woff2', '.ttf', '.otf', '.eot', '.xml', '.json', '.csv', '.txt',
                               '.mp4', '.mp3', '.avi', '.mov', '.wav', '.ogg', '.zip', '.rar', '.tar', '.gz', '.map']
            return any(url_lower.endswith(ext) for ext in static_extensions)

    def calculate_file_hash(self, file_content: bytes) -> str:
        """Calculate SHA-256 hash of file content"""
        return hashlib.sha256(file_content).hexdigest()

    async def download_file(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Download a file and save it to the files directory with comprehensive duplicate detection"""
        # Check if this file URL was already downloaded
        if self.db.is_file_downloaded(url):
            logger.info(f"Skipping already downloaded file (by URL): {url}")
            return None

        try:
            logger.info(f"Downloading file: {url}")

            async with session.get(url) as response:
                if response.status == 200:
                    # Get filename from URL
                    parsed_url = urlparse(url)
                    filename = os.path.basename(unquote(parsed_url.path))

                    # If no filename, generate one
                    if not filename or '.' not in filename:
                        content_disposition = response.headers.get('content-disposition', '')
                        if 'filename=' in content_disposition:
                            filename = content_disposition.split('filename=')[1].strip('"')
                        else:
                            # Generate filename based on URL
                            filename = f"file_{self.db.get_downloaded_count()}.pdf"

                    # Check if file with same name already exists in database
                    existing_path = self.db.is_file_downloaded_by_name(filename)
                    if existing_path and os.path.exists(existing_path):
                        logger.info(f"Skipping file with same name already downloaded: {filename} -> {existing_path}")
                        return existing_path

                    # Download content to check for duplicates by hash
                    content = await response.read()
                    file_size = len(content)
                    file_hash = self.calculate_file_hash(content)

                    # Check if file with same hash already exists
                    existing_path = self.db.is_file_downloaded_by_hash(file_hash)
                    if existing_path and os.path.exists(existing_path):
                        logger.info(f"Skipping duplicate file (same content): {filename} -> {existing_path}")
                        # Still record this URL as downloaded, pointing to the existing file
                        self.db.add_downloaded_file(url, existing_path, filename, file_size, file_hash)
                        return existing_path

                    # Ensure unique filename on disk
                    file_path = self.files_dir / filename
                    counter = 1
                    original_filename = filename
                    while file_path.exists():
                        name, ext = os.path.splitext(original_filename)
                        filename = f"{name}_{counter}{ext}"
                        file_path = self.files_dir / filename
                        counter += 1

                    # Save the file
                    async with aiofiles.open(file_path, 'wb') as f:
                        await f.write(content)

                    # Verify file was written correctly
                    if not file_path.exists() or file_path.stat().st_size != file_size:
                        logger.error(f"File verification failed for {file_path}")
                        return None

                    logger.info(f"Downloaded: {file_path} ({file_size} bytes)")
                    self.db.add_downloaded_file(url, str(file_path), filename, file_size, file_hash)
                    return str(file_path)
                else:
                    logger.warning(f"Failed to download {url}: HTTP {response.status}")
        except Exception as e:
            logger.error(f"Error downloading {url}: {str(e)}")

        return None

    def extract_links(self, html_content: str, current_url: str) -> Set[str]:
        """Extract all links from HTML content with improved validation"""
        links = set()

        try:
            # Simple regex to find links (href attributes)
            link_pattern = r'href=["\']([^"\']+)["\']'
            matches = re.findall(link_pattern, html_content, re.IGNORECASE)

            for match in matches:
                try:
                    # Skip obviously invalid matches
                    if not match or match.startswith('#') or match.startswith('javascript:') or match.startswith('mailto:'):
                        continue

                    # Convert relative URLs to absolute with improved handling
                    absolute_url = self.safe_urljoin(current_url, match)

                    # Skip if safe_urljoin returned empty string (invalid)
                    if not absolute_url:
                        continue

                    # Validate the URL
                    if not self.is_valid_url(absolute_url):
                        continue

                    # Only include same-domain links and exclude static assets
                    if self.is_same_domain(absolute_url) and not self.is_static_asset(absolute_url):
                        # Normalize the URL
                        clean_url = self.normalize_url(absolute_url)
                        if clean_url:  # Only add if normalization succeeded
                            links.add(clean_url)

                except Exception as e:
                    logger.debug(f"Error processing link '{match}': {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in extract_links: {e}")

        return links

    async def crawl_page(self, crawler: AsyncWebCrawler, session: aiohttp.ClientSession,
                        url: str, depth: int = 0) -> None:
        """Crawl a single page and extract content and links"""
        if (self.db.is_url_visited(url) or
            depth > self.max_depth or
            self.db.get_visited_count() >= self.max_pages):
            return

        self.db.add_visited_url(url, depth)
        logger.info(f"Crawling (depth {depth}): {url} [{self.db.get_visited_count()}/{self.max_pages}]")

        try:
            # Check if it's a downloadable file
            if self.is_downloadable_file(url):
                await self.download_file(session, url)
                return

            # Skip static assets (CSS, JS, images, etc.)
            if self.is_static_asset(url):
                logger.debug(f"Skipping static asset: {url}")
                return

            # Crawl the page using Crawl4AI
            result = await crawler.arun(
                url=url,
                word_count_threshold=10,
                exclude_external_links=True,
                exclude_social_media_links=True,
                process_iframes=True,
                remove_overlay_elements=True
            )

            if result.success:
                # Store page content
                title = result.metadata.get('title', '') if result.metadata else ''
                content = result.cleaned_html or ''
                markdown = result.markdown or ''
                links_found = 0  # Will be updated later

                # Get links from Crawl4AI result with improved validation
                internal_links = set()
                if result.links and 'internal' in result.links:
                    for link in result.links['internal']:
                        try:
                            # Handle different link formats from Crawl4AI
                            link_url = None

                            if isinstance(link, str):
                                link_url = link
                            elif isinstance(link, dict):
                                # Handle dictionary format: {'href': 'url', 'text': 'text', ...}
                                link_url = link.get('href') or link.get('url')
                            else:
                                # Try to convert to string as fallback
                                link_str = str(link)
                                # Skip if it looks like a malformed JSON structure
                                if "{'href':" in link_str or '{' in link_str:
                                    logger.debug(f"Skipping malformed Crawl4AI link: {link_str[:100]}...")
                                    continue
                                link_url = link_str

                            if not link_url:
                                continue

                            # Convert relative URLs to absolute with improved handling
                            absolute_url = self.safe_urljoin(url, link_url)

                            # Skip if safe_urljoin returned empty string (invalid)
                            if not absolute_url:
                                continue

                            # Validate the URL
                            if not self.is_valid_url(absolute_url):
                                continue

                            # Only include same-domain links and exclude static assets
                            if self.is_same_domain(absolute_url) and not self.is_static_asset(absolute_url):
                                # Normalize the URL
                                clean_url = self.normalize_url(absolute_url)
                                if clean_url:
                                    internal_links.add(clean_url)

                        except Exception as e:
                            logger.debug(f"Error processing Crawl4AI link {link}: {e}")

                # Also extract links manually from HTML as backup
                if result.html:
                    try:
                        manual_links = self.extract_links(result.html, url)
                        internal_links.update(manual_links)
                    except Exception as e:
                        logger.debug(f"Error extracting manual links: {e}")

                # Update links found count and save page content
                links_found = len(internal_links)
                self.db.add_crawled_content(url, title, content, markdown, depth, links_found)

                # Check for downloadable files in the content
                if result.html:
                    file_pattern = r'href=["\']([^"\']*\.(?:pdf|doc|docx))["\']'
                    file_matches = re.findall(file_pattern, result.html, re.IGNORECASE)

                    for file_match in file_matches:
                        file_url = self.safe_urljoin(url, file_match)
                        if file_url and self.is_same_domain(file_url):
                            await self.download_file(session, file_url)

                # Add new links to crawl queue with validation
                if depth < self.max_depth:
                    for link in internal_links:
                        # Double-check URL validity before adding to queue
                        if (self.is_valid_url(link) and
                            not self.db.is_url_visited(link) and
                            not self.is_static_asset(link)):
                            self.db.add_to_queue(link, depth + 1)
                            logger.debug(f"Added to queue: {link}")

                # Save state periodically (every 10 pages)
                if self.db.get_crawled_count() % 10 == 0:
                    self.save_state()

            else:
                logger.warning(f"Failed to crawl {url}: {getattr(result, 'error_message', 'Unknown error')}")

        except Exception as e:
            logger.error(f"Error crawling {url}: {str(e)}")

    async def save_markdown_report(self) -> str:
        """Save all crawled content as a markdown file"""
        markdown_file = self.output_dir / f"{self.domain_clean}.md"
        
        crawled_content = self.db.get_crawled_content()
        downloaded_files = self.db.get_downloaded_files()

        content = f"# Website Crawl Report: {self.domain}\n\n"
        content += f"**Base URL:** {self.base_url}\n"
        content += f"**Crawl Date:** {time.time()}\n"
        content += f"**Pages Crawled:** {len(crawled_content)}\n"
        content += f"**Files Downloaded:** {len(downloaded_files)}\n\n"
        
        # Add table of contents
        content += "## Table of Contents\n\n"
        for i, page in enumerate(crawled_content, 1):
            title = page['title'] or f"Page {i}"
            content += f"{i}. [{title}]({page['url']})\n"
        content += "\n"

        # Add downloaded files section
        if downloaded_files:
            content += "## Downloaded Files\n\n"
            for file_path in downloaded_files:
                filename = os.path.basename(file_path)
                content += f"- {filename}\n"
            content += "\n"

        # Add page content
        content += "## Page Content\n\n"
        for i, page in enumerate(crawled_content, 1):
            content += f"### {i}. {page['title'] or 'Untitled Page'}\n\n"
            content += f"**URL:** {page['url']}\n"
            content += f"**Depth:** {page['depth']}\n\n"
            content += page['markdown']
            content += "\n\n---\n\n"
        
        async with aiofiles.open(markdown_file, 'w', encoding='utf-8') as f:
            await f.write(content)
        
        logger.info(f"Markdown report saved: {markdown_file}")
        return str(markdown_file)

    async def crawl(self) -> dict:
        """Main crawling function with queue-based processing"""
        logger.info(f"Starting crawl of {self.base_url}")

        # Initialize crawl queue if empty (new crawl or no resume data)
        if self.db.get_queue_count() == 0 and not self.db.is_url_visited(self.base_url):
            self.db.add_to_queue(self.base_url, 0)

        # Configure Crawl4AI with proper settings
        crawler_config = {
            'headless': True,
            'verbose': True,
            'browser_type': 'chromium',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        async with AsyncWebCrawler(**crawler_config) as crawler:
            # Create SSL context that ignores SSL errors
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            # Additional SSL options for compatibility
            ssl_context.set_ciphers('DEFAULT:@SECLEVEL=1')

            # Create connector with SSL context and increased timeout for file downloads
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=60, connect=30),  # Increased timeout for large files
                headers={'User-Agent': crawler_config['user_agent']}
            ) as session:

                # Process crawl queue
                processed_count = 0
                skipped_count = 0
                while self.db.get_queue_count() > 0 and self.db.get_visited_count() < self.max_pages:
                    # Get next URL from queue
                    queue_item = self.db.get_next_from_queue()
                    if not queue_item:
                        break

                    url, depth = queue_item

                    # Skip if already visited or depth exceeded
                    if self.db.is_url_visited(url) or depth > self.max_depth:
                        continue

                    # Validate URL before processing
                    if not self.is_valid_url(url):
                        skipped_count += 1
                        logger.warning(f"Skipping malformed URL: {url}")
                        continue

                    # Crawl the page
                    await self.crawl_page(crawler, session, url, depth)
                    processed_count += 1

                    # Add small delay to be respectful
                    await asyncio.sleep(0.5)

                    # Log progress
                    if processed_count % 5 == 0:
                        logger.info(f"Progress: {self.db.get_visited_count()} pages crawled, "
                                   f"{self.db.get_queue_count()} in queue, "
                                   f"{self.db.get_downloaded_count()} files downloaded, "
                                   f"{skipped_count} malformed URLs skipped")

        # Final state save
        self.save_state()

        # Save results
        markdown_file = await self.save_markdown_report()

        # Calculate crawl duration
        duration = time.time() - self.start_time

        result = {
            'domain': self.domain,
            'base_url': self.base_url,
            'pages_crawled': self.db.get_crawled_count(),
            'files_downloaded': self.db.get_downloaded_count(),
            'markdown_file': markdown_file,
            'output_directory': str(self.output_dir),
            'duration_seconds': duration,
            'remaining_queue': self.db.get_queue_count()
        }

        logger.info(f"Crawl completed: {result}")
        return result


async def crawl_website(url: str, max_depth: int = 3, max_pages: int = 100,
                       force_recrawl: bool = False, resume: bool = True) -> dict:
    """
    Crawl a website recursively and save content as markdown

    Args:
        url: The website URL to crawl
        max_depth: Maximum depth to crawl (default: 3)
        max_pages: Maximum number of pages to crawl (default: 100)
        force_recrawl: If True, ignore existing data and recrawl everything (default: False)
        resume: If True, resume from previous crawl state (default: True)

    Returns:
        dict: Crawl results summary
    """
    crawler = WebsiteCrawler(url, max_depth, max_pages, force_recrawl, resume)
    return await crawler.crawl()


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("Usage: python crawl_website.py <url> [max_depth] [max_pages] [--force-recrawl] [--no-resume]")
        print("Example: python crawl_website.py https://myehalal.halal.gov.my 3 100")
        print("         python crawl_website.py https://myehalal.halal.gov.my 3 100 --force-recrawl")
        print("         python crawl_website.py https://myehalal.halal.gov.my 3 100 --no-resume")
        print("")
        print("Options:")
        print("  --force-recrawl  Clear all previous data and start fresh")
        print("  --no-resume      Don't resume from previous crawl state")
        sys.exit(1)

    url = sys.argv[1]
    max_depth = 3
    max_pages = 100
    force_recrawl = False
    resume = True

    # Parse arguments
    args = sys.argv[2:]
    numeric_args = []

    for arg in args:
        if arg == '--force-recrawl':
            force_recrawl = True
        elif arg == '--no-resume':
            resume = False
        elif arg.startswith('--'):
            print(f"Unknown option: {arg}")
            sys.exit(1)
        else:
            try:
                numeric_args.append(int(arg))
            except ValueError:
                print(f"Invalid numeric argument: {arg}")
                sys.exit(1)

    # Set numeric parameters
    if len(numeric_args) > 0:
        max_depth = numeric_args[0]
    if len(numeric_args) > 1:
        max_pages = numeric_args[1]

    print(f"🕷️  Starting crawl of {url}")
    print(f"📊 Parameters: depth={max_depth}, pages={max_pages}, force_recrawl={force_recrawl}, resume={resume}")

    result = asyncio.run(crawl_website(url, max_depth, max_pages, force_recrawl, resume))

    print(f"\n✅ Crawl completed successfully!")
    print(f"📄 Pages crawled: {result['pages_crawled']}")
    print(f"📁 Files downloaded: {result['files_downloaded']}")
    print(f"⏱️  Duration: {result['duration_seconds']:.1f} seconds")
    if result['remaining_queue'] > 0:
        print(f"⏳ Remaining in queue: {result['remaining_queue']} (run again to continue)")
    print(f"💾 Results saved to: {result['output_directory']}")
