#!/bin/bash

# Website Crawler Runner Script
# Usage: ./run_crawler.sh [URL] [MAX_DEPTH] [MAX_PAGES] [--force-recrawl] [--no-resume] [--local]

set -e

# Default values
DEFAULT_URL="https://myehalal.halal.gov.my"
DEFAULT_DEPTH=3
DEFAULT_PAGES=100

# Parse arguments
URL=""
MAX_DEPTH=$DEFAULT_DEPTH
MAX_PAGES=$DEFAULT_PAGES
FORCE_RECRAWL=""
NO_RESUME=""
USE_LOCAL=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force-recrawl)
            FORCE_RECRAWL="--force-recrawl"
            shift
            ;;
        --no-resume)
            NO_RESUME="--no-resume"
            shift
            ;;
        --local)
            USE_LOCAL=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [URL] [MAX_DEPTH] [MAX_PAGES] [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --force-recrawl  Clear all previous data and start fresh"
            echo "  --no-resume      Don't resume from previous crawl state"
            echo "  --local          Run locally instead of using Docker"
            echo "  --help, -h       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 https://myehalal.halal.gov.my"
            echo "  $0 https://myehalal.halal.gov.my 2 50"
            echo "  $0 https://myehalal.halal.gov.my 3 100 --force-recrawl"
            echo "  $0 https://myehalal.halal.gov.my --local"
            exit 0
            ;;
        -*)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
        *)
            if [[ -z "$URL" ]]; then
                URL="$1"
            elif [[ "$MAX_DEPTH" == "$DEFAULT_DEPTH" ]]; then
                MAX_DEPTH="$1"
            elif [[ "$MAX_PAGES" == "$DEFAULT_PAGES" ]]; then
                MAX_PAGES="$1"
            else
                echo "Too many arguments: $1"
                echo "Use --help for usage information"
                exit 1
            fi
            shift
            ;;
    esac
done

# Set default URL if not provided
URL=${URL:-$DEFAULT_URL}

echo "🕷️  Website Crawler"
echo "=================="
echo "URL: $URL"
echo "Max Depth: $MAX_DEPTH"
echo "Max Pages: $MAX_PAGES"
echo "Force Recrawl: ${FORCE_RECRAWL:-"No"}"
echo "Resume: ${NO_RESUME:-"Yes"}"
echo "Mode: $(if $USE_LOCAL; then echo "Local"; else echo "Docker"; fi)"
echo ""

# Create output directory if it doesn't exist
mkdir -p output

if $USE_LOCAL; then
    echo "🐍 Running crawler locally..."
    echo "This may take several minutes depending on the website size..."
    echo ""

    # Check if Python dependencies are installed
    if ! python3 -c "import crawl4ai" 2>/dev/null; then
        echo "📦 Installing Python dependencies..."
        python3 -m pip install --user crawl4ai aiohttp aiofiles
        python3 -m playwright install
    fi

    # Run the crawler locally
    python3 crawl_website.py "$URL" "$MAX_DEPTH" "$MAX_PAGES" $FORCE_RECRAWL $NO_RESUME
else
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        echo "💡 Tip: Use --local to run without Docker"
        exit 1
    fi

    echo "🔨 skip Building Docker image, run make build..."
    # docker build -t website-crawler .
    # docker buildx build --platform linux/amd64 -t website-crawler .

    echo ""
    echo "🚀 Starting crawler..."
    echo "This may take several minutes depending on the website size..."
    echo ""

    # Run the crawler in Docker
    docker run --rm \
        -v "$(pwd)/output:/app/output" \
        website-crawler \
        "$URL" "$MAX_DEPTH" "$MAX_PAGES" $FORCE_RECRAWL $NO_RESUME
fi

echo ""
echo "✅ Crawling completed!"
echo ""
echo "📁 Results saved in: ./output/"
echo ""

# List the output files
if [ -d "output" ]; then
    echo "📄 Generated files:"
    find output -type f -name "*.md" -o -name "*.pdf" -o -name "*.doc" -o -name "*.docx" | head -20
    
    total_files=$(find output -type f | wc -l)
    if [ $total_files -gt 20 ]; then
        echo "... and $(($total_files - 20)) more files"
    fi
fi

echo ""
echo "🎉 Done! Check the output directory for your crawled content."
