#!/bin/bash

# Malaysian Halal Companies Crawler Setup Script

echo "🚀 Setting up Malaysian Halal Companies Crawler..."

# Check if Bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun.js is not installed. Please install it first:"
    echo "   curl -fsSL https://bun.sh/install | bash"
    exit 1
fi

echo "✅ Bun.js found"

# Install dependencies
echo "📦 Installing dependencies..."
bun install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your database configuration"
else
    echo "✅ .env file already exists"
fi

# Test database connection
echo "🔍 Testing database connection..."
bun run test

if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
    
    # Generate and apply migrations
    echo "🗄️  Setting up database schema..."
    bun run db:generate
    bun run db:migrate
    
    if [ $? -eq 0 ]; then
        echo "✅ Database schema setup complete"
    else
        echo "⚠️  Database migration failed. Please check your database configuration."
    fi
else
    echo "⚠️  Database connection failed. Please check your DATABASE_URL in .env"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Edit .env file with your database configuration"
echo "   2. Run: bun run test (to verify setup)"
echo "   3. Run: bun run crawl (to start crawling)"
echo ""
echo "📖 Available commands:"
echo "   bun run crawl          - Start crawling"
echo "   bun run crawl --help   - Show crawling options"
echo "   bun run stats          - Show database statistics"
echo "   bun run search <term>  - Search companies"
echo "   bun run test           - Test system"
echo ""
