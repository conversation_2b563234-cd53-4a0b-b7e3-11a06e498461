import { NextRequest, NextResponse } from 'next/server'
import { sql, count, isNotNull, ne, and } from 'drizzle-orm'
import { db } from '@/lib/db'
import {
  products,
  companies,
  categories,
  productCategories,
  companyCategories,
} from '@/lib/db/schema'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const type = searchParams.get('type') || 'all' // 'products', 'companies', or 'all'

  try {
    // Get all categories from the categories table with counts
    const allCategories = await db
      .select({
        id: categories.id,
        categoryName: categories.categoryName,
        createdAt: categories.createdAt,
      })
      .from(categories)
      .orderBy(categories.categoryName)

    // Get product counts for each category
    let productCounts: { [key: string]: number } = {}
    let companyCounts: { [key: string]: number } = {}

    if (type === 'products' || type === 'all') {
      const productCategoryStats = await db
        .select({
          categoryId: productCategories.categoryId,
          count: count(),
        })
        .from(productCategories)
        .groupBy(productCategories.categoryId)

      productCounts = Object.fromEntries(
        productCategoryStats.map(stat => [stat.categoryId, Number(stat.count)])
      )
    }

    if (type === 'companies' || type === 'all') {
      const companyCategoryStats = await db
        .select({
          categoryId: companyCategories.categoryId,
          count: count(),
        })
        .from(companyCategories)
        .groupBy(companyCategories.categoryId)

      companyCounts = Object.fromEntries(
        companyCategoryStats.map(stat => [stat.categoryId, Number(stat.count)])
      )
    }

    // Combine counts and create filter options
    const filterOptions = allCategories
      .map(category => {
        const productCount = productCounts[category.id] || 0
        const companyCount = companyCounts[category.id] || 0
        const totalCount = productCount + companyCount

        return {
          value: category.categoryName,
          label: category.categoryName,
          count: totalCount,
          type: 'category' as const,
          id: category.id,
          productCount,
          companyCount,
        }
      })
      .filter(option => option.count > 0) // Only show categories that have items
      .sort((a, b) => b.count - a.count || a.label.localeCompare(b.label))

    // Legacy format for backward compatibility
    const categoriesWithCount = filterOptions.map(option => ({
      category: option.label,
      count: option.count,
    }))

    return NextResponse.json({
      categories: filterOptions.map(option => option.label),
      subcategories: [], // No longer using subcategories from old system
      categoriesWithCount,
      subcategoriesWithCount: [],
      filterOptions,
      total: {
        categories: filterOptions.length,
        subcategories: 0,
        totalOptions: filterOptions.length,
      },
    })
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to fetch categories',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
