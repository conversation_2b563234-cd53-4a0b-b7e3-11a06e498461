'use client'

export const runtime = 'edge'

import { Arrow<PERSON>ef<PERSON>, Copy } from 'lucide-react'
import { useParams } from 'next/navigation'
import type React from 'react'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ModelSelect } from '@/components/ui/model-select'
import { ProviderSelect } from '@/components/ui/provider-select'
import { Textarea } from '@/components/ui/textarea'
import { useAdminAuthGuard } from '@/hooks/useAuthGuard'
import { Link, useRouter } from '@/i18n/navigation'
import { useBotsStore } from '@/stores/bots'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function CloneBotPage() {
  const router = useRouter()
  const params = useParams()
  const botId = params.id ? Number.parseInt(params.id as string, 10) : null

  const {
    currentBot,
    fetchBotById,
    createBot,
    isLoading,
    error,
    clearError,
    setCurrentBot,
  } = useBotsStore()

  // Auth guard
  useAdminAuthGuard()

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    provider: 'openai',
    model: 'gpt-4.1',
    temperature: 0.5,
    isDefault: false,
    systemPrompt: '',
  })

  const [isInitialized, setIsInitialized] = useState(false)
  const [isFetching, setIsFetching] = useState(true)

  // Clear current bot and reset form when bot ID changes
  useEffect(() => {
    setCurrentBot(null)
    setIsInitialized(false)
    setFormData({
      name: '',
      slug: '',
      provider: 'openai',
      model: 'gpt-4.1',
      temperature: 0.5,
      isDefault: false,
      systemPrompt: '',
    })
  }, [botId, setCurrentBot])

  // Fetch bot data on mount
  useEffect(() => {
    if (botId && !Number.isNaN(botId)) {
      fetchBotById(botId).then(() => {
        setIsFetching(false)
      })
    } else {
      setIsFetching(false)
    }
  }, [botId, fetchBotById])

  // Update form data when bot is loaded (for cloning, add "_copy" suffix)
  useEffect(() => {
    if (currentBot && !isInitialized) {
      setFormData({
        name: `${currentBot.name}_copy` || '',
        slug: `${currentBot.slug}_copy` || '',
        provider: currentBot.provider || 'openai',
        model: currentBot.model || 'gpt-4.1',
        temperature: currentBot.temperature || 0.5,
        isDefault: false, // Don't clone the default status
        systemPrompt: currentBot.systemPrompt || '',
      })
      setIsInitialized(true)
    }
  }, [currentBot, isInitialized])

  const handleInputChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const value =
        e.target.type === 'checkbox'
          ? (e.target as HTMLInputElement).checked
          : e.target.value
      setFormData(prev => ({
        ...prev,
        [field]:
          field === 'temperature' ? Number.parseFloat(value as string) : value,
      }))

      // Auto-generate slug from name
      if (field === 'name') {
        const slug = e.target.value
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '')
        setFormData(prev => ({ ...prev, slug }))
      }

      // Clear error when user starts typing
      if (error) {
        clearError()
      }
    }

  const handleSelectChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }))
    }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (
      !formData.name ||
      !formData.slug ||
      !formData.provider ||
      !formData.model
    ) {
      return
    }

    const success = await createBot(formData)

    if (success) {
      router.push('/admin/bots')
    }
  }

  const isFormValid =
    formData.name && formData.slug && formData.provider && formData.model

  if (isFetching) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-gray-500">Loading bot...</p>
        </div>
      </div>
    )
  }

  if (!currentBot && !isFetching) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/bots">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Bots
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Bot Not Found</h1>
            <p className="text-gray-600">
              The requested bot could not be found
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Link href="/admin/bots">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bots
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Clone Bot</h1>
          <p className="text-gray-600">
            Create a new bot based on "{currentBot?.name}"
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ×
            </Button>
          </div>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Bot Configuration</CardTitle>
          <CardDescription>
            Configure the bot settings and AI model parameters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Bot Name *</Label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  placeholder="Enter bot name"
                  disabled={isLoading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug *</Label>
                <Input
                  type="text"
                  value={formData.slug}
                  onChange={handleInputChange('slug')}
                  placeholder="bot-slug"
                  disabled={isLoading}
                  required
                />
                <p className="text-sm text-gray-500">
                  Auto-generated from name, but can be customized
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="provider">AI Provider *</Label>
                <ProviderSelect
                  value={formData.provider}
                  onValueChange={handleSelectChange('provider')}
                  placeholder="Select provider"
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model *</Label>
                <ModelSelect
                  value={formData.model}
                  onValueChange={handleSelectChange('model')}
                  placeholder="Select model"
                  disabled={isLoading}
                  provider={formData.provider}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="temperature">
                  Temperature ({formData.temperature})
                </Label>
                <Input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.temperature}
                  onChange={handleInputChange('temperature')}
                  disabled={isLoading}
                />
                <p className="text-sm text-gray-500">
                  Controls randomness: 0.0 = deterministic, 1.0 = very creative
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isDefault}
                  onChange={handleInputChange('isDefault')}
                  disabled={isLoading}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isDefault">Set as default bot</Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                value={formData.systemPrompt}
                onChange={handleInputChange('systemPrompt')}
                placeholder="Enter system prompt to define the bot's behavior and personality..."
                disabled={isLoading}
                rows={6}
              />
              <p className="text-sm text-gray-500">
                Optional: Define how the bot should behave and respond
              </p>
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <Button type="submit" disabled={isLoading || !isFormValid}>
                <Copy className="mr-2 h-4 w-4" />
                {isLoading ? 'Cloning...' : 'Clone Bot'}
              </Button>
              <Link href="/admin/bots">
                <Button type="button" variant="outline" disabled={isLoading}>
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
