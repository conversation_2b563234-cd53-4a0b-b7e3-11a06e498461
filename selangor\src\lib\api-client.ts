/**
 * Standardized API client for direct fetch/axios calls
 * Use this for server components or when not using the centralized api.ts service
 */

/**
 * Standardized API response format
 */
export interface StandardApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * Process API response to ensure it follows the standardized format
 */
export function processApiResponse<T>(response: any): StandardApiResponse<T> {
  // If response already follows the standard format, return it
  if (typeof response.success === 'boolean') {
    return response as StandardApiResponse<T>
  }

  // If response has an error field, it's an error response
  if (response.error) {
    return {
      success: false,
      error: response.error,
      message: response.message,
    }
  }

  // Otherwise, wrap the response in the standard format
  return {
    success: true,
    data: response,
  }
}

/**
 * Extract data from a standardized API response
 * This is useful for components that expect direct data access
 */
export function extractApiData<T>(response: StandardApiResponse<T>): T | null {
  if (!response.success || !response.data) {
    return null
  }
  return response.data
}

/**
 * Fetch API with standardized response handling
 */
export async function fetchApi<T = any>(
  url: string,
  options?: RequestInit
): Promise<StandardApiResponse<T>> {
  try {
    const response = await fetch(url, options)

    // Handle non-JSON responses
    if (response.status === 204) {
      return { success: true }
    }

    const data = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: data.error || `HTTP error! status: ${response.status}`,
        message: data.message,
      }
    }

    return processApiResponse<T>(data)
  } catch (error) {
    console.error(`API request failed for ${url}:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

/**
 * Helper function to safely extract data from API response
 * with proper error handling for server components
 */
export async function fetchApiData<T = any>(
  url: string,
  options?: RequestInit,
  defaultValue: T | null = null
): Promise<T | null> {
  const response = await fetchApi<T>(url, options)

  if (!response.success) {
    console.error(`API error for ${url}:`, response.error)
    return defaultValue
  }

  return response.data || defaultValue
}
