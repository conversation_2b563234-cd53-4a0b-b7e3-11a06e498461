# OpenAI API Configuration
OPENAI_API_KEY=

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/halal_chat

# Server Configuration
PORT=16001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:16000

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DIR=uploads

# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=
WHATSAPP_WEBHOOK_VERIFY_TOKEN=
WHATSAPP_BUSINESS_ACCOUNT_ID=

# Twilio WhatsApp Configuration
# Get these from Twilio Console: https://console.twilio.com/
TWILIO_ACCOUNT_SID=                    # Account SID (starts with AC...)
TWILIO_AUTH_TOKEN=                     # Auth Token (keep secure)
TWILIO_PHONE_NUMBER=                   # WhatsApp number (e.g., whatsapp:+***********)
TWILIO_WEBHOOK=                        # Optional: webhook URL for reference

# Admin Authentication
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=365d
ADMIN_DEFAULT_PASSWORD=admin123

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com

# S3 Configuration (Optional - can be managed via admin dashboard)
# DEFAULT_S3_SERVICE_NAME=AWS S3
# S3_ACCESS_KEY_ID=
# S3_SECRET_ACCESS_KEY=
# S3_BUCKET_NAME=
# S3_REGION=us-east-1
# S3_ENDPOINT_URL=  # Leave empty for AWS S3, set for Cloudflare R2/DigitalOcean Spaces
