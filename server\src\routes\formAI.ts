import { Hono } from 'hono'
import { cors } from 'hono/cors'
import formAIService from '../services/formAI'
import type { FormFillRequest } from '../services/formAI'
import { logger } from '../utils/logger'

const formAIRouter = new Hono()

// CORS configuration for the embedable JS
formAIRouter.use(
  '/*',
  cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
  }),
)

/**
 * Analyze a form's structure
 * POST /api/form-ai/analyze
 */
formAIRouter.post('/analyze', async (c) => {
  try {
    const body = await c.req.json()
    const { formHTML } = body

    if (!formHTML) {
      return c.json({ error: 'Form HTML is required' }, 400)
    }

    const analysis = formAIService.analyzeForm(formHTML)

    return c.json({
      success: true,
      analysis,
    })
  } catch (error) {
    logger.error('Error analyzing form:', error)
    return c.json(
      {
        error: 'Failed to analyze form',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
})

/**
 * Fill a form using AI based on user request
 * POST /api/form-ai/fill
 */
formAIRouter.post('/fill', async (c) => {
  try {
    const body: FormFillRequest = await c.req.json()
    const { formHTML, userRequest, context } = body

    if (!formHTML) {
      return c.json({ error: 'Form HTML is required' }, 400)
    }

    if (!userRequest) {
      return c.json({ error: 'User request is required' }, 400)
    }

    const result = await formAIService.fillForm({
      formHTML,
      userRequest,
      context,
    })

    return c.json({
      success: true,
      result,
    })
  } catch (error) {
    logger.error('Error filling form:', error)
    return c.json(
      {
        error: 'Failed to fill form',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
})

/**
 * Get the embedable JS client
 * GET /api/form-ai/client.js
 */
formAIRouter.get('/client.js', async (c) => {
  const clientJS = `
(function() {
  'use strict';
  
  // Configuration
  const CONFIG = {
    apiBaseUrl: '${process.env.API_BASE_URL || 'http://localhost:3000'}/api/form-ai',
    enableLogs: true
  };
  
  // Utility functions
  function log(...args) {
    if (CONFIG.enableLogs) {
      console.log('[FormAI]', ...args);
    }
  }
  
  function error(...args) {
    console.error('[FormAI]', ...args);
  }
  
  // Main FormAI class
  class FormAI {
    constructor(options = {}) {
      this.apiBaseUrl = options.apiBaseUrl || CONFIG.apiBaseUrl;
      this.currentForm = null;
      this.isProcessing = false;
      
      this.init();
    }
    
    init() {
      this.createUI();
      this.attachEventListeners();
      log('FormAI initialized');
    }
    
    createUI() {
      // Create floating button
      const button = document.createElement('div');
      button.id = 'form-ai-button';
      button.innerHTML = '🤖';
      button.style.cssText = \`
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        background: #007bff;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        transition: all 0.3s ease;
      \`;
      
      button.addEventListener('mouseenter', () => {
        button.style.transform = 'scale(1.1)';
      });
      
      button.addEventListener('mouseleave', () => {
        button.style.transform = 'scale(1)';
      });
      
      // Create chat interface
      const chatInterface = document.createElement('div');
      chatInterface.id = 'form-ai-chat';
      chatInterface.style.cssText = \`
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 350px;
        height: 400px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        display: none;
        flex-direction: column;
        z-index: 10001;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      \`;
      
      chatInterface.innerHTML = \`
        <div style="padding: 15px; border-bottom: 1px solid #eee; background: #007bff; color: white; border-radius: 10px 10px 0 0;">
          <h3 style="margin: 0; font-size: 16px;">Form AI Assistant</h3>
          <button id="form-ai-close" style="position: absolute; top: 10px; right: 10px; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
        </div>
        <div id="form-ai-messages" style="flex: 1; padding: 15px; overflow-y: auto; font-size: 14px;"></div>
        <div style="padding: 15px; border-top: 1px solid #eee;">
          <input type="text" id="form-ai-input" placeholder="Tell me how to fill this form..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
          <button id="form-ai-send" style="width: 100%; margin-top: 10px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">Send</button>
        </div>
      \`;
      
      document.body.appendChild(button);
      document.body.appendChild(chatInterface);
      
      this.button = button;
      this.chatInterface = chatInterface;
      this.messagesContainer = document.getElementById('form-ai-messages');
      this.input = document.getElementById('form-ai-input');
    }
    
    attachEventListeners() {
      // Toggle chat interface
      this.button.addEventListener('click', () => {
        this.toggleChat();
      });
      
      // Close chat
      document.getElementById('form-ai-close').addEventListener('click', () => {
        this.closeChat();
      });
      
      // Send message
      document.getElementById('form-ai-send').addEventListener('click', () => {
        this.sendMessage();
      });
      
      this.input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.sendMessage();
        }
      });
      
      // Detect form focus
      document.addEventListener('focusin', (e) => {
        if (e.target.form && e.target.form !== this.currentForm) {
          this.currentForm = e.target.form;
          this.showFormDetected();
        }
      });
    }
    
    toggleChat() {
      const isVisible = this.chatInterface.style.display === 'flex';
      this.chatInterface.style.display = isVisible ? 'none' : 'flex';
      
      if (!isVisible) {
        this.addMessage('Hello! I can help you fill out forms. Just tell me what you want to do.', 'ai');
        this.input.focus();
      }
    }
    
    closeChat() {
      this.chatInterface.style.display = 'none';
    }
    
    showFormDetected() {
      this.addMessage('I detected a form on this page. How would you like me to help fill it?', 'ai');
      if (this.chatInterface.style.display === 'none') {
        this.button.style.background = '#28a745';
        setTimeout(() => {
          this.button.style.background = '#007bff';
        }, 2000);
      }
    }
    
    addMessage(text, sender = 'user') {
      const messageDiv = document.createElement('div');
      messageDiv.style.cssText = \`
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 10px;
        max-width: 80%;
        \${sender === 'ai' ? 'background: #f8f9fa; margin-right: auto;' : 'background: #007bff; color: white; margin-left: auto;'}
      \`;
      messageDiv.textContent = text;
      
      this.messagesContainer.appendChild(messageDiv);
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
    
    async sendMessage() {
      const message = this.input.value.trim();
      if (!message || this.isProcessing) return;
      
      this.addMessage(message, 'user');
      this.input.value = '';
      this.isProcessing = true;
      
      this.addMessage('Processing...', 'ai');
      
      try {
        await this.processUserRequest(message);
      } catch (error) {
        error('Error processing request:', error);
        this.addMessage('Sorry, I encountered an error. Please try again.', 'ai');
      } finally {
        this.isProcessing = false;
        // Remove "Processing..." message
        const messages = this.messagesContainer.children;
        if (messages[messages.length - 1].textContent === 'Processing...') {
          this.messagesContainer.removeChild(messages[messages.length - 1]);
        }
      }
    }
    
    async processUserRequest(request) {
      if (!this.currentForm) {
        this.addMessage('Please click on a form field first so I can detect which form to fill.', 'ai');
        return;
      }
      
      const formHTML = this.currentForm.outerHTML;
      
      try {
        const response = await fetch(\`\${this.apiBaseUrl}/fill\`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            formHTML,
            userRequest: request,
            context: document.title + ' - ' + window.location.href
          })
        });
        
        const data = await response.json();
        
        if (data.success && data.result.success) {
          await this.fillFormFields(data.result);
          this.addMessage(\`Form filled successfully! \${data.result.reasoning}\`, 'ai');
        } else {
          this.addMessage('Sorry, I couldn\'t fill the form. ' + (data.result?.reasoning || 'Unknown error'), 'ai');
        }
        
      } catch (error) {
        error('API request failed:', error);
        this.addMessage('Failed to connect to the AI service. Please try again.', 'ai');
      }
    }
    
    async fillFormFields(result) {
      const actions = result.actions || [];
      
      for (const action of actions) {
        try {
          const element = document.querySelector(action.selector);
          if (!element) continue;
          
          switch (action.type) {
            case 'fill':
              if (element.type === 'textarea' || element.tagName === 'TEXTAREA') {
                element.value = action.value;
              } else {
                element.value = action.value;
              }
              element.dispatchEvent(new Event('input', { bubbles: true }));
              element.dispatchEvent(new Event('change', { bubbles: true }));
              break;
              
            case 'select':
              if (element.tagName === 'SELECT') {
                const option = Array.from(element.options).find(opt => 
                  opt.text.toLowerCase().includes(action.value.toLowerCase()) ||
                  opt.value.toLowerCase().includes(action.value.toLowerCase())
                );
                if (option) {
                  element.selectedIndex = option.index;
                  element.dispatchEvent(new Event('change', { bubbles: true }));
                }
              }
              break;
              
            case 'click':
              if (element.type === 'checkbox' || element.type === 'radio') {
                element.checked = true;
                element.dispatchEvent(new Event('change', { bubbles: true }));
              }
              break;
          }
          
          // Add visual feedback
          element.style.background = '#d4edda';
          setTimeout(() => {
            element.style.background = '';
          }, 1000);
          
        } catch (error) {
          error('Error filling field:', action.selector, error);
        }
      }
    }
  }
  
  // Auto-initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      window.FormAI = new FormAI();
    });
  } else {
    window.FormAI = new FormAI();
  }
  
  // Expose FormAI globally
  window.FormAI = window.FormAI || FormAI;
  
})();
`

  c.header('Content-Type', 'application/javascript')
  c.header('Cache-Control', 'no-cache')
  return c.text(clientJS)
})

/**
 * Health check endpoint
 */
formAIRouter.get('/health', async (c) => {
  return c.json({
    status: 'ok',
    service: 'form-ai',
    timestamp: new Date().toISOString(),
  })
})

export default formAIRouter
