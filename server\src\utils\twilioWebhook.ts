import twilio from 'twilio'

/**
 * Twilio Webhook Utility Functions
 * Separated from controller for clean architecture
 */

/**
 * Verify Twilio webhook signature
 */
export function verifyTwilioWebhook(
  authToken: string,
  twilioSignature: string,
  requestUrl: string,
  body: Record<string, string>,
): boolean {
  try {
    console.log(
      `TwilioWebhook: Verifying webhook. URL: ${requestUrl}, Body: ${JSON.stringify(body)}, Signature: ${twilioSignature}`,
    )

    const isValid = twilio.validateRequest(
      authToken,
      twilioSignature,
      requestUrl,
      body,
    )

    if (!isValid) {
      console.warn(
        'TwilioWebhook: Webhook verification failed. Signature mismatch.',
      )
    } else {
      console.log('TwilioWebhook: Webhook verified successfully.')
    }

    return isValid
  } catch (error) {
    console.error('TwilioWebhook: Error during webhook verification:', error)
    return false
  }
}

/**
 * Parse Twilio webhook body from URL-encoded string
 */
export function parseTwilioWebhookBody(
  rawBody: string,
): Record<string, string> {
  const bodyParams = new URLSearchParams(rawBody)
  const parsedBody: Record<string, string> = {}

  // Convert URLSearchParams to object
  bodyParams.forEach((value, key) => {
    parsedBody[key] = value
  })

  return parsedBody
}

/**
 * Extract Twilio signature from request headers
 */
export function getTwilioSignature(request: Request): string | null {
  return request.headers.get('x-twilio-signature')
}

/**
 * Validate required fields in Twilio webhook payload
 */
export function validateTwilioWebhookPayload(
  body: Record<string, string>,
): boolean {
  const requiredFields = ['MessageSid', 'From', 'To']

  for (const field of requiredFields) {
    if (!body[field]) {
      console.error(`TwilioWebhook: Missing required field: ${field}`)
      return false
    }
  }

  return true
}
