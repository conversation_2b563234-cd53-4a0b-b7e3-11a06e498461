'use client'

export const runtime = 'edge'

import { Arrow<PERSON>ef<PERSON>, BarChart3, Download, RefreshCw } from 'lucide-react'
import { useState } from 'react'
import { AgentAnalytics } from '@/components/analytics/AgentAnalytics'
import { AgentPerformanceTable } from '@/components/analytics/AgentPerformanceTable'
import { NotificationCenter } from '@/components/notifications/NotificationCenter'
import { useAdminAuthGuard } from '@/hooks/useAuthGuard'
import { Link } from '@/i18n/navigation'
import { UserRole } from '@/types/roles'

// Mock data for the performance table
const mockAgentData = [
  {
    agentId: 1,
    agentName: '<PERSON>',
    email: '<EMAIL>',
    role: 'Senior Agent',
    isOnline: true,
    totalSessions: 45,
    completedSessions: 42,
    averageResponseTime: 120,
    averageSessionDuration: 15.5,
    customerSatisfactionScore: 4.2,
    onlineHours: 160,
    handoverRate: 6.7,
    lastActive: '2024-01-15T10:30:00Z',
    trend: 'up' as const,
  },
  {
    agentId: 2,
    agentName: '<PERSON>',
    email: '<EMAIL>',
    role: 'Agent',
    isOnline: true,
    totalSessions: 38,
    completedSessions: 36,
    averageResponseTime: 95,
    averageSessionDuration: 12.3,
    customerSatisfactionScore: 4.5,
    onlineHours: 155,
    handoverRate: 5.3,
    lastActive: '2024-01-15T09:45:00Z',
    trend: 'up' as const,
  },
  {
    agentId: 3,
    agentName: 'Mike Johnson',
    email: '<EMAIL>',
    role: 'Agent',
    isOnline: false,
    totalSessions: 52,
    completedSessions: 48,
    averageResponseTime: 140,
    averageSessionDuration: 18.2,
    customerSatisfactionScore: 3.9,
    onlineHours: 170,
    handoverRate: 7.7,
    lastActive: '2024-01-14T18:20:00Z',
    trend: 'down' as const,
  },
  {
    agentId: 4,
    agentName: 'Sarah Wilson',
    email: '<EMAIL>',
    role: 'Supervisor',
    isOnline: true,
    totalSessions: 29,
    completedSessions: 28,
    averageResponseTime: 85,
    averageSessionDuration: 14.8,
    customerSatisfactionScore: 4.7,
    onlineHours: 140,
    handoverRate: 3.4,
    lastActive: '2024-01-15T11:15:00Z',
    trend: 'stable' as const,
  },
]

export default function AnalyticsPage() {
  const { user, loading } = useAdminAuthGuard([
    UserRole.ADMIN,
    UserRole.SUPERVISOR,
  ])

  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [selectedAgent, setSelectedAgent] = useState<number | undefined>()
  const [activeTab, setActiveTab] = useState<'overview' | 'detailed'>(
    'overview'
  )
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsRefreshing(false)
  }

  const handleExport = () => {
    // Implement export functionality
    console.log('Exporting analytics data...')
  }

  const handleAgentClick = (agentId: number) => {
    setSelectedAgent(selectedAgent === agentId ? undefined : agentId)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/dashboard"
                className="p-2 hover:bg-gray-100 rounded-md"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">
                  Agent Analytics
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Time Range Selector */}
              <select
                value={timeRange}
                onChange={e =>
                  setTimeRange(e.target.value as '7d' | '30d' | '90d')
                }
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>

              {/* Notification Center */}
              <NotificationCenter />

              {/* Action Buttons */}
              <button
                type="button"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
                title="Refresh data"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                />
              </button>

              <button
                type="button"
                onClick={handleExport}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                type="button"
                onClick={() => setActiveTab('overview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Overview
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('detailed')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'detailed'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Detailed View
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {activeTab === 'overview' ? (
          <div className="space-y-6">
            <AgentAnalytics timeRange={timeRange} agentId={selectedAgent} />
          </div>
        ) : (
          <div className="space-y-6">
            <AgentPerformanceTable
              data={mockAgentData}
              onAgentClick={handleAgentClick}
            />

            {selectedAgent && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Individual Agent Analytics
                </h3>
                <AgentAnalytics timeRange={timeRange} agentId={selectedAgent} />
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  )
}
