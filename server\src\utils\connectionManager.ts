import postgres from 'postgres'

/**
 * Connection Manager for postgres-js connections
 * Helps manage and cleanup database connections properly
 */
class ConnectionManager {
  private static connections = new Map<string, postgres.Sql>()
  private static cleanupInterval: NodeJS.Timeout | null = null
  private static healthCheckInterval: NodeJS.Timeout | null = null

  /**
   * Get environment-specific connection options
   */
  private static getEnvironmentOptions(): Partial<postgres.Options> {
    const isProduction = process.env.NODE_ENV === 'production'
    const isCloudflareWorkers = typeof globalThis.caches !== 'undefined'

    if (isCloudflareWorkers) {
      // Cloudflare Workers - optimized for edge runtime
      return {
        max: 3, // Lower pool size for edge
        idle_timeout: 10, // Faster cleanup for serverless
        max_lifetime: 60 * 10, // 10 minutes max lifetime
        connect_timeout: 5, // Faster timeout for edge
      }
    }
    if (isProduction) {
      // Production server - optimized for high throughput
      return {
        max: 10, // Higher pool size for production
        idle_timeout: 30, // Keep connections longer
        max_lifetime: 60 * 60, // 1 hour max lifetime
        connect_timeout: 15, // Longer timeout for production
      }
    }
    // Development - balanced settings
    return {
      max: 5, // Moderate pool size for dev
      idle_timeout: 20, // Standard cleanup
      max_lifetime: 60 * 30, // 30 minutes max lifetime
      connect_timeout: 10, // Standard timeout
    }
  }

  /**
   * Get or create a connection with proper pooling
   */
  static getConnection(
    databaseUrl: string,
    options: postgres.Options = {},
  ): postgres.Sql {
    const connectionKey = `${databaseUrl}_${JSON.stringify(options)}`

    if (!ConnectionManager.connections.has(connectionKey)) {
      const envOptions = ConnectionManager.getEnvironmentOptions()
      const defaultOptions: postgres.Options = {
        prepare: false,
        debug:
          process.env.NODE_ENV === 'development' &&
          process.env.DB_DEBUG === 'true',
        // Connection retry configuration
        connection: {
          application_name: 'halal-chat-server',
        },
        // Transform configuration for better error handling
        transform: {
          undefined: null, // Convert undefined to null for PostgreSQL
        },
        // Apply environment-specific options
        ...envOptions,
        // User options override defaults
        ...options,
      }

      const connection = postgres(databaseUrl, defaultOptions)
      ConnectionManager.connections.set(connectionKey, connection)

      // Start monitoring intervals if not already running
      ConnectionManager.startCleanupInterval()
      ConnectionManager.startHealthCheckInterval()
    }

    return ConnectionManager.connections.get(connectionKey)!
  }

  /**
   * Close a specific connection
   */
  static async closeConnection(
    databaseUrl: string,
    options: postgres.Options = {},
  ): Promise<void> {
    const connectionKey = `${databaseUrl}_${JSON.stringify(options)}`
    const connection = ConnectionManager.connections.get(connectionKey)

    if (connection) {
      try {
        await connection.end()
        ConnectionManager.connections.delete(connectionKey)
      } catch (error) {
        console.error('Error closing connection:', error)
      }
    }
  }

  /**
   * Close all connections
   */
  static async closeAllConnections(): Promise<void> {
    const closePromises = Array.from(
      ConnectionManager.connections.values(),
    ).map(async (connection) => {
      try {
        await connection.end()
      } catch (error) {
        console.error('Error closing connection:', error)
      }
    })

    await Promise.all(closePromises)
    ConnectionManager.connections.clear()

    // Clear all intervals
    if (ConnectionManager.cleanupInterval) {
      clearInterval(ConnectionManager.cleanupInterval)
      ConnectionManager.cleanupInterval = null
    }

    if (ConnectionManager.healthCheckInterval) {
      clearInterval(ConnectionManager.healthCheckInterval)
      ConnectionManager.healthCheckInterval = null
    }
  }

  /**
   * Start periodic cleanup of idle connections
   */
  private static startCleanupInterval(): void {
    if (ConnectionManager.cleanupInterval) {
      return
    }

    ConnectionManager.cleanupInterval = setInterval(() => {
      // Log connection count for monitoring
      console.log(
        `Active database connections: ${ConnectionManager.connections.size}`,
      )
    }, 60000) // Check every minute
  }

  /**
   * Start periodic health checks for connections
   */
  private static startHealthCheckInterval(): void {
    if (ConnectionManager.healthCheckInterval) {
      return
    }

    // Only run health checks in production
    if (process.env.NODE_ENV !== 'production') {
      return
    }

    ConnectionManager.healthCheckInterval = setInterval(
      async () => {
        const connections = Array.from(ConnectionManager.connections.entries())

        for (const [key, connection] of connections) {
          try {
            // Simple health check query
            await connection`SELECT 1`
          } catch (error) {
            console.warn(`Health check failed for connection ${key}:`, error)
            // Remove unhealthy connection
            try {
              await connection.end()
            } catch (endError) {
              console.error('Error closing unhealthy connection:', endError)
            }
            ConnectionManager.connections.delete(key)
          }
        }
      },
      5 * 60 * 1000,
    ) // Check every 5 minutes
  }

  /**
   * Get connection count for monitoring
   */
  static getConnectionCount(): number {
    return ConnectionManager.connections.size
  }

  /**
   * Get connection health status
   */
  static async getConnectionHealth(): Promise<{
    total: number
    healthy: number
    unhealthy: number
  }> {
    const connections = Array.from(ConnectionManager.connections.values())
    let healthy = 0
    let unhealthy = 0

    await Promise.all(
      connections.map(async (connection) => {
        try {
          await connection`SELECT 1`
          healthy++
        } catch {
          unhealthy++
        }
      }),
    )

    return {
      total: connections.length,
      healthy,
      unhealthy,
    }
  }
}

// Graceful shutdown handling
// process.on('SIGTERM', async () => {
//   console.log('SIGTERM received, closing database connections...');
//   await ConnectionManager.closeAllConnections();
// });

// process.on('SIGINT', async () => {
//   console.log('SIGINT received, closing database connections...');
//   await ConnectionManager.closeAllConnections();
// });

// process.on('beforeExit', async () => {
//   console.log('Process exiting, closing database connections...');
//   await ConnectionManager.closeAllConnections();
// });

export default ConnectionManager
