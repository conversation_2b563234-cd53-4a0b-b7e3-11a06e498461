'use client'

import { <PERSON><PERSON><PERSON><PERSON>, Save } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/admin/AdminLayout'
import {
  Button,
  Input,
  Select,
  type SelectOption,
  Textarea,
} from '@/components/ui'

const statusOptions: SelectOption[] = [
  { value: 'Active', label: 'Active' },
  { value: 'Inactive', label: 'Inactive' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Expired', label: 'Expired' },
]

const certificateTypeOptions: SelectOption[] = [
  { value: 'HALAL', label: 'HALAL' },
  { value: 'HALAL JAKIM', label: 'HALAL JAKIM' },
  { value: 'HALAL SELANGOR', label: 'HALAL SELANGOR' },
]

const categoryOptions: SelectOption[] = [
  { value: 'Food', label: 'Food' },
  { value: 'Beverage', label: 'Beverage' },
  { value: 'Cosmetics', label: 'Cosmetics' },
  { value: 'Pharmaceutical', label: 'Pharmaceutical' },
  { value: 'Others', label: 'Others' },
]

const stateOptions: SelectOption[] = [
  { value: 'Selangor', label: 'Selangor' },
  { value: 'Kuala Lumpur', label: 'Kuala Lumpur' },
  { value: 'Johor', label: 'Johor' },
  { value: 'Penang', label: 'Penang' },
  { value: 'Perak', label: 'Perak' },
  { value: 'Kedah', label: 'Kedah' },
  { value: 'Kelantan', label: 'Kelantan' },
  { value: 'Terengganu', label: 'Terengganu' },
  { value: 'Pahang', label: 'Pahang' },
  { value: 'Negeri Sembilan', label: 'Negeri Sembilan' },
  { value: 'Melaka', label: 'Melaka' },
  { value: 'Perlis', label: 'Perlis' },
  { value: 'Sabah', label: 'Sabah' },
  { value: 'Sarawak', label: 'Sarawak' },
]

interface EditProductPageProps {
  params: Promise<{ id: string }>
}

export default function EditProductPage({ params }: EditProductPageProps) {
  const router = useRouter()
  const [productId, setProductId] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingProduct, setIsLoadingProduct] = useState(true)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    productName: '',
    companyName: '',
    certificateNumber: '',
    certificateType: '',
    issuedDate: '',
    expiryDate: '',
    status: '',
    category: '',
    subcategory: '',
    address: '',
    state: '',
    country: 'Malaysia',
    contactInfo: '',
    website: '',
    sourceUrl: '',
  })

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setProductId(resolvedParams.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    if (productId) {
      fetchProduct()
    }
  }, [productId])

  const fetchProduct = async () => {
    setIsLoadingProduct(true)
    try {
      const response = await fetch(`/api/products/${productId}`)
      if (response.ok) {
        const data = await response.json()
        const product = data.product

        setFormData({
          productName: product.productName || '',
          companyName: product.companyName || '',
          certificateNumber: product.certificateNumber || '',
          certificateType: product.certificateType || '',
          issuedDate: product.issuedDate || '',
          expiryDate: product.expiryDate || '',
          status: product.status || '',
          category: product.category || '',
          subcategory: product.subcategory || '',
          address: product.address || '',
          state: product.state || '',
          country: product.country || 'Malaysia',
          contactInfo: product.contactInfo || '',
          website: product.website || '',
          sourceUrl: product.sourceUrl || '',
        })
      } else {
        console.error('Failed to fetch product')
        router.push('/cp1/products')
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      router.push('/cp1/products')
    } finally {
      setIsLoadingProduct(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.productName.trim()) {
      newErrors.productName = 'Product name is required'
    }

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/products', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: Number.parseInt(productId), ...formData }),
      })

      if (response.ok) {
        router.push('/cp1/products')
      } else {
        const errorData = await response.json()
        console.error('Failed to update product:', errorData)
      }
    } catch (error) {
      console.error('Error updating product:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingProduct) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading product...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/cp1/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Product</h1>
            <p className="text-gray-600">Update product information</p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Basic Information
                </h3>

                <Input
                  label="Product Name *"
                  value={formData.productName}
                  onChange={e =>
                    handleInputChange('productName', e.target.value)
                  }
                  error={errors.productName}
                  placeholder="Enter product name"
                />

                <Input
                  label="Company Name *"
                  value={formData.companyName}
                  onChange={e =>
                    handleInputChange('companyName', e.target.value)
                  }
                  error={errors.companyName}
                  placeholder="Enter company name"
                />

                <Select
                  label="Category"
                  value={formData.category}
                  onChange={e => handleInputChange('category', e.target.value)}
                  options={categoryOptions}
                  placeholder="Select category"
                />

                <Input
                  label="Subcategory"
                  value={formData.subcategory}
                  onChange={e =>
                    handleInputChange('subcategory', e.target.value)
                  }
                  placeholder="Enter subcategory"
                />
              </div>

              {/* Certificate Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Certificate Information
                </h3>

                <Input
                  label="Certificate Number"
                  value={formData.certificateNumber}
                  onChange={e =>
                    handleInputChange('certificateNumber', e.target.value)
                  }
                  placeholder="Enter certificate number"
                />

                <Select
                  label="Certificate Type"
                  value={formData.certificateType}
                  onChange={e =>
                    handleInputChange('certificateType', e.target.value)
                  }
                  options={certificateTypeOptions}
                  placeholder="Select certificate type"
                />

                <Input
                  label="Issued Date"
                  type="date"
                  value={formData.issuedDate}
                  onChange={e =>
                    handleInputChange('issuedDate', e.target.value)
                  }
                />

                <Input
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={e =>
                    handleInputChange('expiryDate', e.target.value)
                  }
                />

                <Select
                  label="Status"
                  value={formData.status}
                  onChange={e => handleInputChange('status', e.target.value)}
                  options={statusOptions}
                  placeholder="Select status"
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link href="/cp1/products">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button type="submit" isLoading={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                Update Product
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  )
}
