'use client'

import { ChevronRight, Home } from 'lucide-react'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'

export interface BreadcrumbItem {
  label: string
  labelBM: string
  href?: string
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
  showHome?: boolean
}

export function Breadcrumb({
  items,
  className,
  showHome = true,
}: BreadcrumbProps) {
  const { language } = useLanguage()

  const allItems = showHome
    ? [
        {
          label: 'Home',
          labelBM: 'Laman <PERSON>',
          href: '/',
        },
        ...items,
      ]
    : items

  return (
    <nav
      className={cn('flex items-center space-x-2 text-sm', className)}
      aria-label={
        language === 'en' ? 'Breadcrumb navigation' : 'Navigasi breadcrumb'
      }
    >
      <ol className="flex items-center space-x-2">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1
          const label = language === 'bm' ? item.labelBM : item.label

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="w-4 h-4 text-gray-400 mx-2" />
              )}

              {index === 0 && showHome ? (
                // Home icon for first item
                <Link
                  href={item.href || '/'}
                  className="flex items-center text-gray-500 hover:text-primary-green transition-colors"
                >
                  <Home className="w-4 h-4" />
                  <span className="sr-only">{label}</span>
                </Link>
              ) : isLast ? (
                // Current page (no link)
                <span className="text-gray-900 font-medium" aria-current="page">
                  {label}
                </span>
              ) : (
                // Intermediate pages (with links)
                <Link
                  href={item.href || '#'}
                  className="text-gray-500 hover:text-primary-green transition-colors"
                >
                  {label}
                </Link>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}

// Compact breadcrumb for mobile
export function MobileBreadcrumb({ items, className }: BreadcrumbProps) {
  const { language } = useLanguage()

  if (items.length === 0) {
    return null
  }

  const currentPage = items[items.length - 1]
  const parentPage = items.length > 1 ? items[items.length - 2] : null

  return (
    <nav className={cn('flex items-center text-sm md:hidden', className)}>
      {parentPage && (
        <>
          <Link
            href={parentPage.href || '#'}
            className="text-gray-500 hover:text-primary-green transition-colors"
          >
            {language === 'bm' ? parentPage.labelBM : parentPage.label}
          </Link>
          <ChevronRight className="w-4 h-4 text-gray-400 mx-2" />
        </>
      )}
      <span className="text-gray-900 font-medium">
        {language === 'bm' ? currentPage.labelBM : currentPage.label}
      </span>
    </nav>
  )
}

// Auto-generating breadcrumb based on pathname
export function AutoBreadcrumb({
  pathname,
  className,
  customLabels = {},
}: {
  pathname: string
  className?: string
  customLabels?: Record<string, { en: string; bm: string }>
}) {
  const { language } = useLanguage()

  const segments = pathname.split('/').filter(Boolean)

  const items: BreadcrumbItem[] = segments.map((segment, index) => {
    const href = `/${segments.slice(0, index + 1).join('/')}`
    const customLabel = customLabels[segment]

    return {
      label:
        customLabel?.en ||
        segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
      labelBM:
        customLabel?.bm ||
        segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
      href: index < segments.length - 1 ? href : undefined,
    }
  })

  if (items.length === 0) {
    return null
  }

  return <Breadcrumb items={items} className={className} />
}

// Structured data for SEO
export function BreadcrumbStructuredData({
  items,
}: {
  items: BreadcrumbItem[]
}) {
  const { language } = useLanguage()

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: language === 'en' ? 'Home' : 'Laman Utama',
        item: typeof window !== 'undefined' ? window.location.origin : '',
      },
      ...items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 2,
        name: language === 'bm' ? item.labelBM : item.label,
        item: item.href
          ? typeof window !== 'undefined'
            ? window.location.origin + item.href
            : item.href
          : undefined,
      })),
    ],
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
