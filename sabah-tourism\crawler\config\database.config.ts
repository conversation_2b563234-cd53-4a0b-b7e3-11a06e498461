import path from "path";
import type { DatabaseConfig } from "../src/types/database.js";

export const defaultDatabaseConfig: DatabaseConfig = {
  path:
    process.env.DATABASE_PATH || path.join(process.cwd(), "data", "crawler.db"),
  enableWAL: true,
  timeout: 5000,
  verbose: process.env.NODE_ENV === "development",
};

export const testDatabaseConfig: DatabaseConfig = {
  path: ":memory:", // In-memory database for testing
  enableWAL: false,
  timeout: 1000,
  verbose: false,
};

export function createDatabaseConfig(
  overrides?: Partial<DatabaseConfig>,
): DatabaseConfig {
  return {
    ...defaultDatabaseConfig,
    ...overrides,
  };
}

export function getTestDatabaseConfig(): DatabaseConfig {
  return { ...testDatabaseConfig };
}
