import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
} from 'vitest'
import { KafkaClient } from './kafka-client'

describe('Kafka Client Tests', () => {
  let client: KafkaClient
  const testTopic = 'test-topic-' + Date.now()
  const testMessage = {
    id: 1,
    message: 'Test message',
    timestamp: new Date().toISOString(),
    type: 'test',
  }

  beforeAll(async () => {
    client = new KafkaClient({
      clientId: 'test-client',
      brokers: ['127.0.0.1:19092'],
    })

    // Wait for RedPanda to be ready
    await new Promise((resolve) => setTimeout(resolve, 2000))
  })

  afterAll(async () => {
    try {
      await client.disconnect()
    } catch (error) {
      console.warn('Error during cleanup:', error)
    }
  })

  beforeEach(async () => {
    // Small delay between tests
    await new Promise((resolve) => setTimeout(resolve, 500))
  })

  describe('Topic Management', () => {
    it('should create a topic successfully', async () => {
      await client.admin.connect()
      await expect(client.createTopic(testTopic)).resolves.toBeUndefined()
      await client.admin.disconnect()
    })

    it('should list topics including the created one', async () => {
      await client.admin.connect()
      await client.createTopic(testTopic)
      const topics = await client.listTopics()
      expect(topics).toContain(testTopic)
      await client.admin.disconnect()
    })

    it('should handle creating existing topic gracefully', async () => {
      await client.admin.connect()
      await client.createTopic(testTopic)
      // Should not throw when topic already exists
      await expect(client.createTopic(testTopic)).resolves.toBeUndefined()
      await client.admin.disconnect()
    })
  })

  describe('Message Publishing', () => {
    beforeEach(async () => {
      await client.connectProducer()
      await client.admin.connect()
      await client.createTopic(testTopic)
      await client.admin.disconnect()
    })

    afterEach(async () => {
      await client.disconnectProducer()
    })

    it('should publish a message successfully', async () => {
      await expect(
        client.publishMessage(testTopic, 'test-key', testMessage),
      ).resolves.toBeUndefined()
    })

    it('should publish multiple messages successfully', async () => {
      const messages = Array.from({ length: 5 }, (_, i) => ({
        id: i + 1,
        message: `Test message ${i + 1}`,
        timestamp: new Date().toISOString(),
        type: 'batch-test',
      }))

      for (const message of messages) {
        await expect(
          client.publishMessage(testTopic, `key-${message.id}`, message),
        ).resolves.toBeUndefined()
      }
    })

    it('should handle publishing to non-existent topic by creating it', async () => {
      const nonExistentTopic = 'auto-created-topic-' + Date.now()
      await client.admin.connect()
      await client.createTopic(nonExistentTopic)
      await client.admin.disconnect()

      await expect(
        client.publishMessage(nonExistentTopic, 'test-key', testMessage),
      ).resolves.toBeUndefined()
    })
  })

  describe('Message Consumption', () => {
    let receivedMessages: any[] = []

    beforeEach(async () => {
      receivedMessages = []
      await client.connectConsumer()
      await client.admin.connect()
      await client.createTopic(testTopic)
      await client.admin.disconnect()
    })

    afterEach(async () => {
      await client.disconnectConsumer()
    })

    it('should consume messages from topic', async () => {
      // Use a unique topic for this test
      const consumeTestTopic = `consume-test-topic-${Date.now()}`

      // Create the test topic
      await client.admin.connect()
      await client.createTopic(consumeTestTopic)
      await client.admin.disconnect()

      // First publish a message
      await client.connectProducer()
      await client.publishMessage(consumeTestTopic, 'consume-test', testMessage)
      await client.disconnectProducer()

      // Set up consumer with message collection
      let messageReceived = false

      // Subscribe to topic first
      await client.consumer.subscribe({
        topic: consumeTestTopic,
        fromBeginning: true,
      })

      // Mock the consumer run method to capture messages
      const consumePromise = new Promise<void>((resolve) => {
        client.consumer.run({
          eachMessage: async ({ topic, partition, message }) => {
            const value = message.value
              ? JSON.parse(message.value.toString())
              : null
            receivedMessages.push({
              topic,
              partition,
              offset: message.offset,
              key: message.key?.toString(),
              value,
              timestamp: message.timestamp,
            })
            messageReceived = true
            resolve()
          },
        })
      })

      // Wait for message or timeout
      await Promise.race([
        consumePromise,
        new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error('Message not received within timeout')),
            10000,
          ),
        ),
      ])

      expect(messageReceived).toBe(true)
      expect(receivedMessages).toHaveLength(1)
      expect(receivedMessages[0].value).toEqual(testMessage)
    })

    it('should handle subscription to non-existent topic', async () => {
      const nonExistentTopic = 'non-existent-topic-' + Date.now()
      await client.admin.connect()
      await client.createTopic(nonExistentTopic)
      await client.admin.disconnect()

      await expect(
        client.consumer.subscribe({
          topic: nonExistentTopic,
          fromBeginning: true,
        }),
      ).resolves.toBeUndefined()
    })
  })

  describe('Connection Management', () => {
    it('should connect and disconnect successfully', async () => {
      const testClient = new KafkaClient({
        clientId: 'connection-test-client',
        brokers: ['localhost:19092'],
      })

      await expect(testClient.connect()).resolves.toBeUndefined()
      await expect(testClient.disconnect()).resolves.toBeUndefined()
    })

    it('should handle individual producer connection', async () => {
      const testClient = new KafkaClient({
        clientId: 'producer-test-client',
        brokers: ['localhost:19092'],
      })

      await expect(testClient.connectProducer()).resolves.toBeUndefined()
      await expect(testClient.disconnectProducer()).resolves.toBeUndefined()
    })

    it('should handle individual consumer connection', async () => {
      const testClient = new KafkaClient({
        clientId: 'consumer-test-client',
        brokers: ['localhost:19092'],
      })

      await expect(testClient.connectConsumer()).resolves.toBeUndefined()
      await expect(testClient.disconnectConsumer()).resolves.toBeUndefined()
    })
  })

  describe('Error Handling', () => {
    it('should handle connection errors gracefully', async () => {
      const badClient = new KafkaClient({
        clientId: 'bad-client',
        brokers: ['localhost:99999'], // Invalid port
      })

      await expect(badClient.connect()).rejects.toThrow()
    }, 10000) // 10 second timeout

    it('should handle publishing errors gracefully', async () => {
      const badClient = new KafkaClient({
        clientId: 'bad-producer-client',
        brokers: ['localhost:99999'], // Invalid port
      })

      await expect(
        badClient.publishMessage('test-topic', 'key', testMessage),
      ).rejects.toThrow()
    }, 10000) // 10 second timeout
  })

  describe('Performance Tests', () => {
    beforeEach(async () => {
      await client.connectProducer()
      await client.admin.connect()
      await client.createTopic(testTopic)
      await client.admin.disconnect()
    })

    afterEach(async () => {
      await client.disconnectProducer()
    })

    it('should handle rapid message publishing', async () => {
      const messageCount = 100
      const messages = Array.from({ length: messageCount }, (_, i) => ({
        id: i + 1,
        message: `Performance test message ${i + 1}`,
        timestamp: new Date().toISOString(),
        type: 'performance',
      }))

      const startTime = Date.now()

      const publishPromises = messages.map((message, index) =>
        client.publishMessage(testTopic, `perf-key-${index}`, message),
      )

      await expect(Promise.all(publishPromises)).resolves.toBeDefined()

      const endTime = Date.now()
      const duration = endTime - startTime

      console.log(`Published ${messageCount} messages in ${duration}ms`)
      expect(duration).toBeLessThan(30000) // Should complete within 30 seconds
    })

    it('should handle concurrent operations', async () => {
      await client.connectConsumer()

      const publishPromise = client.publishMessage(
        testTopic,
        'concurrent-key',
        testMessage,
      )
      const subscribePromise = client.consumer.subscribe({
        topic: testTopic,
        fromBeginning: true,
      })

      await expect(
        Promise.all([publishPromise, subscribePromise]),
      ).resolves.toBeDefined()

      await client.disconnectConsumer()
    })
  })

  describe('Message Formats', () => {
    beforeEach(async () => {
      await client.connectProducer()
      await client.admin.connect()
      await client.createTopic(testTopic)
      await client.admin.disconnect()
    })

    afterEach(async () => {
      await client.disconnectProducer()
    })

    it('should handle different message formats', async () => {
      const formats = [
        {
          id: 1,
          message: 'String message',
          timestamp: new Date().toISOString(),
        },
        {
          id: 2,
          message: { nested: 'object' },
          timestamp: new Date().toISOString(),
        },
        { id: 3, message: [1, 2, 3], timestamp: new Date().toISOString() },
        { id: 4, message: null, timestamp: new Date().toISOString() },
        { id: 5, message: 42, timestamp: new Date().toISOString() },
      ]

      for (const format of formats) {
        await expect(
          client.publishMessage(testTopic, `format-key-${format.id}`, format),
        ).resolves.toBeUndefined()
      }
    })

    it('should handle large messages', async () => {
      const largeMessage = {
        id: 1,
        message: 'x'.repeat(10000), // 10KB message
        timestamp: new Date().toISOString(),
        type: 'large',
      }

      await expect(
        client.publishMessage(testTopic, 'large-key', largeMessage),
      ).resolves.toBeUndefined()
    })
  })
})
