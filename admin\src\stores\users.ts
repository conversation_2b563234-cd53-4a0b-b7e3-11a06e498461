import { create } from 'zustand'
import type {
  PaginatedResponse,
  SearchParams,
  User,
  UserCreateRequest,
  UserUpdateRequest,
} from '@/types'
import { useAuthStore } from './auth'

interface UsersState {
  // State
  users: User[]
  currentUser: User | null
  isLoading: boolean
  error: string | null
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

interface UsersActions {
  // Actions
  fetchUsers: (params?: SearchParams) => Promise<void>
  fetchUserById: (id: number) => Promise<User | null>
  createUser: (data: UserCreateRequest) => Promise<boolean>
  updateUser: (id: number, data: UserUpdateRequest) => Promise<boolean>
  deleteUser: (id: number) => Promise<boolean>
  setCurrentUser: (user: User | null) => void
  clearError: () => void
}

type UsersStore = UsersState & UsersActions

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

export const useUsersStore = create<UsersStore>((set, get) => ({
  // Initial state
  users: [],
  currentUser: null,
  isLoading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },

  // Actions
  fetchUsers: async (params?: SearchParams) => {
    set({ isLoading: true, error: null })

    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        set({ error: 'No authentication token', isLoading: false })
        return
      }

      const queryParams = new URLSearchParams()
      if (params?.query) queryParams.append('query', params.query)
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)

      const url = `${API_BASE_URL}/api/admin/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        console.log('fetchUsers', { data })
        // Handle both paginated and non-paginated responses
        if (Array.isArray(data)) {
          set({
            users: data,
            pagination: {
              total: data.length,
              page: 1,
              limit: data.length,
              totalPages: 1,
            },
            isLoading: false,
          })
        } else {
          set({
            users: data.data || data.users || [],
            pagination: {
              total: data.total || 0,
              page: data.page || 1,
              limit: data.limit || 10,
              totalPages: data.totalPages || 1,
            },
            isLoading: false,
          })
        }
      } else {
        const errorData = await response.json()
        set({
          error: errorData.error || 'Failed to fetch users',
          isLoading: false,
        })
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      })
    }
  },

  fetchUserById: async (id: number) => {
    set({ isLoading: true, error: null })

    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        set({ error: 'No authentication token', isLoading: false })
        return null
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/users/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const user = await response.json()
        set({ currentUser: user, isLoading: false })
        return user
      }
      const errorData = await response.json()
      set({
        error: errorData.error || 'Failed to fetch user',
        isLoading: false,
      })
      return null
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      })
      return null
    }
  },

  createUser: async (data: UserCreateRequest) => {
    set({ isLoading: true, error: null })

    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        set({ error: 'No authentication token', isLoading: false })
        return false
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        // Refresh users list
        await get().fetchUsers()
        set({ isLoading: false })
        return true
      }
      const errorData = await response.json()
      set({
        error: errorData.error || 'Failed to create user',
        isLoading: false,
      })
      return false
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      })
      return false
    }
  },

  updateUser: async (id: number, data: UserUpdateRequest) => {
    set({ isLoading: true, error: null })

    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        set({ error: 'No authentication token', isLoading: false })
        return false
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/users/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        // Refresh users list
        await get().fetchUsers()
        set({ isLoading: false })
        return true
      }
      const errorData = await response.json()
      set({
        error: errorData.error || 'Failed to update user',
        isLoading: false,
      })
      return false
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      })
      return false
    }
  },

  deleteUser: async (id: number) => {
    set({ isLoading: true, error: null })

    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        set({ error: 'No authentication token', isLoading: false })
        return false
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/users/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.ok) {
        // Refresh users list
        await get().fetchUsers()
        set({ isLoading: false })
        return true
      }
      const errorData = await response.json()
      set({
        error: errorData.error || 'Failed to delete user',
        isLoading: false,
      })
      return false
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      })
      return false
    }
  },

  setCurrentUser: (user: User | null) => {
    set({ currentUser: user })
  },

  clearError: () => {
    set({ error: null })
  },
}))
