import { OpenAI } from 'openai'
import { logger } from '../utils/logger'
import type { ChatMessage } from '../types'

export interface FormField {
  id: string
  name: string
  type: string
  label: string
  placeholder?: string
  required?: boolean
  value?: string
  options?: string[]
}

export interface FormAnalysis {
  fields: FormField[]
  purpose: string
  context: string
}

export interface FormFillRequest {
  formHTML: string
  userRequest: string
  context?: string
}

export interface FormFillResponse {
  success: boolean
  filledFields: Record<string, string>
  reasoning: string
  confidence: number
  actions: FormAction[]
}

export interface FormAction {
  type: 'fill' | 'click' | 'select' | 'wait'
  selector: string
  value?: string
  description: string
}

class FormAIService {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  /**
   * Analyze HTML form structure and extract form fields
   */
  analyzeForm(formHTML: string): FormAnalysis {
    // Parse HTML to extract form fields
    const fields: FormField[] = []

    // Use regex to extract form elements (simplified approach)
    const inputRegex = /<input[^>]*>/gi
    const selectRegex = /<select[^>]*>[\s\S]*?<\/select>/gi
    const textareaRegex = /<textarea[^>]*>[\s\S]*?<\/textarea>/gi

    const inputs = formHTML.match(inputRegex) || []
    const selects = formHTML.match(selectRegex) || []
    const textareas = formHTML.match(textareaRegex) || []

    // Process input fields
    inputs.forEach((input, index) => {
      const field = this.parseInputElement(input, index)
      if (field) fields.push(field)
    })

    // Process select fields
    selects.forEach((select, index) => {
      const field = this.parseSelectElement(select, index)
      if (field) fields.push(field)
    })

    // Process textarea fields
    textareas.forEach((textarea, index) => {
      const field = this.parseTextareaElement(textarea, index)
      if (field) fields.push(field)
    })

    return {
      fields,
      purpose: this.inferFormPurpose(formHTML, fields),
      context: this.extractFormContext(formHTML),
    }
  }

  /**
   * Use AI to fill form fields based on user request
   */
  async fillForm(request: FormFillRequest): Promise<FormFillResponse> {
    try {
      const analysis = this.analyzeForm(request.formHTML)

      const prompt = this.buildFillPrompt(
        analysis,
        request.userRequest,
        request.context,
      )

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant that helps fill out web forms. You analyze form fields and user requests to provide appropriate values. Always respond with valid JSON containing field values, reasoning, and confidence score.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 2000,
      })

      const aiResponse = response.choices[0]?.message?.content
      if (!aiResponse) {
        throw new Error('No response from AI')
      }

      return this.parseAIResponse(aiResponse, analysis)
    } catch (error) {
      logger.error('Error filling form with AI:', error)
      return {
        success: false,
        filledFields: {},
        reasoning: 'Failed to process form with AI',
        confidence: 0,
        actions: [],
      }
    }
  }

  /**
   * Generate step-by-step actions to fill the form
   */
  generateFormActions(
    analysis: FormAnalysis,
    filledFields: Record<string, string>,
  ): FormAction[] {
    const actions: FormAction[] = []

    analysis.fields.forEach((field) => {
      const value = filledFields[field.name] || filledFields[field.id]
      if (value) {
        const selector = field.id ? `#${field.id}` : `[name="${field.name}"]`

        switch (field.type) {
          case 'select':
            actions.push({
              type: 'select',
              selector,
              value,
              description: `Select "${value}" from ${field.label || field.name}`,
            })
            break
          case 'checkbox':
          case 'radio':
            if (value.toLowerCase() === 'true' || value === '1') {
              actions.push({
                type: 'click',
                selector,
                description: `Check ${field.label || field.name}`,
              })
            }
            break
          default:
            actions.push({
              type: 'fill',
              selector,
              value,
              description: `Fill ${field.label || field.name} with "${value}"`,
            })
        }
      }
    })

    return actions
  }

  private parseInputElement(input: string, index: number): FormField | null {
    const id = this.extractAttribute(input, 'id') || `input_${index}`
    const name = this.extractAttribute(input, 'name') || id
    const type = this.extractAttribute(input, 'type') || 'text'
    const placeholder = this.extractAttribute(input, 'placeholder')
    const required = input.includes('required')

    // Try to find associated label
    const label = this.findLabelForField(input, id, name)

    return {
      id,
      name,
      type,
      label: label || name,
      placeholder,
      required,
    }
  }

  private parseSelectElement(select: string, index: number): FormField | null {
    const id = this.extractAttribute(select, 'id') || `select_${index}`
    const name = this.extractAttribute(select, 'name') || id
    const required = select.includes('required')

    // Extract options
    const optionRegex = /<option[^>]*>(.*?)<\/option>/gi
    const options: string[] = []
    let match
    while ((match = optionRegex.exec(select)) !== null) {
      const optionText = match[1].trim()
      if (optionText) options.push(optionText)
    }

    const label = this.findLabelForField(select, id, name)

    return {
      id,
      name,
      type: 'select',
      label: label || name,
      required,
      options,
    }
  }

  private parseTextareaElement(
    textarea: string,
    index: number,
  ): FormField | null {
    const id = this.extractAttribute(textarea, 'id') || `textarea_${index}`
    const name = this.extractAttribute(textarea, 'name') || id
    const placeholder = this.extractAttribute(textarea, 'placeholder')
    const required = textarea.includes('required')

    const label = this.findLabelForField(textarea, id, name)

    return {
      id,
      name,
      type: 'textarea',
      label: label || name,
      placeholder,
      required,
    }
  }

  private extractAttribute(element: string, attribute: string): string | null {
    const regex = new RegExp(`${attribute}=["']([^"']*)["']`, 'i')
    const match = element.match(regex)
    return match ? match[1] : null
  }

  private findLabelForField(element: string, id: string, name: string): string {
    // This is a simplified approach - in a real implementation,
    // you'd want to parse the entire HTML to find labels
    return id.replace(/[-_]/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
  }

  private inferFormPurpose(formHTML: string, fields: FormField[]): string {
    // Simple heuristics to determine form purpose
    const html = formHTML.toLowerCase()

    if (
      html.includes('contact') ||
      fields.some((f) => f.name.includes('email'))
    ) {
      return 'contact'
    }
    if (html.includes('register') || html.includes('signup')) {
      return 'registration'
    }
    if (html.includes('login') || html.includes('signin')) {
      return 'login'
    }
    if (html.includes('payment') || html.includes('billing')) {
      return 'payment'
    }
    if (html.includes('profile') || html.includes('account')) {
      return 'profile'
    }

    return 'general'
  }

  private extractFormContext(formHTML: string): string {
    // Extract text content around the form for context
    const textRegex = />([^<>]+)</g
    const texts: string[] = []
    let match

    while ((match = textRegex.exec(formHTML)) !== null) {
      const text = match[1].trim()
      if (text.length > 2) texts.push(text)
    }

    return texts.join(' ').substring(0, 500)
  }

  private buildFillPrompt(
    analysis: FormAnalysis,
    userRequest: string,
    context?: string,
  ): string {
    return `
Please analyze this form and fill it based on the user's request.

FORM ANALYSIS:
Purpose: ${analysis.purpose}
Context: ${analysis.context}
${context ? `Additional Context: ${context}` : ''}

FORM FIELDS:
${analysis.fields
  .map(
    (field) =>
      `- ${field.name} (${field.type}): ${field.label}${field.required ? ' [REQUIRED]' : ''}${field.options ? ` Options: ${field.options.join(', ')}` : ''}`,
  )
  .join('\n')}

USER REQUEST: ${userRequest}

Please provide a JSON response with the following structure:
{
  "filledFields": {
    "fieldName": "value",
    ...
  },
  "reasoning": "Brief explanation of how you filled the fields",
  "confidence": 0.9
}

Rules:
1. Only fill fields that are relevant to the user request
2. Use appropriate values based on the field type and purpose
3. For required fields, always provide a value if possible
4. For select fields, choose from available options
5. Be conservative with personal information
6. Confidence should be 0-1 based on how well you understood the request
`
  }

  private parseAIResponse(
    aiResponse: string,
    analysis: FormAnalysis,
  ): FormFillResponse {
    try {
      const parsed = JSON.parse(aiResponse)
      const actions = this.generateFormActions(
        analysis,
        parsed.filledFields || {},
      )

      return {
        success: true,
        filledFields: parsed.filledFields || {},
        reasoning: parsed.reasoning || 'Form filled by AI',
        confidence: parsed.confidence || 0.5,
        actions,
      }
    } catch (error) {
      logger.error('Error parsing AI response:', error)
      return {
        success: false,
        filledFields: {},
        reasoning: 'Failed to parse AI response',
        confidence: 0,
        actions: [],
      }
    }
  }
}

export default new FormAIService()
