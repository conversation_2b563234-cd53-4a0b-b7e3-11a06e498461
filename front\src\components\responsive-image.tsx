'use client'

import Image from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  sizes?: string
  fill?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
}

export function ResponsiveImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  placeholder = 'empty',
  blurDataURL,
  loading = 'lazy',
  onLoad,
  onError,
}: ResponsiveImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }

  // Default responsive sizes if not provided
  const defaultSizes =
    sizes ||
    '(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw'

  if (hasError) {
    return (
      <div
        className={cn(
          'bg-gray-200 flex items-center justify-center text-gray-500 text-sm',
          fill ? 'absolute inset-0' : '',
          className
        )}
        style={!fill ? { width, height } : undefined}
      >
        <span>Image not available</span>
      </div>
    )
  }

  return (
    <div className={cn('relative', !fill && 'inline-block', className)}>
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center',
            fill ? '' : 'absolute'
          )}
          style={!fill ? { width, height } : undefined}
        >
          <div className="w-8 h-8 bg-gray-300 rounded animate-pulse" />
        </div>
      )}

      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        sizes={defaultSizes}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100',
          fill ? `object-${objectFit}` : '',
          objectPosition && fill ? `object-${objectPosition}` : ''
        )}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  )
}

interface ResponsiveAvatarProps {
  src?: string
  alt: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  className?: string
  fallback?: string
}

export function ResponsiveAvatar({
  src,
  alt,
  size = 'md',
  className,
  fallback,
}: ResponsiveAvatarProps) {
  const [hasError, setHasError] = useState(false)

  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl',
    '2xl': 'w-20 h-20 text-2xl',
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (!src || hasError) {
    return (
      <div
        className={cn(
          'rounded-full bg-gray-300 flex items-center justify-center font-medium text-gray-600',
          sizeClasses[size],
          className
        )}
      >
        {fallback || getInitials(alt)}
      </div>
    )
  }

  return (
    <div
      className={cn(
        'relative rounded-full overflow-hidden',
        sizeClasses[size],
        className
      )}
    >
      <ResponsiveImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        className="rounded-full"
        onError={() => setHasError(true)}
      />
    </div>
  )
}

interface ResponsiveBackgroundImageProps {
  src: string
  alt: string
  children?: React.ReactNode
  className?: string
  overlay?: boolean
  overlayOpacity?: number
  priority?: boolean
}

export function ResponsiveBackgroundImage({
  src,
  alt,
  children,
  className,
  overlay = false,
  overlayOpacity = 0.5,
  priority = false,
}: ResponsiveBackgroundImageProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <ResponsiveImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        priority={priority}
        className="absolute inset-0"
      />

      {overlay && (
        <div
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
        />
      )}

      {children && <div className="relative z-10">{children}</div>}
    </div>
  )
}

// Hook for responsive image sizes
export function useResponsiveImageSizes() {
  return {
    hero: '(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw',
    card: '(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw',
    thumbnail:
      '(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw',
    avatar: '(max-width: 640px) 40px, (max-width: 768px) 48px, 64px',
    logo: '(max-width: 640px) 120px, (max-width: 768px) 150px, 200px',
  }
}
