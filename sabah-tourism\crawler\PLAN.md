# Douyin Crawler Implementation Plan

## Overview

Create a comprehensive Douyin social media crawler using Stagehand for browser automation, with SQLite caching, media download capabilities, and a reporting interface. This crawler will be designed as a reusable framework for multiple social media platforms.

## 1. Project Structure

```
sabah-tourism/crawler/
├── src/
│   ├── crawlers/
│   │   ├── base/
│   │   │   ├── BaseCrawler.ts          # Abstract base crawler
│   │   │   ├── MediaDownloader.ts      # Media file handler
│   │   │   └── DatabaseManager.ts      # SQLite operations
│   │   ├── douyin/
│   │   │   ├── DouyinCrawler.ts        # Douyin-specific crawler
│   │   │   ├── DouyinParser.ts         # Content extraction logic
│   │   │   └── DouyinConfig.ts         # Platform configuration
│   │   └── index.ts                    # Crawler factory
│   ├── database/
│   │   ├── schema.sql                  # Database schema
│   │   ├── migrations/                 # Database migrations
│   │   └── models.ts                   # TypeScript models
│   ├── reporting/
│   │   ├── ReportGenerator.ts          # Report generation
│   │   ├── WebInterface.ts             # Web-based report viewer
│   │   └── templates/                  # Report templates
│   ├── utils/
│   │   ├── logger.ts                   # Logging utilities
│   │   ├── retry.ts                    # Retry mechanisms
│   │   └── validation.ts               # Data validation
│   └── types/
│       ├── crawler.ts                  # Crawler interfaces
│       ├── social-media.ts             # Social media data types
│       └── database.ts                 # Database types
├── output/                             # Downloaded media files
│   └── douyin/                         # Douyin-specific downloads
│       └── <pageid>/                   # Individual post folders
├── config/
│   ├── crawler.config.ts               # Crawler configuration
│   └── database.config.ts              # Database configuration
├── scripts/
│   ├── crawl-douyin.ts                 # Main crawler script
│   ├── setup-db.ts                     # Database setup
│   └── generate-report.ts              # Report generation
├── tests/
│   ├── unit/                           # Unit tests
│   └── integration/                    # Integration tests
├── package.json
├── tsconfig.json
├── .env.example
└── README.md
```

## 2. Technology Stack

### Core Technologies

- **Stagehand**: Browser automation with AI-powered element detection
- **TypeScript**: Type-safe development
- **SQLite**: Local caching and data storage
- **Bun**: Runtime and package manager
- **Playwright**: Underlying browser automation (via Stagehand)

### Additional Libraries

- **better-sqlite3**: SQLite database operations
- **axios**: HTTP requests for media downloads
- **cheerio**: HTML parsing (fallback)
- **zod**: Schema validation
- **winston**: Logging
- **express**: Web interface for reports
- **tailwindcss**: Report styling

## 3. Database Schema

### Core Tables

```sql
-- Social media posts (shared across platforms)
CREATE TABLE social_posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform TEXT NOT NULL,           -- 'douyin', 'tiktok', 'instagram', etc.
    post_id TEXT NOT NULL,            -- Platform-specific post ID
    url TEXT NOT NULL,                -- Original post URL
    title TEXT,                       -- Post title/caption
    content TEXT,                     -- Post text content
    author_username TEXT,             -- Author username
    author_display_name TEXT,         -- Author display name
    author_avatar_url TEXT,           -- Author profile picture
    posted_at DATETIME,               -- When post was published
    crawled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    tags TEXT,                        -- JSON array of hashtags
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    metadata TEXT,                    -- JSON metadata specific to platform
    UNIQUE(platform, post_id)
);

-- Media files associated with posts
CREATE TABLE media_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER REFERENCES social_posts(id),
    media_type TEXT NOT NULL,         -- 'image', 'video', 'audio'
    file_name TEXT NOT NULL,          -- Local file name
    file_path TEXT NOT NULL,          -- Relative path to file
    original_url TEXT,                -- Original media URL
    file_size INTEGER,                -- File size in bytes
    duration INTEGER,                 -- For video/audio (seconds)
    width INTEGER,                    -- For images/videos
    height INTEGER,                   -- For images/videos
    downloaded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Crawling sessions and status tracking
CREATE TABLE crawl_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform TEXT NOT NULL,
    keywords TEXT NOT NULL,           -- JSON array of search keywords
    status TEXT NOT NULL,             -- 'running', 'completed', 'failed', 'paused'
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    total_posts INTEGER DEFAULT 0,
    successful_posts INTEGER DEFAULT 0,
    failed_posts INTEGER DEFAULT 0,
    error_message TEXT,
    config TEXT                       -- JSON crawl configuration
);

-- Individual crawl attempts (for resume functionality)
CREATE TABLE crawl_attempts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER REFERENCES crawl_sessions(id),
    post_url TEXT NOT NULL,
    keyword TEXT,                     -- Which keyword led to this post
    status TEXT NOT NULL,             -- 'pending', 'success', 'failed', 'skipped'
    error_message TEXT,
    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
);

-- Search keywords tracking
CREATE TABLE search_keywords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    keyword TEXT NOT NULL UNIQUE,
    platform TEXT NOT NULL,
    last_crawled DATETIME,
    total_posts_found INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 4. Implementation Phases

### Phase 1: Foundation Setup (Week 1)

1. **Project Initialization**
   - Set up TypeScript project with Bun
   - Install Stagehand and dependencies
   - Create basic project structure
   - Set up SQLite database with schema

2. **Base Crawler Architecture**
   - Implement `BaseCrawler` abstract class
   - Create `DatabaseManager` for SQLite operations
   - Implement `MediaDownloader` utility
   - Set up logging and error handling

### Phase 2: Douyin Analysis & Implementation (Week 2)

1. **Douyin Platform Analysis**
   - Research Douyin website structure and DOM elements
   - Identify video/image/audio selectors
   - Understand pagination and infinite scroll
   - Map out search functionality

2. **Douyin Crawler Implementation**
   - Implement `DouyinCrawler` extending `BaseCrawler`
   - Create `DouyinParser` for content extraction
   - Implement search functionality with keywords
   - Add media detection and download logic

### Phase 3: Caching & Resume Functionality (Week 3)

1. **Caching System**
   - Implement SQLite-based caching
   - Add duplicate detection
   - Create resume functionality for interrupted crawls
   - Implement incremental crawling

2. **Error Handling & Retry Logic**
   - Add comprehensive error handling
   - Implement retry mechanisms with exponential backoff
   - Create failure tracking and reporting
   - Add graceful shutdown handling

### Phase 4: Media Download & Storage (Week 4)

1. **Media Download System**
   - Implement parallel media downloads
   - Add file type detection and validation
   - Create organized folder structure (`douyin/<pageid>/`)
   - Add progress tracking for downloads

2. **File Management**
   - Implement file deduplication
   - Add file integrity checks
   - Create cleanup utilities
   - Add storage optimization

### Phase 5: Reporting Interface (Week 5)

1. **Report Generation**
   - Create web-based reporting interface
   - Implement pagination for large datasets
   - Add filtering by platform, keywords, date range
   - Create searchable author selection

2. **Advanced Features**
   - Add export functionality (CSV, JSON)
   - Implement real-time crawl monitoring
   - Create analytics dashboard
   - Add batch operations

## 5. Key Features Implementation

### Stagehand Integration

```typescript
// Example Stagehand usage for Douyin
const stagehand = new Stagehand({
  env: "LOCAL",
  headless: false, // For debugging
  logger: logger,
});

const page = stagehand.page;
await page.goto("https://www.douyin.com");

// AI-powered element detection
await page.act("Click the search box");
await page.act(`Type "${keyword}" into the search box`);
await page.act("Press Enter to search");

// Extract post data using AI
const posts = await page.extract({
  instruction: "Extract all video posts with their metadata",
  schema: z.object({
    posts: z.array(
      z.object({
        id: z.string(),
        title: z.string(),
        author: z.string(),
        url: z.string(),
        mediaUrl: z.string(),
        likes: z.number(),
        comments: z.number(),
      }),
    ),
  }),
});
```

### Resume Functionality

- Track crawl progress in SQLite
- Store failed URLs with error reasons
- Implement checkpoint system for large crawls
- Allow manual retry of failed items

### Media Download Strategy

- Parallel downloads with rate limiting
- File type detection and validation
- Organized storage: `douyin/<post_id>/<filename>.ext`
- Progress tracking and resume capability

### Reporting Features

- Web interface with filtering and search
- Real-time crawl status monitoring
- Export capabilities (CSV, JSON)
- Analytics and statistics

## 6. Configuration System

### Crawler Configuration

```typescript
interface CrawlerConfig {
  platform: "douyin";
  keywords: string[];
  maxPosts: number;
  downloadMedia: boolean;
  mediaTypes: ("image" | "video" | "audio")[];
  rateLimiting: {
    requestsPerMinute: number;
    downloadConcurrency: number;
  };
  retryConfig: {
    maxRetries: number;
    backoffMultiplier: number;
  };
}
```

## 7. Testing Strategy

- Unit tests for core utilities
- Integration tests for crawler functionality
- Mock Douyin responses for testing
- Performance testing for large datasets

## 8. Deployment & Usage

- Docker containerization
- CLI interface for easy usage
- Configuration file support
- Monitoring and alerting

This plan provides a comprehensive foundation for building a robust, scalable Douyin crawler that can be extended to other social media platforms. The use of Stagehand makes it particularly powerful for handling dynamic content and UI changes, while the SQLite caching system ensures reliability and resume capability.
