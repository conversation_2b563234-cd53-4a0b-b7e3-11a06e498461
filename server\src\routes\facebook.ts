import express, { type Request, type Response } from 'express'
import { v4 as uuidv4 } from 'uuid'
import facebookService from '../services/facebook'
import openaiService from '../services/openai'
import type { ChatMessage, ChatSession, FacebookWebhookPayload } from '../types'

const router: express.Router = express.Router()

// Store Facebook chat sessions in memory (in production, use a database)
const facebookSessions = new Map<string, ChatSession>()

// Webhook verification endpoint
router.get('/webhook', (req: Request, res: Response): void => {
  const mode = req.query['hub.mode']
  const token = req.query['hub.verify_token']
  const challenge = req.query['hub.challenge']

  console.log('Facebook webhook verification request:', {
    mode,
    token,
    challenge,
  })

  if (mode && token && challenge) {
    const verificationResult = facebookService.verifyWebhookChallenge(
      mode as string,
      token as string,
      challenge as string,
    )

    if (verificationResult) {
      console.log('Facebook webhook verified successfully')
      res.status(200).send(challenge)
      return
    }
  }

  console.log('Facebook webhook verification failed')
  res.status(403).send('Forbidden')
})

// Webhook endpoint for receiving Facebook messages
router.post('/webhook', async (req: Request, res: Response): Promise<void> => {
  try {
    const signature = req.headers['x-hub-signature-256'] as string
    const payload = JSON.stringify(req.body)

    // Verify webhook signature for security
    if (!facebookService.verifyWebhookSignature(payload, signature)) {
      console.log('Facebook webhook signature verification failed')
      res.status(403).send('Forbidden')
      return
    }

    const webhookPayload: FacebookWebhookPayload = req.body

    // Process each entry in the webhook payload
    for (const entry of webhookPayload.entry) {
      for (const messagingEvent of entry.messaging) {
        if (messagingEvent.message && !messagingEvent.message.is_echo) {
          // Handle incoming message
          await handleIncomingMessage(messagingEvent)
        } else if (messagingEvent.postback) {
          // Handle postback (button clicks, etc.)
          await handlePostback(messagingEvent)
        }
      }
    }

    res.status(200).send('OK')
  } catch (error) {
    console.error('Facebook webhook error:', error)
    res.status(500).send('Internal Server Error')
  }
})

// Handle incoming Facebook message and integrate with OpenAI
async function handleIncomingMessage(messagingEvent: any): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const message = messagingEvent.message

    let messageText = ''
    let messageType = 'text'
    let mediaUrl: string | undefined

    if (message.text) {
      messageText = message.text
      messageType = 'text'
    } else if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0]
      messageType = attachment.type
      mediaUrl = attachment.payload.url
      messageText = `[${attachment.type.toUpperCase()} received]`
    } else if (message.quick_reply) {
      messageText = message.quick_reply.payload
      messageType = 'quick_reply'
    }

    console.log(`Processing Facebook message from ${senderId}: ${messageText}`)

    // Get or create session for this sender
    let session = facebookSessions.get(senderId)
    if (!session) {
      session = {
        id: uuidv4(),
        messages: [
          {
            role: 'system',
            content:
              'You are a helpful assistant for Halal inquiries. You speak in Bahasa Malaysia by default. You may switch between Bahasa Malaysia, Chinese, English, and Tamil. Keep responses concise for Facebook Messenger.',
          },
        ],
        createdAt: new Date(),
      }
      facebookSessions.set(senderId, session)
    }

    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl: mediaUrl,
    }

    session.messages.push(userMessage)

    // Get response from OpenAI
    let openaiResponse

    if (messageType === 'image' && mediaUrl) {
      // Use image analysis for image messages
      openaiResponse = await openaiService.analyzeImage(
        mediaUrl,
        messageText ||
          "What's in this image? Please provide a brief description.",
      )
    } else {
      // Prepare messages for OpenAI (exclude timestamps)
      const openaiMessages = session.messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }))

      // Use text chat for text messages
      openaiResponse = await openaiService.sendTextMessage(openaiMessages)
    }

    if (openaiResponse.success && openaiResponse.message) {
      // Add assistant message to session
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: openaiResponse.message,
        timestamp: new Date(),
      }
      session.messages.push(assistantMessage)

      // Send response back to Facebook Messenger
      const sendResult = await facebookService.sendTextMessage(
        senderId,
        openaiResponse.message,
      )

      if (!sendResult.success) {
        console.error('Failed to send Facebook message:', sendResult.error)
      }
    } else {
      console.error('OpenAI request failed:', openaiResponse.error)

      // Send error message to user
      await facebookService.sendTextMessage(
        senderId,
        'Sorry, I encountered an error processing your message. Please try again later.',
      )
    }

    // TODO: Log the message to database when implemented
    // await databaseService.logFacebookMessage(
    //   message.mid,
    //   senderId,
    //   facebookService.getConfig()?.pageId || '',
    //   messageType,
    //   messageText,
    //   'inbound',
    //   mediaUrl
    // );
  } catch (error) {
    console.error('Error handling Facebook message:', error)
  }
}

// Handle Facebook postback events (button clicks, etc.)
async function handlePostback(messagingEvent: any): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const postback = messagingEvent.postback

    console.log(
      `Processing Facebook postback from ${senderId}: ${postback.payload}`,
    )

    // Handle different postback payloads
    switch (postback.payload) {
      case 'GET_STARTED':
        await facebookService.sendTextMessage(
          senderId,
          'Welcome to Halal Malaysia! I can help you with halal certification inquiries. How can I assist you today?',
        )
        break

      case 'HELP':
        await facebookService.sendTextMessage(
          senderId,
          'I can help you with:\n• Halal certification information\n• Application procedures\n• Requirements and guidelines\n• General halal inquiries\n\nJust ask me anything!',
        )
        break

      default:
        // Treat unknown postbacks as regular messages
        await handleIncomingMessage({
          sender: { id: senderId },
          message: { text: postback.payload },
        })
        break
    }
  } catch (error) {
    console.error('Error handling Facebook postback:', error)
  }
}

export default router
