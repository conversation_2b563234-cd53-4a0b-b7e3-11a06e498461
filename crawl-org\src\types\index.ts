export interface ScrapedCompany {
  companyName: string;
  registrationNumber?: string;
  businessType?: string;
  category?: string;
  subcategory?: string;
  address?: string;
  state?: string;
  postcode?: string;
  city?: string;
  country?: string;
  phone?: string;
  fax?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  certificateNumber?: string;
  certificateType?: string;
  certificateStatus?: string;
  issuedDate?: string;
  expiryDate?: string;
  sourceUrl?: string;
  pageNumber?: number;
  rawData?: string;
}

export interface ScrapingConfig {
  maxPages: number;
  concurrentPages: number;
  delayBetweenRequests: number;
  retryAttempts: number;
  timeoutMs: number;
  headless: boolean;
  debug: boolean;
  siteId: number;
}

export interface ScrapingStats {
  totalPages: number;
  processedPages: number;
  totalCompanies: number;
  savedCompanies: number;
  duplicates: number;
  errors: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
}

export interface ScrapingResult {
  success: boolean;
  stats: ScrapingStats;
  errors: string[];
}
