import { create } from 'zustand';
import type { Document, DocumentCreateRequest, DocumentUpdateRequest, DocumentUploadResponse } from '@/types/document';
import { api } from '@/lib/api';

interface DocumentsState {
  documents: Document[];
  currentDocument: Document | null;
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  uploadProgress: number;
}

interface DocumentsActions {
  fetchDocuments: (collectionId?: number) => Promise<void>;
  fetchDocumentById: (id: number) => Promise<Document | null>;
  uploadDocument: (collectionId: number, file: File) => Promise<DocumentUploadResponse | null>;
  createDocument: (data: DocumentCreateRequest) => Promise<boolean>;
  updateDocument: (id: number, data: DocumentUpdateRequest) => Promise<boolean>;
  deleteDocument: (id: number) => Promise<boolean>;
  setCurrentDocument: (document: Document | null) => void;
  clearError: () => void;
  setUploadProgress: (progress: number) => void;
}

type DocumentsStore = DocumentsState & DocumentsActions;

export const useDocumentsStore = create<DocumentsStore>((set, get) => ({
  documents: [],
  currentDocument: null,
  isLoading: false,
  isUploading: false,
  error: null,
  uploadProgress: 0,

  fetchDocuments: async (collectionId?: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = collectionId 
        ? await api.admin.getDocumentsByCollection(collectionId)
        : await api.admin.getDocuments();
      
      if (response.success && response.data) {
        set({ documents: response.data, isLoading: false });
      } else {
        set({ error: response.error || 'Failed to fetch documents', isLoading: false });
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch documents', 
        isLoading: false 
      });
    }
  },

  fetchDocumentById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.getDocumentById(id);
      if (response.success && response.data) {
        const document = response.data;
        set({ currentDocument: document, isLoading: false });
        return document;
      } else {
        set({ error: response.error || 'Failed to fetch document', isLoading: false });
        return null;
      }
    } catch (error) {
      console.error('Error fetching document:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch document', 
        isLoading: false 
      });
      return null;
    }
  },

  uploadDocument: async (collectionId: number, file: File) => {
    set({ isUploading: true, error: null, uploadProgress: 0 });
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('collectionId', collectionId.toString());

      const response = await api.admin.uploadDocument(formData, (progress) => {
        set({ uploadProgress: progress });
      });

      if (response.success && response.data) {
        set({ isUploading: false, uploadProgress: 100 });
        // Refresh documents list
        await get().fetchDocuments(collectionId);
        return response.data;
      } else {
        set({ error: response.error || 'Failed to upload document', isUploading: false });
        return null;
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to upload document', 
        isUploading: false 
      });
      return null;
    }
  },

  createDocument: async (data: DocumentCreateRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.createDocument(data);
      if (response.success) {
        // Refresh documents list
        await get().fetchDocuments(data.collectionId);
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || 'Failed to create document', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error creating document:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create document', 
        isLoading: false 
      });
      return false;
    }
  },

  updateDocument: async (id: number, data: DocumentUpdateRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.updateDocument(id, data);
      if (response.success) {
        // Refresh documents list
        const current = get().currentDocument;
        if (current) {
          await get().fetchDocuments(current.collectionId);
        }
        // Update current document if it's the one being updated
        if (current && current.id === id) {
          await get().fetchDocumentById(id);
        }
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || 'Failed to update document', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error updating document:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update document', 
        isLoading: false 
      });
      return false;
    }
  },

  deleteDocument: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.deleteDocument(id);
      if (response.success) {
        // Remove from documents list
        const documents = get().documents.filter(d => d.id !== id);
        set({ documents, isLoading: false });
        // Clear current document if it's the one being deleted
        const current = get().currentDocument;
        if (current && current.id === id) {
          set({ currentDocument: null });
        }
        return true;
      } else {
        set({ error: response.error || 'Failed to delete document', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete document', 
        isLoading: false 
      });
      return false;
    }
  },

  setCurrentDocument: (document: Document | null) => {
    set({ currentDocument: document });
  },

  clearError: () => {
    set({ error: null });
  },

  setUploadProgress: (progress: number) => {
    set({ uploadProgress: progress });
  },
}));
