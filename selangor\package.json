{"name": "selangor", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 16010", "build": "next build", "deploy": "next build && npx @cloudflare/next-on-pages && wrangler deploy", "start": "next start", "lint": "eslint . --fix", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "next": "15.3.4", "postgres": "^3.4.7", "r2r-js": "^0.4.43", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "tsx": "^4.20.3"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.2", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-react": "^7.37.5", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "tailwindcss": "^4", "typescript": "^5"}}