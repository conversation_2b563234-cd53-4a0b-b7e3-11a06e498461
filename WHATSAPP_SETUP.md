# WhatsApp Business API Integration Setup Guide

This guide will help you set up WhatsApp Business API integration with the Halal Chat Application.

## Overview

The WhatsApp Business API integration allows users to interact with the AI assistant through WhatsApp messages. The system includes:

- **Backend Integration**: WhatsApp Business API webhook handling and message processing
- **Admin Panel**: Web interface for configuration, testing, and monitoring
- **Unified Chat System**: Integration with existing OpenAI-powered chat system

## Prerequisites

1. **WhatsApp Business API Access**
   - WhatsApp Business Account
   - Meta Developer Account
   - WhatsApp Business API access (Cloud API or On-Premises)

2. **Server Requirements**
   - Public HTTPS endpoint for webhook
   - SSL certificate
   - Node.js 18+ with the existing chat server

3. **API Credentials**
   - Access Token
   - Phone Number ID
   - Webhook Verify Token
   - Business Account ID (optional)

## Quick Start

### 1. Backend Setup

The backend is already configured with WhatsApp support. Ensure your server is running:

```bash
cd server
npm install
npm run build
npm start
```

### 2. Admin Panel Access

1. Navigate to `http://localhost:16000/admin`
2. Login with default credentials:
   - Username: `admin`
   - Password: `admin123`

### 3. WhatsApp Configuration

1. Go to **WhatsApp Configuration** in the admin panel
2. Enter your WhatsApp Business API credentials:
   - **Access Token**: Your WhatsApp Business API access token
   - **Phone Number ID**: Your WhatsApp Business phone number ID
   - **Webhook Verify Token**: A secure token for webhook verification
   - **Business Account ID**: (Optional) Your business account ID

3. Copy the webhook URL provided
4. Configure the webhook in your Meta Developer Console

### 4. Webhook Setup in Meta Developer Console

1. Go to [Meta for Developers](https://developers.facebook.com/)
2. Navigate to your WhatsApp Business API app
3. Go to **WhatsApp > Configuration**
4. Set up webhook:
   - **Callback URL**: Use the webhook URL from admin panel
   - **Verify Token**: Use the same token from your configuration
   - **Webhook Fields**: Subscribe to `messages`

### 5. Test Integration

1. Use the **Test WhatsApp** feature in admin panel
2. Send a test message to verify configuration
3. Send a WhatsApp message to your business number
4. Check admin panel for message logs

## Environment Variables

### Backend (.env)

```env
# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id

# Admin Authentication
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=24h
ADMIN_DEFAULT_PASSWORD=admin123

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com
```

### Frontend (.env)

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:16001
NEXT_PUBLIC_ADMIN_ENABLED=true
```

## API Endpoints

### Admin Authentication

- `POST /api/admin/login` - Admin login
- `GET /api/admin/me` - Get current admin user

### WhatsApp Configuration

- `GET /api/admin/whatsapp/config` - Get WhatsApp configuration
- `POST /api/admin/whatsapp/config` - Save WhatsApp configuration
- `POST /api/admin/whatsapp/test` - Test WhatsApp configuration
- `GET /api/admin/whatsapp/webhook-url` - Get webhook URL

### WhatsApp Messaging

- `POST /api/admin/whatsapp/test-message` - Send test message
- `GET /api/admin/whatsapp/messages/:phoneNumber` - Get message history

### WhatsApp Webhook

- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Receive WhatsApp messages

### Chat Integration

- `GET /api/chat/whatsapp-status` - Get WhatsApp integration status

## Features

### 1. Message Processing

- Automatic message routing to OpenAI
- Support for text and image messages
- Session management per phone number
- Message history logging

### 2. Admin Panel

- **Dashboard**: Overview of system status
- **Configuration**: WhatsApp API setup
- **Testing**: Send test messages
- **Monitoring**: View active sessions and message logs

### 3. Security

- JWT-based admin authentication
- Webhook signature verification
- Secure token storage
- Rate limiting

### 4. Integration

- Unified chat system with web interface
- Shared OpenAI processing
- Cross-platform message history
- Consistent AI responses

## Troubleshooting

### Common Issues

1. **Webhook Verification Failed**
   - Check verify token matches in both admin panel and Meta console
   - Ensure webhook URL is publicly accessible
   - Verify HTTPS certificate is valid

2. **Messages Not Received**
   - Check webhook subscription includes "messages"
   - Verify phone number ID is correct
   - Check server logs for errors

3. **Test Messages Fail**
   - Verify access token is valid and not expired
   - Check phone number format (include country code)
   - Ensure recipient has WhatsApp installed

4. **Admin Panel Access Issues**
   - Check default credentials (admin/admin123)
   - Verify JWT_SECRET is set
   - Check server is running on correct port

### Debug Steps

1. **Check Server Logs**

   ```bash
   cd server
   npm run dev  # Development mode with detailed logs
   ```

2. **Test Webhook Manually**

   ```bash
   curl -X GET "https://your-domain.com/api/whatsapp/webhook?hub.mode=subscribe&hub.verify_token=your_token&hub.challenge=test"
   ```

3. **Verify Database**
   - Check `server/data/halal_chat.db` exists
   - Verify admin user and WhatsApp config tables

## Production Deployment

### 1. Security Considerations

- Change default admin password
- Use strong JWT secret
- Enable HTTPS
- Set up proper firewall rules

### 2. Environment Setup

- Set `NODE_ENV=production`
- Configure production database
- Set up monitoring and logging
- Configure backup procedures

### 3. Scaling

- Use Redis for session storage
- Implement message queuing
- Set up load balancing
- Monitor API rate limits

## Support

For issues and questions:

1. Check server logs for error messages
2. Verify WhatsApp Business API documentation
3. Test with Meta's API testing tools
4. Review webhook payload format

## License

This WhatsApp integration is part of the Halal Chat Application and follows the same license terms.
