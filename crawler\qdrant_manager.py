#!/usr/bin/env python3
"""
Qdrant Manager for pushing crawled files and querying collections
Handles .md, .pdf, .docx, and .txt files from the output directory
"""

import os
import json
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
    from qdrant_client.http.exceptions import UnexpectedResponse
except ImportError:
    print("Please install qdrant-client: pip install qdrant-client")
    raise

try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    print("Please install sentence-transformers: pip install sentence-transformers")
    raise

try:
    import PyPDF2
    import docx
except ImportError:
    print("Please install document processing libraries: pip install PyPDF2 python-docx")
    raise

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QdrantManager:
    def __init__(self, qdrant_url: str = "http://localhost:6333", model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize Qdrant Manager
        
        Args:
            qdrant_url: Qdrant server URL
            model_name: Sentence transformer model name for embeddings
        """
        self.client = QdrantClient(url=qdrant_url)
        self.model = SentenceTransformer(model_name)
        self.vector_size = self.model.get_sentence_embedding_dimension()
        logger.info(f"Initialized QdrantManager with model {model_name} (vector size: {self.vector_size})")
    
    def _get_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file content"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return ""
    
    def _get_file_size(self, file_path: Path) -> int:
        """Get file size in bytes"""
        try:
            return file_path.stat().st_size
        except Exception as e:
            logger.error(f"Error getting size for {file_path}: {e}")
            return 0
    
    def _read_text_file(self, file_path: Path) -> str:
        """Read content from text files (.md, .txt)"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return ""
    
    def _read_pdf_file(self, file_path: Path) -> str:
        """Extract text from PDF files"""
        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}")
            return ""
    
    def _read_docx_file(self, file_path: Path) -> str:
        """Extract text from DOCX files"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            logger.error(f"Error reading DOCX file {file_path}: {e}")
            return ""
    
    def _extract_file_content(self, file_path: Path) -> str:
        """Extract text content from various file types"""
        suffix = file_path.suffix.lower()
        
        if suffix in ['.md', '.txt']:
            return self._read_text_file(file_path)
        elif suffix == '.pdf':
            return self._read_pdf_file(file_path)
        elif suffix == '.docx':
            return self._read_docx_file(file_path)
        else:
            logger.warning(f"Unsupported file type: {suffix}")
            return ""
    
    def _ensure_collection_exists(self, collection_name: str) -> bool:
        """Ensure collection exists, create if it doesn't"""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if collection_name not in collection_names:
                logger.info(f"Creating collection: {collection_name}")
                self.client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(size=self.vector_size, distance=Distance.COSINE)
                )
                logger.info(f"Collection {collection_name} created successfully")
            else:
                logger.debug(f"Collection {collection_name} already exists")
            
            return True
        except Exception as e:
            logger.error(f"Error ensuring collection {collection_name} exists: {e}")
            return False
    
    def _check_file_exists_in_collection(self, collection_name: str, file_path: str, file_hash: str, file_size: int) -> bool:
        """Check if file already exists in collection with same hash and size"""
        try:
            # Search for existing file by path
            search_result = self.client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="file_path",
                            match=MatchValue(value=file_path)
                        )
                    ]
                ),
                limit=1
            )
            
            if search_result[0]:  # If file exists
                existing_point = search_result[0][0]
                existing_hash = existing_point.payload.get("file_hash", "")
                existing_size = existing_point.payload.get("file_size", 0)
                
                # Check if hash and size match
                if existing_hash == file_hash and existing_size == file_size:
                    logger.debug(f"File {file_path} already exists with same content")
                    return True
                else:
                    logger.info(f"File {file_path} exists but content changed (hash/size mismatch)")
                    # Delete old version
                    self.client.delete(
                        collection_name=collection_name,
                        points_selector=[existing_point.id]
                    )
                    return False
            
            return False
        except Exception as e:
            logger.error(f"Error checking file existence in collection: {e}")
            return False

    def push_files_to_collection(self, collection_name: str, output_dir: str = "output") -> Dict[str, Any]:
        """
        Push all .md, .pdf, .docx, and .txt files from output directory to Qdrant collection

        Args:
            collection_name: Name of the Qdrant collection
            output_dir: Directory to scan for files (default: "output")

        Returns:
            Dict with statistics about the operation
        """
        logger.info(f"Starting to push files to collection: {collection_name}")

        # Ensure collection exists
        if not self._ensure_collection_exists(collection_name):
            return {"error": "Failed to create/access collection"}

        stats = {
            "total_files_found": 0,
            "files_processed": 0,
            "files_skipped": 0,
            "files_updated": 0,
            "errors": 0,
            "start_time": datetime.now().isoformat(),
            "collection_name": collection_name
        }

        # Supported file extensions
        supported_extensions = {'.md', '.pdf', '.docx', '.txt'}

        # Recursively find all supported files
        output_path = Path(output_dir)
        if not output_path.exists():
            logger.error(f"Output directory {output_dir} does not exist")
            return {"error": f"Output directory {output_dir} does not exist"}

        all_files = []
        for ext in supported_extensions:
            all_files.extend(output_path.rglob(f"*{ext}"))

        stats["total_files_found"] = len(all_files)
        logger.info(f"Found {len(all_files)} files to process")

        points_to_upsert = []

        for file_path in all_files:
            try:
                logger.debug(f"Processing file: {file_path}")

                # Get file metadata
                file_hash = self._get_file_hash(file_path)
                file_size = self._get_file_size(file_path)
                relative_path = str(file_path.relative_to(output_path))

                if not file_hash:
                    logger.error(f"Could not calculate hash for {file_path}")
                    stats["errors"] += 1
                    continue

                # Check if file already exists with same content
                if self._check_file_exists_in_collection(collection_name, relative_path, file_hash, file_size):
                    logger.debug(f"Skipping unchanged file: {relative_path}")
                    stats["files_skipped"] += 1
                    continue

                # Extract content
                content = self._extract_file_content(file_path)
                if not content.strip():
                    logger.warning(f"No content extracted from {file_path}")
                    stats["errors"] += 1
                    continue

                # Generate embedding
                logger.debug(f"Generating embedding for {relative_path}")
                embedding = self.model.encode(content).tolist()

                # Create point
                point_id = hashlib.md5(relative_path.encode()).hexdigest()
                point = PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload={
                        "file_path": relative_path,
                        "file_name": file_path.name,
                        "file_type": file_path.suffix.lower(),
                        "file_size": file_size,
                        "file_hash": file_hash,
                        "content": content,
                        "content_length": len(content),
                        "processed_at": datetime.now().isoformat(),
                        "collection": collection_name
                    }
                )

                points_to_upsert.append(point)
                stats["files_processed"] += 1

                # Batch upsert every 10 files
                if len(points_to_upsert) >= 10:
                    logger.debug(f"Upserting batch of {len(points_to_upsert)} points")
                    self.client.upsert(
                        collection_name=collection_name,
                        points=points_to_upsert
                    )
                    stats["files_updated"] += len(points_to_upsert)
                    points_to_upsert = []

            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                stats["errors"] += 1

        # Upsert remaining points
        if points_to_upsert:
            logger.debug(f"Upserting final batch of {len(points_to_upsert)} points")
            self.client.upsert(
                collection_name=collection_name,
                points=points_to_upsert
            )
            stats["files_updated"] += len(points_to_upsert)

        stats["end_time"] = datetime.now().isoformat()
        logger.info(f"Completed pushing files to collection {collection_name}")
        logger.info(f"Stats: {stats}")

        return stats

    def query_collection(self,
                        collection_name: str,
                        query_text: str,
                        limit: int = 10,
                        score_threshold: float = 0.0,
                        file_type_filter: Optional[str] = None,
                        file_name_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Query the collection and return results sorted by highest score first

        Args:
            collection_name: Name of the collection to query
            query_text: Text to search for
            limit: Maximum number of results to return (default: 10)
            score_threshold: Minimum score threshold (default: 0.0)
            file_type_filter: Filter by file type (e.g., '.pdf', '.md')
            file_name_filter: Filter by file name (partial match)

        Returns:
            List of results sorted by score (highest first)
        """
        logger.info(f"Querying collection {collection_name} with: '{query_text}'")

        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                logger.error(f"Collection {collection_name} does not exist")
                return []

            # Generate query embedding
            query_embedding = self.model.encode(query_text).tolist()

            # Build filter conditions
            filter_conditions = []
            if file_type_filter:
                filter_conditions.append(
                    FieldCondition(
                        key="file_type",
                        match=MatchValue(value=file_type_filter.lower())
                    )
                )

            if file_name_filter:
                filter_conditions.append(
                    FieldCondition(
                        key="file_name",
                        match=MatchValue(value=file_name_filter)
                    )
                )

            # Create filter object if conditions exist
            search_filter = None
            if filter_conditions:
                search_filter = Filter(must=filter_conditions)

            # Perform search
            search_results = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )

            # Format results
            results = []
            for result in search_results:
                result_data = {
                    "id": result.id,
                    "score": result.score,
                    "file_path": result.payload.get("file_path", ""),
                    "file_name": result.payload.get("file_name", ""),
                    "file_type": result.payload.get("file_type", ""),
                    "file_size": result.payload.get("file_size", 0),
                    "content_length": result.payload.get("content_length", 0),
                    "content": result.payload.get("content", ""),
                    "processed_at": result.payload.get("processed_at", ""),
                    "collection": result.payload.get("collection", "")
                }
                results.append(result_data)

            # Results are already sorted by score (highest first) from Qdrant
            logger.info(f"Found {len(results)} results for query")

            # Log top results for debugging
            for i, result in enumerate(results[:3]):
                logger.debug(f"Result {i+1}: {result['file_name']} (score: {result['score']:.4f})")

            return results

        except Exception as e:
            logger.error(f"Error querying collection {collection_name}: {e}")
            return []


# Convenience functions for easy usage
def push_output_files_to_qdrant(collection_name: str,
                                output_dir: str = "output",
                                qdrant_url: str = "http://localhost:6333") -> Dict[str, Any]:
    """
    Convenience function to push all files from output directory to Qdrant

    Args:
        collection_name: Name of the collection
        output_dir: Output directory path (default: "output")
        qdrant_url: Qdrant server URL

    Returns:
        Statistics dictionary
    """
    manager = QdrantManager(qdrant_url=qdrant_url)
    return manager.push_files_to_collection(collection_name, output_dir)


def query_qdrant_collection(collection_name: str,
                           query_text: str,
                           limit: int = 10,
                           score_threshold: float = 0.0,
                           file_type_filter: Optional[str] = None,
                           file_name_filter: Optional[str] = None,
                           qdrant_url: str = "http://localhost:6333") -> List[Dict[str, Any]]:
    """
    Convenience function to query a Qdrant collection

    Args:
        collection_name: Name of the collection to query
        query_text: Text to search for
        limit: Maximum number of results
        score_threshold: Minimum score threshold
        file_type_filter: Filter by file type
        file_name_filter: Filter by file name
        qdrant_url: Qdrant server URL

    Returns:
        List of results sorted by score (highest first)
    """
    manager = QdrantManager(qdrant_url=qdrant_url)
    return manager.query_collection(
        collection_name=collection_name,
        query_text=query_text,
        limit=limit,
        score_threshold=score_threshold,
        file_type_filter=file_type_filter,
        file_name_filter=file_name_filter
    )
