'use client'

export const runtime = 'edge'

import { ArrowLeft, Save } from 'lucide-react'
import { useParams } from 'next/navigation'
import type React from 'react'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ModelSelect } from '@/components/ui/model-select'
import { ProviderSelect } from '@/components/ui/provider-select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { useAdminAuthGuard } from '@/hooks/useAuthGuard'
import { Link, useRouter } from '@/i18n/navigation'
import { useBotsStore } from '@/stores/bots'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

export default function EditBotPage() {
  const router = useRouter()
  const params = useParams()
  const botId = params.id ? Number.parseInt(params.id as string, 10) : null

  const {
    currentBot,
    fetchBotById,
    updateBot,
    isLoading,
    error,
    clearError,
    setCurrentBot,
  } = useBotsStore()

  // Auth guard
  useAdminAuthGuard()

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    provider: 'openai',
    model: 'gpt-4.1',
    temperature: 0.5,
    isDefault: false,
    systemPrompt: '',
  })

  const [isInitialized, setIsInitialized] = useState(false)
  const [isFetching, setIsFetching] = useState(true)

  // Clear current bot and reset form when bot ID changes
  useEffect(() => {
    setCurrentBot(null)
    setIsInitialized(false)
    setFormData({
      name: '',
      slug: '',
      provider: 'openai',
      model: 'gpt-4.1',
      temperature: 0.5,
      isDefault: false,
      systemPrompt: '',
    })
  }, [botId, setCurrentBot])

  // Fetch bot data on mount
  useEffect(() => {
    if (botId && !Number.isNaN(botId)) {
      fetchBotById(botId).then(() => {
        setIsFetching(false)
      })
    } else {
      setIsFetching(false)
    }
  }, [botId, fetchBotById])

  // Update form data when bot is loaded
  useEffect(() => {
    if (currentBot && !isInitialized) {
      setFormData({
        name: currentBot.name || '',
        slug: currentBot.slug || '',
        provider: currentBot.provider || 'openai',
        model: currentBot.model || 'gpt-4.1',
        temperature: currentBot.temperature || 0.5,
        isDefault: currentBot.isDefault || false,
        systemPrompt: currentBot.systemPrompt || '',
      })
      setIsInitialized(true)
    }
  }, [currentBot, isInitialized])

  const handleInputChange = (
    field: string,
    value: string | number | boolean
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!botId) {
      return
    }

    // Validate form
    if (
      !formData.name ||
      !formData.slug ||
      !formData.provider ||
      !formData.model
    ) {
      return
    }

    const success = await updateBot(botId, formData)

    if (success) {
      router.push('/admin/bots')
    }
  }

  const isFormValid =
    formData.name && formData.slug && formData.provider && formData.model

  if (isFetching) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-gray-500">Loading bot...</p>
        </div>
      </div>
    )
  }

  if (!currentBot && !isFetching) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/bots">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Bots
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Bot Not Found</h1>
            <p className="text-gray-600">
              The requested bot could not be found
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Link href="/admin/bots">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bots
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Edit Bot</h1>
          <p className="text-gray-600">Update bot configuration</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ×
            </Button>
          </div>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Bot Configuration</CardTitle>
          <CardDescription>
            Configure the bot settings and behavior
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Bot Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  placeholder="Enter bot name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={e => handleInputChange('slug', e.target.value)}
                  placeholder="Enter bot slug (URL-friendly)"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="provider">Provider</Label>
                <ProviderSelect
                  value={formData.provider}
                  onValueChange={value => handleInputChange('provider', value)}
                  placeholder="Select provider"
                  includeAzure={false}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <ModelSelect
                  value={formData.model}
                  onValueChange={value => handleInputChange('model', value)}
                  placeholder="Select model"
                  provider={formData.provider}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="temperature">
                  Temperature ({formData.temperature})
                </Label>
                <Input
                  id="temperature"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.temperature}
                  onChange={e =>
                    handleInputChange(
                      'temperature',
                      Number.parseFloat(e.target.value)
                    )
                  }
                  className="w-full"
                />
                <p className="text-sm text-gray-500">
                  Controls randomness: 0.0 = deterministic, 1.0 = very creative
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isDefault"
                  checked={formData.isDefault}
                  onCheckedChange={checked =>
                    handleInputChange('isDefault', checked)
                  }
                />
                <Label htmlFor="isDefault">Set as default bot</Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={formData.systemPrompt}
                onChange={e =>
                  handleInputChange('systemPrompt', e.target.value)
                }
                placeholder="Enter system prompt for the bot..."
                rows={6}
              />
            </div>

            <div className="flex justify-end space-x-4">
              <Link href="/admin/bots">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button type="submit" disabled={!isFormValid || isLoading}>
                <Save className="mr-2 h-4 w-4" />
                {isLoading ? 'Updating...' : 'Update Bot'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
