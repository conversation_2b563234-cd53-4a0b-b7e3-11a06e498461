import type { NextFunction, Request, Response } from 'express'
import { logSecurityEvent } from './security'

// Error types for better categorization
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
}

// Custom error class with security logging
export class SecurityError extends Error {
  public readonly type: ErrorType
  public readonly statusCode: number
  public readonly isOperational: boolean
  public readonly details?: Record<string, any>

  constructor(
    message: string,
    type: ErrorType,
    statusCode = 500,
    isOperational = true,
    details?: Record<string, any>,
  ) {
    super(message)
    this.name = 'SecurityError'
    this.type = type
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.details = details

    // Capture stack trace
    Error.captureStackTrace(this, SecurityError)
  }
}

// Error response interface
interface ErrorResponse {
  error: string
  message: string
  statusCode: number
  timestamp: string
  requestId?: string
  details?: any
}

// Generate unique request ID for tracking
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Enhanced error handler with security logging
export const enhancedErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // Generate request ID for tracking
  const requestId = generateRequestId()

  // Determine error details
  const statusCode = error.statusCode || error.status || 500
  const errorType = error.type || ErrorType.INTERNAL_ERROR
  const isClientError = statusCode >= 400 && statusCode < 500
  const isServerError = statusCode >= 500
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Log security event
  logSecurityEvent({
    event: 'APPLICATION_ERROR_HANDLED',
    level: isServerError ? 'error' : isClientError ? 'warn' : 'info',
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId?.toString(),
    endpoint: req.path,
    method: req.method,
    details: {
      errorType,
      errorMessage: error.message,
      statusCode,
      requestId,
      isOperational: error.isOperational || false,
      hasStack: !!error.stack,
      errorDetails: error.details,
      isClientError,
      isServerError,
    },
  })

  // Log full error details for debugging (separate from security logs)
  console.error(`[ERROR] ${requestId}:`, {
    message: error.message,
    type: errorType,
    statusCode,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
    timestamp: new Date().toISOString(),
    details: error.details,
  })

  // Prepare error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: getErrorTitle(errorType, statusCode),
    message: getErrorMessage(error, errorType, isDevelopment),
    statusCode,
    timestamp: new Date().toISOString(),
    requestId,
  }

  // Add details in development mode
  if (isDevelopment) {
    errorResponse.details = {
      type: errorType,
      stack: error.stack,
      originalMessage: error.message,
      ...(error.details && { errorDetails: error.details }),
    }
  }

  // Send error response
  res.status(statusCode).json(errorResponse)
}

// Get appropriate error title based on type and status
const getErrorTitle = (errorType: ErrorType, statusCode: number): string => {
  switch (errorType) {
    case ErrorType.VALIDATION_ERROR:
      return 'Validation Error'
    case ErrorType.AUTHENTICATION_ERROR:
      return 'Authentication Required'
    case ErrorType.AUTHORIZATION_ERROR:
      return 'Access Denied'
    case ErrorType.RATE_LIMIT_ERROR:
      return 'Rate Limit Exceeded'
    case ErrorType.NOT_FOUND_ERROR:
      return 'Resource Not Found'
    case ErrorType.EXTERNAL_SERVICE_ERROR:
      return 'Service Unavailable'
    case ErrorType.DATABASE_ERROR:
      return 'Database Error'
    default:
      return statusCode >= 500 ? 'Internal Server Error' : 'Bad Request'
  }
}

// Get appropriate error message
const getErrorMessage = (
  error: any,
  errorType: ErrorType,
  isDevelopment: boolean,
): string => {
  if (isDevelopment) {
    return error.message
  }

  // Production-safe error messages
  switch (errorType) {
    case ErrorType.VALIDATION_ERROR:
      return 'The provided data is invalid. Please check your input and try again.'
    case ErrorType.AUTHENTICATION_ERROR:
      return 'Authentication is required to access this resource.'
    case ErrorType.AUTHORIZATION_ERROR:
      return 'You do not have permission to access this resource.'
    case ErrorType.RATE_LIMIT_ERROR:
      return 'Too many requests. Please slow down and try again later.'
    case ErrorType.NOT_FOUND_ERROR:
      return 'The requested resource was not found.'
    case ErrorType.EXTERNAL_SERVICE_ERROR:
      return 'An external service is temporarily unavailable. Please try again later.'
    case ErrorType.DATABASE_ERROR:
      return 'A database error occurred. Please try again later.'
    default:
      return error.statusCode >= 500
        ? 'An internal server error occurred. Please try again later.'
        : 'An error occurred while processing your request.'
  }
}

// 404 handler for unknown routes
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const error = new SecurityError(
    `Route ${req.originalUrl} not found`,
    ErrorType.NOT_FOUND_ERROR,
    404,
    true,
    {
      requestedUrl: req.originalUrl,
      method: req.method,
    },
  )

  next(error)
}

// Async error wrapper to catch async errors
export const asyncErrorHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// Validation error handler
export const validationErrorHandler = (
  errors: string[],
  req: Request,
): SecurityError => {
  return new SecurityError(
    'Validation failed',
    ErrorType.VALIDATION_ERROR,
    400,
    true,
    {
      validationErrors: errors,
      endpoint: req.path,
      method: req.method,
    },
  )
}

// Authentication error handler
export const authenticationErrorHandler = (
  message = 'Authentication required',
): SecurityError => {
  return new SecurityError(message, ErrorType.AUTHENTICATION_ERROR, 401, true)
}

// Authorization error handler
export const authorizationErrorHandler = (
  message = 'Access denied',
): SecurityError => {
  return new SecurityError(message, ErrorType.AUTHORIZATION_ERROR, 403, true)
}

// Rate limit error handler
export const rateLimitErrorHandler = (
  message = 'Rate limit exceeded',
): SecurityError => {
  return new SecurityError(message, ErrorType.RATE_LIMIT_ERROR, 429, true)
}

// External service error handler
export const externalServiceErrorHandler = (
  service: string,
  originalError?: any,
): SecurityError => {
  return new SecurityError(
    `External service error: ${service}`,
    ErrorType.EXTERNAL_SERVICE_ERROR,
    503,
    true,
    {
      service,
      originalError: originalError?.message,
    },
  )
}

// Database error handler
export const databaseErrorHandler = (originalError?: any): SecurityError => {
  return new SecurityError(
    'Database operation failed',
    ErrorType.DATABASE_ERROR,
    500,
    true,
    {
      originalError: originalError?.message,
    },
  )
}

// Graceful shutdown handler
export const gracefulShutdown = (server: any) => {
  const shutdown = (signal: string) => {
    console.log(`\n${signal} received. Starting graceful shutdown...`)

    server.close((err: any) => {
      if (err) {
        console.error('Error during server shutdown:', err)
        process.exit(1)
      }

      console.log('Server closed successfully')
      process.exit(0)
    })

    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error('Forced shutdown after timeout')
      process.exit(1)
    }, 30000)
  }

  process.on('SIGTERM', () => shutdown('SIGTERM'))
  process.on('SIGINT', () => shutdown('SIGINT'))

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error)
    logSecurityEvent({
      event: 'UNCAUGHT_EXCEPTION',
      level: 'critical',
      details: {
        error: error.message,
        stack: error.stack,
      },
    })
    process.exit(1)
  })

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
    logSecurityEvent({
      event: 'UNHANDLED_REJECTION',
      level: 'critical',
      details: {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
      },
    })
    process.exit(1)
  })
}
