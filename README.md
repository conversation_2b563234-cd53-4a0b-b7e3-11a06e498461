# Halal Malaysia AI Chat System

A comprehensive AI-powered chat system for Halal Malaysia with multi-platform integration including web chat, WhatsApp Business API, and Facebook Messenger.

## Features

### 🤖 AI-Powered Chat

- OpenAI gpt-4.1 integration for intelligent responses
- Multi-language support (Bahasa Malaysia, English, Chinese, Tamil)
- Image analysis capabilities
- Voice message transcription
- Context-aware conversations

### 📱 Multi-Platform Integration

- **Web Chat Interface**: Modern, responsive chat widget
- **WhatsApp Business API**: Direct integration with WhatsApp
- **Twilio WhatsApp**: Twilio WhatsApp Business API integration
- **Facebook Messenger**: Native Facebook Messenger support
- **Voice Support**: Push-to-talk and audio file upload
- **Image Support**: Drag & drop image analysis

### 🛠 Admin Dashboard & Agent Support

- Configuration management for all integrations
- Real-time testing tools
- Message history and analytics
- Webhook management
- User session monitoring
- **Multi-role user system**: Users can have multiple roles (Admin, Editor, Agent, Supervisor)
- **Agent handover**: Live chat support with agent assignment
- **Agent management**: Online status, session assignments, and handover controls

### 🔒 Security & Reliability

- JWT-based authentication
- Webhook signature verification
- Rate limiting and error handling
- Secure token management
- HTTPS enforcement

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │    │   (Cloudflare   │    │   Services      │
│                 │    │    Workers)     │    │                 │
│ • Chat UI       │◄──►│ • API Routes    │◄──►│ • OpenAI API    │
│ • Admin Panel   │    │ • Auth System   │    │ • WhatsApp API  │
│ • Voice/Image   │    │ • Integrations  │    │ • Facebook API  │
│   Upload        │    │ • Database      │    │ • File Storage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Node.js 18+ or Bun
- Cloudflare account (for deployment)
- OpenAI API key
- WhatsApp Business API credentials (optional)
- Twilio WhatsApp credentials (optional)
- Facebook Developer account (optional)

### 1. Clone and Install

```bash
git clone <repository-url>
cd halal

# Install dependencies
npm install
# or
bun install
```

### 2. Environment Setup

```bash
# Copy environment files
cp server/.env.example server/.env
cp front/.env.example front/.env.local

# Configure your API keys and settings
```

### 3. Database Setup

```bash
cd server
npm run db:setup
# or
bun run db:setup
```

### 4. Development

```bash
# Start both frontend and backend
npm run dev
# or
bun run dev

# Frontend only (http://localhost:3000)
cd front && npm run dev

# Backend only (http://localhost:16001)
cd server && npm run dev
```

### 5. Admin Access

- Navigate to `http://localhost:3000/admin`
- Default credentials: `admin` / `admin123`
- Configure your integrations in the admin panel

### 6. Agent Access (Optional)

- Navigate to `http://localhost:3000/agent`
- Default agent credentials: `agent1` / `password123`
- Multi-role user example: `superuser` / `password123` (has both ADMIN and AGENT roles)

## Integration Setup

### WhatsApp Business API

See [WHATSAPP_SETUP.md](./WHATSAPP_SETUP.md) for detailed setup instructions.

### Twilio WhatsApp

See [server/TWILIO_README.md](./server/TWILIO_README.md) for detailed setup instructions.

### Facebook Messenger

See [docs/FACEBOOK_MESSENGER_SETUP.md](./docs/FACEBOOK_MESSENGER_SETUP.md) for detailed setup instructions.

### Chat Integration

See [CHAT_INTEGRATION.md](./CHAT_INTEGRATION.md) for embedding the chat widget.

## Deployment

### Cloudflare Workers (Recommended)

```bash
# Deploy backend
cd server
npm run deploy

# Deploy frontend
cd front
npm run deploy
```

### Docker (Alternative)

```bash
# Build and run with Docker
docker-compose up -d
```

## User System

### Multi-Role Architecture

The system uses a unified user table with support for multiple roles per user:

- **ADMIN**: Full system access, user management, configuration
- **EDITOR**: Content management, limited admin access
- **AGENT**: Chat support, session handling
- **SUPERVISOR**: Agent management, advanced chat features

### User Examples

- `admin` (ADMIN role): Full administrative access
- `testadmin` (EDITOR role): Limited admin access
- `agent1`, `agent2` (AGENT role): Chat support agents
- `supervisor1` (SUPERVISOR role): Agent supervisor
- `superuser` (ADMIN + AGENT roles): Multi-role user example

### Role-Based Access

Users can have multiple roles simultaneously, enabling flexible permission systems. For example, a user with both ADMIN and AGENT roles can access both the admin dashboard and handle chat sessions.

## Configuration

### Environment Variables

#### Backend (`server/.env`)

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Database
DATABASE_URL=your_database_url

# WhatsApp (optional)
WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token

# Twilio WhatsApp (optional)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_whatsapp_number

# Facebook (optional)
FACEBOOK_PAGE_ACCESS_TOKEN=your_page_access_token
FACEBOOK_PAGE_ID=your_page_id
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_VERIFY_TOKEN=your_verify_token

# Security
JWT_SECRET=your_jwt_secret
ADMIN_PASSWORD_HASH=your_admin_password_hash

# Webhook
WEBHOOK_BASE_URL=https://your-domain.com
```

#### Frontend (`front/.env.local`)

```bash
NEXT_PUBLIC_API_BASE_URL=https://your-backend-url.workers.dev
```

## API Documentation

### Chat Endpoints

- `POST /api/chat/session` - Initialize chat session
- `POST /api/chat/message` - Send text message
- `POST /api/chat/image` - Send image for analysis
- `GET /api/chat/whatsapp-status` - Get WhatsApp integration status
- `GET /api/chat/facebook-status` - Get Facebook integration status

### Admin Endpoints

- `POST /api/admin/login` - Admin authentication
- `GET/POST /api/admin/whatsapp/config` - WhatsApp configuration
- `GET/POST /api/admin/twilio/config` - Twilio configuration (coming soon)
- `GET/POST /api/admin/facebook/config` - Facebook configuration
- `POST /api/admin/whatsapp/test` - Test WhatsApp integration
- `POST /api/admin/twilio/test` - Test Twilio integration (coming soon)
- `POST /api/admin/facebook/test` - Test Facebook integration
- `GET/POST/PUT/DELETE /api/admin/users` - User management (multi-role support)

### Agent Endpoints

- `POST /api/agent/login` - Agent authentication
- `GET /api/agent/sessions` - Get assigned chat sessions
- `POST /api/agent/sessions/:id/messages` - Send agent message
- `PUT /api/agent/sessions/:id/handover` - Handle session handover

### Webhook Endpoints

- `GET/POST /api/whatsapp/webhook` - WhatsApp webhook
- `GET/POST /api/twilio/webhook` - Twilio webhook
- `GET/POST /api/facebook/webhook` - Facebook webhook

## Development

### Project Structure

```
halal/
├── front/                 # Next.js frontend
│   ├── src/app/          # App router pages
│   ├── src/components/   # React components
│   └── src/lib/          # Utilities
├── server/               # Cloudflare Workers backend
│   ├── src/             # Source code
│   ├── drizzle/         # Database migrations and schema
│   └── scripts/         # Build scripts
├── docs/                # Documentation
└── crawler/             # Web crawling tools
```

### Testing

```bash
# Run frontend tests
cd front && npm test

# Run backend tests
cd server && npm test

# Run integration tests
npm run test:integration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Check the documentation in the `docs/` folder
- Review setup guides for specific integrations
- Open an issue on GitHub
- Contact the development team

## Roadmap

- [ ] Advanced analytics dashboard
- [ ] Multi-tenant support
- [ ] Additional messaging platforms (Telegram, Discord)
- [ ] Advanced AI training capabilities
- [ ] Mobile app integration
- [ ] API rate limiting improvements
- [ ] Enhanced security features

## Changes

- added analytics for selangor/
- fixed product semantic search
