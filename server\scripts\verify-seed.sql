-- Verification queries for seeded data

-- Show all admin users
SELECT 'ADMIN USERS:' as info;
SELECT username, created_at FROM admin_users ORDER BY id;

-- Show WhatsApp config
SELECT 'WHATSAPP CONFIG:' as info;
SELECT phone_number_id, is_active, created_at FROM whatsapp_config;

-- Show message counts by direction
SELECT 'MESSAGE COUNTS:' as info;
SELECT direction, COUNT(*) as count FROM whatsapp_messages GROUP BY direction;

-- Show conversation sessions
SELECT 'CONVERSATION SESSIONS:' as info;
SELECT session_id, COUNT(*) as message_count, 
       MIN(timestamp) as first_message, 
       MAX(timestamp) as last_message
FROM whatsapp_messages 
GROUP BY session_id 
ORDER BY session_id;

-- Show sample conversation from session_001
SELECT 'SAMPLE CONVERSATION (session_001):' as info;
SELECT from_number, to_number, 
       CASE direction 
         WHEN 'inbound' THEN '👤 User: ' 
         WHEN 'outbound' THEN '🤖 Bot: ' 
       END || SUBSTR(content, 1, 80) as message,
       timestamp
FROM whatsapp_messages 
WHERE session_id = 'session_001' 
ORDER BY timestamp;
