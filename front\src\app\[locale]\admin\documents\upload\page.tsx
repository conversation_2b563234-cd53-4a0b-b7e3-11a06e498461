'use client';

export const runtime = 'edge';

import { ArrowLeft, Upload } from 'lucide-react';
import type React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { FileUpload } from '@/components/ui/file-upload';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { Link, useRouter } from '@/i18n/navigation';
import { useDocumentsStore } from '@/stores/documents';
import { useCollectionsStore } from '@/stores/collections';
import type { DocumentFormData } from '@/types/document';
import { CollectionStatus } from '@/types/collection';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function UploadDocumentPage() {
  const router = useRouter();
  const { uploadDocument, isUploading, uploadProgress, error, clearError } = useDocumentsStore();
  const { collections, fetchCollections } = useCollectionsStore();

  // Auth guard
  useAdminAuthGuard();

  const [formData, setFormData] = useState<DocumentFormData>({
    collectionId: 0,
    file: null,
  });

  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  // Filter active collections
  const activeCollections = collections.filter(c => c.status === CollectionStatus.ACTIVE);

  const handleCollectionChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      collectionId: parseInt(value, 10),
    }));
  };

  const handleFileSelect = (file: File) => {
    setFormData(prev => ({
      ...prev,
      file,
    }));
  };

  const handleFileRemove = () => {
    setFormData(prev => ({
      ...prev,
      file: null,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.file || !formData.collectionId) {
      return;
    }

    const result = await uploadDocument(formData.collectionId, formData.file);

    if (result) {
      router.push('/admin/documents');
    }
  };

  const isFormValid = formData.file && formData.collectionId > 0;

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/documents">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Documents
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Upload Document</h1>
          <p className="text-gray-600">Upload a new document to a collection</p>
        </div>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Document Upload</CardTitle>
          <CardDescription>
            Select a collection and upload your document file.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="mt-2"
              >
                Dismiss
              </Button>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="collection">Collection *</Label>
              {activeCollections.length === 0 ? (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-yellow-800 text-sm">
                    No active collections found. 
                    <Link href="/admin/collections/new" className="ml-1 text-yellow-900 underline">
                      Create a collection first
                    </Link>
                  </p>
                </div>
              ) : (
                <>
                  <Select
                    value={formData.collectionId.toString()}
                    onValueChange={handleCollectionChange}
                    disabled={isUploading}
                  >
                    <option value="0">Select a collection...</option>
                    {activeCollections.map((collection) => (
                      <option key={collection.id} value={collection.id.toString()}>
                        {collection.name}
                      </option>
                    ))}
                  </Select>
                  <p className="text-sm text-gray-500">
                    Choose the collection where this document will be stored
                  </p>
                </>
              )}
            </div>

            <div className="space-y-2">
              <Label>Document File *</Label>
              <FileUpload
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                selectedFile={formData.file}
                isUploading={isUploading}
                uploadProgress={uploadProgress}
                error={error}
                disabled={isUploading || activeCollections.length === 0}
              />
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <Button 
                type="submit" 
                disabled={isUploading || !isFormValid || activeCollections.length === 0}
              >
                <Upload className="mr-2 h-4 w-4" />
                {isUploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Upload Document'}
              </Button>
              <Link href="/admin/documents">
                <Button type="button" variant="outline" disabled={isUploading}>
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>

      {activeCollections.length > 0 && (
        <Card className="max-w-2xl mt-6">
          <CardHeader>
            <CardTitle>Available Collections</CardTitle>
            <CardDescription>
              Active collections where you can upload documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {activeCollections.map((collection) => (
                <div key={collection.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <p className="font-medium text-gray-900">{collection.name}</p>
                    <p className="text-sm text-gray-500">
                      {collection._count?.documents || 0} documents
                    </p>
                  </div>
                  <Link href={`/admin/collections/${collection.id}`}>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </Link>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
