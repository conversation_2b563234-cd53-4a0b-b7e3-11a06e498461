import { and, eq } from 'drizzle-orm';
import type { Context } from 'hono';
import { collections } from '@/db/schema';
import { DatabaseService } from '@/services/database';

// Get all collections for a site
export const getCollections = async (c: Context) => {
  try {
    const siteId = c.get('siteId');
    const dbService = new DatabaseService(c.env);
    
    const collectionsData = await dbService.getAllCollections();
    
    return c.json({
      success: true,
      data: collectionsData,
    });
  } catch (error) {
    console.error('Error fetching collections:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to fetch collections',
      },
      500,
    );
  }
};

// Get a single collection by ID
export const getCollection = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10);
    const siteId = c.get('siteId');
    
    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid collection ID',
        },
        400,
      );
    }

    const dbService = new DatabaseService(c.env);
    const collection = await dbService.getCollectionById(id);

    if (!collection) {
      return c.json(
        {
          success: false,
          error: 'Collection not found',
        },
        404,
      );
    }

    return c.json({
      success: true,
      data: collection,
    });
  } catch (error) {
    console.error('Error fetching collection:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to fetch collection',
      },
      500,
    );
  }
};

// Create a new collection
export const createCollection = async (c: Context) => {
  try {
    const { name, status } = await c.req.json();
    const siteId = c.get('siteId');

    if (!name) {
      return c.json(
        {
          success: false,
          error: 'Collection name is required',
        },
        400,
      );
    }

    const dbService = new DatabaseService(c.env);
    const newCollection = await dbService.createCollection({
      name,
      status: status || 'ACTIVE',
    });

    if (!newCollection) {
      return c.json(
        {
          success: false,
          error: 'Failed to create collection',
        },
        500,
      );
    }

    return c.json(
      {
        success: true,
        data: newCollection,
      },
      201,
    );
  } catch (error) {
    console.error('Error creating collection:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to create collection',
      },
      500,
    );
  }
};

// Update a collection
export const updateCollection = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10);
    const { name, status } = await c.req.json();
    const siteId = c.get('siteId');

    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid collection ID',
        },
        400,
      );
    }

    if (!name && !status) {
      return c.json(
        {
          success: false,
          error: 'At least one field (name or status) is required',
        },
        400,
      );
    }

    const dbService = new DatabaseService(c.env);
    const updatedCollection = await dbService.updateCollection(id, {
      name,
      status,
    });

    if (!updatedCollection) {
      return c.json(
        {
          success: false,
          error: 'Collection not found or failed to update',
        },
        404,
      );
    }

    return c.json({
      success: true,
      data: updatedCollection,
    });
  } catch (error) {
    console.error('Error updating collection:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to update collection',
      },
      500,
    );
  }
};

// Delete a collection
export const deleteCollection = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10);
    const siteId = c.get('siteId');

    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid collection ID',
        },
        400,
      );
    }

    const dbService = new DatabaseService(c.env);
    const success = await dbService.deleteCollection(id);

    if (!success) {
      return c.json(
        {
          success: false,
          error: 'Collection not found or failed to delete',
        },
        404,
      );
    }

    return c.json({
      success: true,
      message: 'Collection deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting collection:', error);
    return c.json(
      {
        success: false,
        error: 'Failed to delete collection',
      },
      500,
    );
  }
};
