export enum CollectionStatus {
  ACTIVE = 'ACTIVE',
  DISABLED = 'DISABLED',
}

export interface Collection {
  id: number;
  siteId: number;
  name: string;
  status: CollectionStatus;
  createdAt: string;
  updatedAt: string;
  _count?: {
    documents: number;
  };
}

export interface CollectionCreateRequest {
  name: string;
  status?: CollectionStatus;
}

export interface CollectionUpdateRequest extends Partial<CollectionCreateRequest> {}

export interface CollectionFormData {
  name: string;
  status: CollectionStatus;
}

export interface CollectionListItem extends Collection {
  documentCount: number;
}
