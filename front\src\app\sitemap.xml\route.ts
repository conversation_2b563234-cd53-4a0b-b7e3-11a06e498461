import { NextResponse } from 'next/server'
import { announcements, news } from '@/data/content'
import { ContentManager } from '@/lib/content-manager'
import { generateSitemap } from '@/lib/seo'
export const runtime = 'edge'

export async function GET() {
  try {
    // Generate sitemap pages
    const pages = ContentManager.generateContentSitemap(announcements, news)

    // Generate XML sitemap
    const sitemap = generateSitemap(pages)

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    })
  } catch (error) {
    console.error('Sitemap generation error:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}
