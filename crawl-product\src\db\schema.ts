// Re-export the products table and related schemas from the server

export type { InferInsertModel, InferSelectModel } from "drizzle-orm";
export type { products as ProductsTable } from "../../../server/src/db/schema";
export { products, sites } from "../../../server/src/db/schema";

// Type definitions for our use case
export type Product = InferSelectModel<typeof products>;
export type NewProduct = InferInsertModel<typeof products>;
