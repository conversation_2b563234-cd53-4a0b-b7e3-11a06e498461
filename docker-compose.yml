services:
  # PostgreSQL Database with pgvector
  postgres:
    image: pgvector/pgvector:pg15
    container_name: halalmono-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: halal_chat
      POSTGRES_USER: halal_user
      POSTGRES_PASSWORD: halal_password_2025
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./pgdata:/var/lib/postgresql/data
      - ./server/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./server/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql:ro
    ports:
      - "15633:5432"  # Using non-standard port to avoid conflicts
    networks:
      - halal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U halal_user -d halal_chat"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database (for crawler/search functionality)
  # qdrant:
  #   image: qdrant/qdrant:latest
  #   container_name: halalmono-qdrant
  #   restart: unless-stopped
  #   ports:
  #     - "16333:6333"  # Using non-standard port to avoid conflicts
  #     - "16334:6334"  # gRPC port
  #   volumes:
  #     - qdrant_data:/qdrant/storage
  #   networks:
  #     - halal
  #   environment:
  #     QDRANT__SERVICE__HTTP_PORT: 6333
  #     QDRANT__SERVICE__GRPC_PORT: 6334
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis (for caching and session management)
  redis:
    image: redis:7-alpine
    container_name: halalmono-redis
    restart: unless-stopped
    ports:
      - "16379:6379"  # Using non-standard port to avoid conflicts
    volumes:
      - ./redis:/data
    networks:
      - halal
    command: redis-server --appendonly yes --requirepass redis_password_2025
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis_password_2025", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Server (Hono Node.js API)
  server:
    build:
      context: ./server
    container_name: halalmono-server
    restart: unless-stopped
    ports:
      - "16001:16001"
    env_file:
      - ./server/.env
    environment:
      - NODE_ENV=production
      - PORT=16001
      - DATABASE_URL=*********************************************************/halal_chat
      - REDIS_URL=redis://:redis_password_2025@redis:6379
      - FRONTEND_URL=https://halal2.primalcom.com
      - ADMIN_URL=https://halaladmin.primalcom.com
      - JWT_SECRET=halal-chat-secret-key-2025-docker
      - JWT_EXPIRES_IN=365d
      - ADMIN_DEFAULT_PASSWORD=admin123
      - MAX_FILE_SIZE=10485760
      - UPLOAD_DIR=uploads
      - WEBHOOK_BASE_URL=https://halalapi.primalcom.com
    volumes:
      - ./server/uploads:/app/uploads
      - server_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - halal
      - r2rbackend
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:16001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

  # Database Migration Service (runs migrations and exits)
  migration:
    build:
      context: ./server
    container_name: halalmono-migration
    volumes:
      - ./server/schema.sql:/app/schema.sql:ro
      - ./server/seed.sql:/app/seed.sql:ro    
      - ./server/drizzle:/app/drizzle:ro  
    restart: "on-failure"  # Don't restart - run once and exit
    env_file:
      - ./server/.env
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************************/halal_chat
      - REDIS_URL=redis://:redis_password_2025@redis:6379
      - FRONTEND_URL=https://halal2.primalcom.com
      - ADMIN_URL=https://halaladmin.primalcom.com
      - JWT_SECRET=halal-chat-secret-key-2025-docker
      - JWT_EXPIRES_IN=365d
      - ADMIN_DEFAULT_PASSWORD=admin123
      - MAX_FILE_SIZE=10485760
      - UPLOAD_DIR=uploads
      - WEBHOOK_BASE_URL=https://halalapi.primalcom.com
    command: ["bun", "run", "db:migrate:prod"]
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - halal
      - r2rbackend

  # Frontend (Next.js)
  frontend:
    build:
      context: ./front
      args:
        NEXT_PUBLIC_API_BASE_URL: http://server:16001
        NEXT_PUBLIC_SEARCH_API_URL: http://server:16001/api/search
        NEXT_PUBLIC_EADUAN_URL: https://eaduan.islam.gov.my
        NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL: https://myehalal.halal.gov.my/domestik/v1/
        NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL: https://myehalal.halal.gov.my/international/v1/
    container_name: halalmono-frontend
    restart: unless-stopped
    ports:
      - "16000:16000"
    environment:
      - NODE_ENV=production
    depends_on:
      server:
        condition: service_healthy
    networks:
      - halal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Panel (Next.js)
  admin:
    build:
      context: ./admin
    container_name: halalmono-admin
    restart: unless-stopped
    ports:
      - "16005:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:16001
    depends_on:
      server:
        condition: service_healthy
    networks:
      - halal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Selangor Portal (Next.js)
  selangor:
    build:
      context: ./selangor
    container_name: halalmono-selangor
    restart: unless-stopped
    ports:
      - "16002:3000"
    env_file:
      - ./selangor/.env
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************************/halal_chat
      - NEXT_PUBLIC_API_BASE_URL=https://juara-halal.mercstudio.com
      - R2R_URL=http://r2r:7272
      - R2R_DOCUMENT_COLLECTION_ID=35463504-ab4b-47eb-bf39-5ec2c24fa509
      - R2R_COLLECTION_ID=61c6c856-76fd-475c-a9ee-bc0acde04009
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - halal
      - r2rbackend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
  
  panda:
    image: docker.redpanda.com/redpandadata/redpanda-unstable:v25.1.1-rc1
    container_name: halalmono-redpanda
    restart: unless-stopped
    env_file:
      - .env
    command:
      - redpanda
      - start
      - --kafka-addr
      - PLAINTEXT://0.0.0.0:9092
      - --advertise-kafka-addr
      - PLAINTEXT://127.0.0.1:19092
      - --pandaproxy-addr
      - PLAINTEXT://0.0.0.0:8082
      - --advertise-pandaproxy-addr
      - PLAINTEXT://127.0.0.1:18082
      - --schema-registry-addr
      - http://0.0.0.0:8081
      - --rpc-addr
      - 0.0.0.0:33145
      - --advertise-rpc-addr
      - 127.0.0.1:33145
      - --smp
      - '1'
      - --memory
      - 1G
      - --mode
      - dev-container
      - --default-log-level=info
    volumes:
      - ./redpanda:/var/lib/redpanda/data
      - ./init-redpanda.sh:/docker-entrypoint-initdb.d/init-redpanda.sh 
    ports:
      - "19092:9092"
      - "18081:8081"
      - "18082:8082"
      - "9644:9644"# Admin API and Metrics
    networks:
      - halal
  
  panda-console:
    image: docker.redpanda.com/redpandadata/console:latest
    container_name: halalmono-redpanda-console
    restart: unless-stopped
    ports:
      - "8180:8080" # Expose Console UI on host port 8080
    networks:
      - halal
    env_file:
      - .env
    environment:
      - KAFKA_BROKERS=panda:9092
      # If you enabled SASL, configure it here:
      - KAFKA_SASL_ENABLED=false
      # (Optional) For Schema Registry integration in Console
      - KAFKA_SCHEMA_REGISTRY_URLS=http://panda:8081
      # (Optional) For HTTP Proxy (Pandaproxy) integration in Console
      - KAFKA_PANDAPROXY_URLS=http://panda:8082
    depends_on:
      - panda # Ensure Redpanda starts before Console

volumes:
  server_logs:

networks:
  halal:
    driver: bridge
    name: halal
  r2rbackend:
    external: true
      
