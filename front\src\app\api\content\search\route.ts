import { type NextRequest, NextResponse } from 'next/server'
import { announcements, news } from '@/data/content'
import { ContentManager } from '@/lib/content-manager'
export const runtime = 'edge'
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q')
  const type = searchParams.get('type') // 'announcements', 'news', 'all'
  const category = searchParams.get('category')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  const featured = searchParams.get('featured')
  const page = Number.parseInt(searchParams.get('page') || '1')
  const limit = Number.parseInt(searchParams.get('limit') || '10')
  const sortBy = searchParams.get('sortBy') || 'date'
  const sortOrder = searchParams.get('sortOrder') || 'desc'
  const includeContent = searchParams.get('includeContent') === 'true'

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is required' },
      { status: 400 }
    )
  }

  try {
    let results: any[] = []

    // Search announcements
    if (!type || type === 'announcements' || type === 'all') {
      const announcementResults = ContentManager.searchContent(
        query,
        announcements,
        [],
        { limit: type === 'announcements' ? limit * 2 : limit, includeContent }
      )
      results = [...results, ...announcementResults]
    }

    // Search news
    if (!type || type === 'news' || type === 'all') {
      const newsResults = ContentManager.searchContent(query, [], news, {
        limit: type === 'news' ? limit * 2 : limit,
        includeContent,
      })
      results = [...results, ...newsResults]
    }

    // Apply additional filters
    if (category) {
      results = results.filter(item => {
        if (item.type === 'announcement') {
          const announcement = announcements.find(a => a.id === item.id)
          return announcement?.category === category
        }
        if (item.type === 'news') {
          const newsItem = news.find(n => n.id === item.id)
          return (
            newsItem?.category === category || newsItem?.categoryBM === category
          )
        }
        return false
      })
    }

    if (dateFrom) {
      results = results.filter(item => item.date >= dateFrom)
    }

    if (dateTo) {
      results = results.filter(item => item.date <= dateTo)
    }

    if (featured !== null && featured !== undefined) {
      const isFeatured = featured === 'true'
      results = results.filter(item => {
        if (item.type === 'announcement') {
          const announcement = announcements.find(a => a.id === item.id)
          return !!announcement?.featured === isFeatured
        }
        return false // News items don't have featured flag in current structure
      })
    }

    // Sort results
    results.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date)
          bValue = new Date(b.date)
          break
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'relevance':
          aValue = a.relevance || 0
          bValue = b.relevance || 0
          break
        default:
          return 0
      }

      if (sortOrder === 'desc') {
        if (aValue < bValue) {
          return 1
        }
        if (aValue > bValue) {
          return -1
        }
      } else {
        if (aValue < bValue) {
          return -1
        }
        if (aValue > bValue) {
          return 1
        }
      }
      return 0
    })

    // Apply pagination
    const total = results.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedResults = results.slice(startIndex, endIndex)

    return NextResponse.json({
      results: paginatedResults,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      query,
      filters: {
        type,
        category,
        dateFrom,
        dateTo,
        featured,
        sortBy,
        sortOrder,
        includeContent,
      },
    })
  } catch (error) {
    console.error('Content search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST endpoint for advanced search with complex filters
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      query,
      filters = {},
      sort = { field: 'date', order: 'desc' },
      pagination = { page: 1, limit: 10 },
      includeContent = false,
    } = body

    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 })
    }

    let results: any[] = []

    // Search announcements
    if (
      !filters.type ||
      filters.type === 'announcements' ||
      filters.type === 'all'
    ) {
      const announcementFilter = {
        category: filters.category,
        featured: filters.featured,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        search: query,
        tags: filters.tags,
      }

      const announcementResults = ContentManager.filterAnnouncements(
        announcements,
        announcementFilter,
        sort,
        { page: 1, limit: 1000 } // Get all for search, then paginate
      )

      results = [
        ...results,
        ...announcementResults.items.map(item => ({
          type: 'announcement',
          id: item.id,
          title: item.title,
          titleBM: item.titleBM,
          excerpt: `${item.content.substring(0, 200)}...`,
          date: item.date,
          url: `/announcements/${item.id}`,
          category: item.category,
          featured: item.featured,
          relevance: 10, // Base relevance for announcements
        })),
      ]
    }

    // Search news
    if (!filters.type || filters.type === 'news' || filters.type === 'all') {
      const newsFilter = {
        category: filters.category,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        search: query,
        tags: filters.tags,
      }

      const newsResults = ContentManager.filterNews(
        news,
        newsFilter,
        sort,
        { page: 1, limit: 1000 } // Get all for search, then paginate
      )

      results = [
        ...results,
        ...newsResults.items.map(item => ({
          type: 'news',
          id: item.id,
          title: item.title,
          titleBM: item.titleBM,
          excerpt: item.excerpt,
          date: item.date,
          url: `/news/${item.id}`,
          category: item.category,
          relevance: 8, // Base relevance for news
        })),
      ]
    }

    // Calculate relevance scores
    const queryLower = query.toLowerCase()
    results.forEach(item => {
      let relevance = item.relevance || 0

      // Title exact match
      if (item.title.toLowerCase() === queryLower) {
        relevance += 20
      } else if (item.title.toLowerCase().includes(queryLower)) {
        relevance += 10
      }

      // Title BM exact match
      if (item.titleBM?.toLowerCase() === queryLower) {
        relevance += 20
      } else if (item.titleBM?.toLowerCase().includes(queryLower)) {
        relevance += 10
      }

      // Excerpt match
      if (item.excerpt.toLowerCase().includes(queryLower)) {
        relevance += 5
      }

      // Featured content boost
      if (item.featured) {
        relevance += 5
      }

      // Recent content boost
      const daysSincePublished =
        (Date.now() - new Date(item.date).getTime()) / (1000 * 60 * 60 * 24)
      if (daysSincePublished <= 7) {
        relevance += 3
      } else if (daysSincePublished <= 30) {
        relevance += 1
      }

      item.relevance = relevance
    })

    // Sort by relevance and then by date
    results.sort((a, b) => {
      if (a.relevance !== b.relevance) {
        return b.relevance - a.relevance
      }
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })

    // Apply pagination
    const total = results.length
    const totalPages = Math.ceil(total / pagination.limit)
    const startIndex = (pagination.page - 1) * pagination.limit
    const endIndex = startIndex + pagination.limit
    const paginatedResults = results.slice(startIndex, endIndex)

    return NextResponse.json({
      results: paginatedResults,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1,
      },
      query,
      filters,
      sort,
    })
  } catch (error) {
    console.error('Advanced content search error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
