const postgres = require('postgres').default

const sql = postgres(
  'postgresql://halal_user:halal_password_2025@localhost:15633/halal_chat',
)

async function checkDatabaseState() {
  try {
    console.log('🔍 Checking database state...\n')

    // Check if database exists and is accessible
    const dbInfo = await sql`SELECT current_database(), current_user, version()`
    console.log('📊 Database Info:')
    console.log(`Database: ${dbInfo[0].current_database}`)
    console.log(`User: ${dbInfo[0].current_user}`)
    console.log(
      `Version: ${dbInfo[0].version.split(' ').slice(0, 2).join(' ')}\n`,
    )

    // List all tables in all schemas
    const allTables = await sql`
      SELECT table_name, table_schema
      FROM information_schema.tables
      WHERE table_schema IN ('public', 'drizzle')
      ORDER BY table_schema, table_name
    `

    console.log('📋 All Tables:')
    if (allTables.length === 0) {
      console.log('  No tables found')
    } else {
      let currentSchema = ''
      allTables.forEach((table) => {
        if (table.table_schema !== currentSchema) {
          currentSchema = table.table_schema
          console.log(`  ${currentSchema} schema:`)
        }
        console.log(`    - ${table.table_name}`)
      })
    }
    console.log('')

    // Check migration status
    try {
      const migrations = await sql`
        SELECT hash, created_at FROM drizzle.__drizzle_migrations
        ORDER BY id
      `

      console.log('🔄 Migration Status:')
      if (migrations.length === 0) {
        console.log('  No migrations found')
      } else {
        migrations.forEach((migration) => {
          console.log(`  - ${migration.hash} (${migration.created_at})`)
        })
      }
    } catch (error) {
      console.log('⚠️  Migration table not accessible:', error.message)
    }

    console.log('')

    // Check for specific tables that should exist
    const expectedTables = [
      'products',
      'companies',
      'categories',
      'product_categories',
      'company_categories',
    ]
    console.log('🎯 Expected Tables Status:')

    for (const tableName of expectedTables) {
      try {
        const result = await sql`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_name = ${tableName}
        `

        const exists = result[0].count > 0
        console.log(`  - ${tableName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`)

        if (exists) {
          const rowCount =
            await sql`SELECT COUNT(*) as count FROM ${sql(tableName)}`
          console.log(`    Rows: ${rowCount[0].count}`)
        }
      } catch (error) {
        console.log(`  - ${tableName}: ❌ ERROR - ${error.message}`)
      }
    }
  } catch (error) {
    console.error('❌ Error checking database state:', error.message)
    console.error('Full error:', error)
  } finally {
    await sql.end()
  }
}

checkDatabaseState()
