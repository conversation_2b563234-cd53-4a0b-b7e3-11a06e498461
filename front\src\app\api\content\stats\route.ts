import { type NextRequest, NextResponse } from 'next/server'
import { announcements, news } from '@/data/content'
import { ContentManager } from '@/lib/content-manager'
export const runtime = 'edge'
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const period = searchParams.get('period') || 'month' // 'week', 'month', 'quarter', 'year'
  const includeCategories = searchParams.get('includeCategories') === 'true'
  const includeTrends = searchParams.get('includeTrends') === 'true'

  try {
    // Get basic content statistics
    const stats = ContentManager.getContentStats(announcements, news)

    // Calculate period-specific statistics
    const now = new Date()
    let startDate: Date

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'quarter':
        startDate = new Date(
          now.getFullYear(),
          Math.floor(now.getMonth() / 3) * 3,
          1
        )
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1)
        break
      default: // month
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
    }

    const periodAnnouncements = announcements.filter(
      item => new Date(item.date) >= startDate
    )
    const periodNews = news.filter(item => new Date(item.date) >= startDate)

    // Category breakdown
    let categoryStats = {}
    if (includeCategories) {
      const announcementCategories = announcements.reduce(
        (acc, item) => {
          acc[item.category] = (acc[item.category] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      const newsCategories = news.reduce(
        (acc, item) => {
          acc[item.category] = (acc[item.category] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      categoryStats = {
        announcements: announcementCategories,
        news: newsCategories,
      }
    }

    // Trend analysis
    let trends = {}
    if (includeTrends) {
      const last6Months = Array.from({ length: 6 }, (_, i) => {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
        return {
          month: date.toISOString().slice(0, 7), // YYYY-MM format
          announcements: announcements.filter(item =>
            item.date.startsWith(date.toISOString().slice(0, 7))
          ).length,
          news: news.filter(item =>
            item.date.startsWith(date.toISOString().slice(0, 7))
          ).length,
        }
      }).reverse()

      trends = {
        monthly: last6Months,
        growth: {
          announcements: calculateGrowthRate(
            last6Months.map(m => m.announcements)
          ),
          news: calculateGrowthRate(last6Months.map(m => m.news)),
        },
      }
    }

    // Recent activity
    const recentActivity = [
      ...announcements.slice(0, 5).map(item => ({
        type: 'announcement',
        id: item.id,
        title: item.title,
        date: item.date,
        category: item.category,
      })),
      ...news.slice(0, 5).map(item => ({
        type: 'news',
        id: item.id,
        title: item.title,
        date: item.date,
        category: item.category,
      })),
    ]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 10)

    return NextResponse.json({
      overview: {
        total: {
          announcements: stats.announcements.total,
          news: stats.news.total,
          content: stats.announcements.total + stats.news.total,
        },
        period: {
          announcements: periodAnnouncements.length,
          news: periodNews.length,
          content: periodAnnouncements.length + periodNews.length,
          timeframe: period,
        },
        featured: {
          announcements: stats.announcements.featured,
        },
      },
      categories: includeCategories ? categoryStats : undefined,
      trends: includeTrends ? trends : undefined,
      recentActivity,
      metadata: {
        lastUpdated: new Date().toISOString(),
        period,
        dataRange: {
          announcements: {
            earliest:
              announcements.length > 0
                ? announcements[announcements.length - 1].date
                : null,
            latest: announcements.length > 0 ? announcements[0].date : null,
          },
          news: {
            earliest: news.length > 0 ? news[news.length - 1].date : null,
            latest: news.length > 0 ? news[0].date : null,
          },
        },
      },
    })
  } catch (error) {
    console.error('Content stats API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function calculateGrowthRate(values: number[]): number {
  if (values.length < 2) {
    return 0
  }

  const current = values[values.length - 1]
  const previous = values[values.length - 2]

  if (previous === 0) {
    return current > 0 ? 100 : 0
  }

  return Math.round(((current - previous) / previous) * 100)
}
