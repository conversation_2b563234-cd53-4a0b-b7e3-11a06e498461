export interface Document {
  id: number;
  collectionId: number;
  s3ConfigurationId: number;
  s3Key: string;
  filename: string;
  filesize?: number | null;
  mimetype?: string | null;
  createdAt: string;
  updatedAt: string;
  // Joined fields for display
  collectionName?: string;
  s3ConfigurationServiceName?: string;
}

export interface DocumentCreateRequest {
  collectionId: number;
  s3ConfigurationId: number;
  s3Key: string;
  filename: string;
  filesize?: number;
  mimetype?: string;
}

export interface DocumentUpdateRequest extends Partial<Omit<DocumentCreateRequest, 'collectionId' | 's3ConfigurationId' | 's3Key'>> {}

export interface DocumentFormData {
  collectionId: number;
  file: File | null;
  filename?: string;
}

export interface DocumentListItem extends Document {
  downloadUrl?: string;
  fileTypeIcon?: string;
}

export interface DocumentUploadResponse {
  type: 'document';
  url: string;
  s3Key: string;
  originalFilename: string;
  size: number;
  mimetype: string;
}

// Supported file types for document upload
export const SUPPORTED_FILE_TYPES = {
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/pdf': '.pdf',
  'text/plain': '.txt',
  'text/markdown': '.md',
  'text/asciidoc': '.asciidoc',
  'audio/mpeg': '.mp3',
  'audio/wav': '.wav',
  'text/csv': '.csv',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
} as const;

export const SUPPORTED_FILE_EXTENSIONS = Object.values(SUPPORTED_FILE_TYPES);

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

export function validateFile(file: File): FileValidationResult {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)}MB limit`,
    };
  }

  // Check file type
  if (!Object.keys(SUPPORTED_FILE_TYPES).includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not supported. Supported types: ${SUPPORTED_FILE_EXTENSIONS.join(', ')}`,
    };
  }

  return { isValid: true };
}

export function getFileTypeIcon(mimetype: string): string {
  switch (mimetype) {
    case 'application/pdf':
      return '📄';
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return '📝';
    case 'text/plain':
    case 'text/markdown':
    case 'text/asciidoc':
      return '📄';
    case 'audio/mpeg':
    case 'audio/wav':
      return '🎵';
    case 'text/csv':
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return '📊';
    default:
      return '📄';
  }
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
