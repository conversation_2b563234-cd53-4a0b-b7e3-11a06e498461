{"name": "halal-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 16005", "build": "next build", "start": "next start", "lint": "eslint --fix .", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy": "wrangler pages deploy out", "preview": "wrangler pages dev out"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^3.6.0", "lucide-react": "^0.363.0", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.51.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.3.4", "eslint-plugin-react": "^7.37.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5", "wrangler": "^3.48.0"}}