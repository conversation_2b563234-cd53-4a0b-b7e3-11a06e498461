import type { Context } from 'hono'
import DatabaseService from '@/services/database'
import type {
  Contact,
  ContactCreationRequest,
  ContactUpdateRequest,
} from '@/types'

// Get all contacts with pagination and filtering
export const getContacts = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '12')
    const search = c.req.query('search')
    const type = c.req.query('type')
    const department = c.req.query('department')

    const dbService = new DatabaseService(c.env)
    const result = await dbService.getContacts(siteId, {
      page,
      limit,
      search,
      type,
      department,
    })

    return c.json({
      success: true,
      data: {
        contacts: result.contacts,
        pagination: result.pagination,
      },
    })
  } catch (error) {
    console.error('Error fetching contacts:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to fetch contacts',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
}

// Get single contact by ID
export const getContact = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const id = Number.parseInt(c.req.param('id'))

    if (Number.isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid contact ID',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const contact = await dbService.getContactById(siteId, id)

    if (!contact) {
      return c.json(
        {
          success: false,
          error: 'Contact not found',
        },
        404,
      )
    }

    return c.json({
      success: true,
      data: contact,
    })
  } catch (error) {
    console.error('Error fetching contact:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to fetch contact',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
}

// Create new contact
export const createContact = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const body = await c.req.json<ContactCreationRequest>()

    // Validate required fields
    if (!body.name || !body.email) {
      return c.json(
        {
          success: false,
          error: 'Name and email are required',
        },
        400,
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return c.json(
        {
          success: false,
          error: 'Invalid email format',
        },
        400,
      )
    }

    // Validate type
    const validTypes = ['general', 'support', 'sales', 'technical', 'emergency']
    if (!validTypes.includes(body.type)) {
      return c.json(
        {
          success: false,
          error: 'Invalid contact type',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const contact = await dbService.createContact(siteId, body)

    return c.json(
      {
        success: true,
        data: contact,
        message: 'Contact created successfully',
      },
      201,
    )
  } catch (error) {
    console.error('Error creating contact:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to create contact',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
}

// Update contact
export const updateContact = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const id = Number.parseInt(c.req.param('id'))
    const body = await c.req.json<ContactUpdateRequest>()

    if (Number.isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid contact ID',
        },
        400,
      )
    }

    // Validate email format if provided
    if (body.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(body.email)) {
        return c.json(
          {
            success: false,
            error: 'Invalid email format',
          },
          400,
        )
      }
    }

    // Validate type if provided
    if (body.type) {
      const validTypes = [
        'general',
        'support',
        'sales',
        'technical',
        'emergency',
      ]
      if (!validTypes.includes(body.type)) {
        return c.json(
          {
            success: false,
            error: 'Invalid contact type',
          },
          400,
        )
      }
    }

    const dbService = new DatabaseService(c.env)
    const contact = await dbService.updateContact(siteId, id, body)

    if (!contact) {
      return c.json(
        {
          success: false,
          error: 'Contact not found',
        },
        404,
      )
    }

    return c.json({
      success: true,
      data: contact,
      message: 'Contact updated successfully',
    })
  } catch (error) {
    console.error('Error updating contact:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to update contact',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
}

// Delete contact
export const deleteContact = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const id = Number.parseInt(c.req.param('id'))

    if (Number.isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid contact ID',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const success = await dbService.deleteContact(siteId, id)

    if (!success) {
      return c.json(
        {
          success: false,
          error: 'Contact not found',
        },
        404,
      )
    }

    return c.json({
      success: true,
      message: 'Contact deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting contact:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to delete contact',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      500,
    )
  }
}
