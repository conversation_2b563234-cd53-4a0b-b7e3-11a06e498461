CREATE TYPE "public"."service_type" AS ENUM('R2R_RAG', 'SMTP_PROVIDER', 'EXTERNAL_API');--> statement-breakpoint
CREATE TABLE "services" (
	"id" serial PRIMARY KEY NOT NULL,
	"site_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "service_type" NOT NULL,
	"description" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"configuration" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "services" ADD CONSTRAINT "services_site_id_sites_id_fk" FOREIGN KEY ("site_id") REFERENCES "public"."sites"("id") ON DELETE cascade ON UPDATE no action;