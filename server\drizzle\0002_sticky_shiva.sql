CREATE TABLE "products" (
	"id" serial PRIMARY KEY NOT NULL,
	"site_id" integer NOT NULL,
	"product_name" varchar(500) NOT NULL,
	"company_name" varchar(500) NOT NULL,
	"certificate_number" varchar(255),
	"certificate_type" varchar(255),
	"issued_date" varchar(255),
	"expiry_date" varchar(255),
	"status" varchar(100),
	"category" varchar(255),
	"subcategory" varchar(255),
	"address" text,
	"state" varchar(255),
	"country" varchar(255),
	"contact_info" text,
	"website" varchar(500),
	"source_url" varchar(1000),
	"raw_data" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
