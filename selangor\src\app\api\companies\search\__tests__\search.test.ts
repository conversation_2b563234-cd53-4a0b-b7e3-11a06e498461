// Mock the schema import
jest.mock('@/lib/db/schema', () => ({
  companies: 'mocked-companies-table',
  sites: 'mocked-sites-table',
}))

// Mock the database
jest.mock('@/lib/db', () => {
  const mockDb = {
    select: jest.fn().mockReturnValue({
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            offset: jest.fn().mockReturnValue({
              orderBy: jest.fn().mockResolvedValue([
                {
                  id: 1,
                  companyName: 'Halal Food Industries Sdn Bhd',
                  registrationNumber: 'ROC-123456',
                  businessType: 'Manufacturing',
                  category: 'Food Processing',
                  subcategory: 'Meat Products',
                  address: '123 Jalan Halal, Selangor',
                  state: 'Selangor',
                  postcode: '40000',
                  city: 'Shah Alam',
                  country: 'Malaysia',
                  phone: '+***********',
                  email: '<EMAIL>',
                  website: 'https://halalfood.com',
                  contactPerson: '<PERSON>',
                  certificateNumber: 'JAKIM-COMP-001',
                  certificateType: 'HALAL',
                  certificateStatus: 'Active',
                  issuedDate: '2023-01-01',
                  expiryDate: '2025-12-31',
                  sourceUrl: 'https://halal.gov.my',
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              ]),
            }),
          }),
        }),
      }),
    }),
  }

  // Mock the count function
  mockDb.select.mockImplementation(fields => {
    if (fields && fields.count) {
      return {
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([{ count: 1 }]),
        }),
      }
    }
    return {
      from: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            offset: jest.fn().mockReturnValue({
              orderBy: jest.fn().mockResolvedValue([
                {
                  id: 1,
                  companyName: 'Halal Food Industries Sdn Bhd',
                  registrationNumber: 'ROC-123456',
                  businessType: 'Manufacturing',
                  category: 'Food Processing',
                  subcategory: 'Meat Products',
                  address: '123 Jalan Halal, Selangor',
                  state: 'Selangor',
                  postcode: '40000',
                  city: 'Shah Alam',
                  country: 'Malaysia',
                  phone: '+***********',
                  email: '<EMAIL>',
                  website: 'https://halalfood.com',
                  contactPerson: 'Ahmad bin Ali',
                  certificateNumber: 'JAKIM-COMP-001',
                  certificateType: 'HALAL',
                  certificateStatus: 'Active',
                  issuedDate: '2023-01-01',
                  expiryDate: '2025-12-31',
                  sourceUrl: 'https://halal.gov.my',
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              ]),
            }),
          }),
        }),
      }),
    }
  })

  return { db: mockDb }
})

// Mock drizzle-orm functions
jest.mock('drizzle-orm', () => ({
  ilike: jest.fn((field, value) => ({ field, value, type: 'ilike' })),
  or: jest.fn((...conditions) => ({ conditions, type: 'or' })),
  count: jest.fn(() => ({ type: 'count' })),
}))

// Mock Next.js
jest.mock('next/server', () => ({
  NextRequest: jest.fn().mockImplementation(url => ({
    url: url,
    method: 'GET',
  })),
  NextResponse: {
    json: jest.fn((data, options) => ({
      json: () => Promise.resolve(data),
      status: options?.status || 200,
    })),
  },
}))

import { NextRequest } from 'next/server'
import { GET } from '../route'

describe('/api/companies/search', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return company search results', async () => {
    const url =
      'http://localhost:3000/api/companies/search?q=Halal&page=1&limit=10'
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(data).toHaveProperty('companies')
    expect(data).toHaveProperty('pagination')
    expect(data).toHaveProperty('query')
    expect(data.companies).toHaveLength(1)
    expect(data.companies[0]).toHaveProperty(
      'name',
      'Halal Food Industries Sdn Bhd'
    )
    expect(data.companies[0]).toHaveProperty('registrationNumber', 'ROC-123456')
    expect(data.companies[0]).toHaveProperty('businessType', 'Manufacturing')
    expect(data.pagination).toHaveProperty('page', 1)
    expect(data.pagination).toHaveProperty('limit', 10)
    expect(data.pagination).toHaveProperty('total', 1)
  })

  it('should return 400 for missing query parameter', async () => {
    const url = 'http://localhost:3000/api/companies/search'
    const request = new NextRequest(url)

    const response = await GET(request)

    expect(response.status).toBe(400)
  })

  it('should handle pagination correctly', async () => {
    const url =
      'http://localhost:3000/api/companies/search?q=test&page=2&limit=5'
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(data.pagination.page).toBe(2)
    expect(data.pagination.limit).toBe(5)
  })

  it('should search by company name', async () => {
    const url =
      'http://localhost:3000/api/companies/search?q=Halal Food&page=1&limit=10'
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(data.companies[0].name).toContain('Halal')
  })

  it('should search by registration number', async () => {
    const url =
      'http://localhost:3000/api/companies/search?q=ROC-123456&page=1&limit=10'
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(data.companies[0].registrationNumber).toBe('ROC-123456')
  })

  it('should search by address', async () => {
    const url =
      'http://localhost:3000/api/companies/search?q=Jalan Halal&page=1&limit=10'
    const request = new NextRequest(url)

    const response = await GET(request)
    const data = await response.json()

    expect(data.companies[0].address).toContain('Jalan Halal')
  })
})
