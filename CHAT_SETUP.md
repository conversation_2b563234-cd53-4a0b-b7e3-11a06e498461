# Halal Chat Application Setup Guide

This guide will help you set up the complete chat application with Express.js backend and Next.js frontend.

## Overview

The application consists of:

- **Backend**: Express.js server with OpenAI integration (`/server`)
- **Frontend**: Next.js application with chat interface (`/front`)
- **WhatsApp Integration**: WhatsApp Business API support with admin panel

## Prerequisites

- Node.js 18 or higher
- npm or yarn
- OpenAI API key (get one from https://platform.openai.com/api-keys)
- WhatsApp Business API access (optional, for WhatsApp integration)

## Quick Start

### 1. Set up the Backend Server

```bash
# Navigate to server directory
cd server

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Edit .env file and add your OpenAI API key
# OPENAI_API_KEY=your_openai_api_key_here
```

### 2. Set up the Frontend

```bash
# Navigate to frontend directory (from project root)
cd front

# Dependencies should already be installed
# If not, run: npm install
```

### 3. Start the Applications

**Terminal 1 - Start the backend server:**

```bash
cd server
npm run dev
```

Server will start on http://localhost:3001

**Terminal 2 - Start the frontend:**

```bash
cd front
npm run dev
```

Frontend will start on http://localhost:3000

### 4. Access the Chat

Open your browser and go to:

- Main site: http://localhost:3000
- Chat directly: http://localhost:3000/chat

## Features

### 🗣️ Voice Input (Push-to-Talk)

- Click and hold the microphone button to record
- Release to stop recording and transcribe
- Uses OpenAI Whisper for transcription

### 💬 Text Chat

- Type messages and get AI responses
- Powered by OpenAI gpt-4.1
- Maintains conversation context

### 🖼️ Image Upload

- Drag and drop images or click to upload
- Supports JPEG, PNG, GIF, WebP
- AI analyzes images using gpt-4.1 Vision

### 📱 Responsive Design

- Works on desktop, tablet, and mobile
- Touch-friendly interface
- Optimized for all screen sizes

## Configuration

### Backend Configuration (server/.env)

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional (with defaults)
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
```

### Frontend Configuration

The frontend automatically connects to the backend at `http://localhost:16001`. If you change the backend port, update the `NEXT_PUBLIC_API_BASE_URL` environment variable.

## API Endpoints

### Chat Endpoints

- `POST /api/chat/session` - Create new chat session
- `POST /api/chat/message` - Send text message
- `POST /api/chat/image` - Analyze image
- `GET /api/chat/session/:id` - Get chat history
- `DELETE /api/chat/session/:id` - Clear chat

### Upload Endpoints

- `POST /api/upload` - Upload image or audio file
- `DELETE /api/upload/:filename` - Delete uploaded file

### Health Check

- `GET /health` - Server health status

### WhatsApp Integration

- `GET /api/whatsapp/webhook` - Webhook verification
- `POST /api/whatsapp/webhook` - Receive WhatsApp messages
- `GET /api/chat/whatsapp-status` - Get WhatsApp integration status

### Admin Panel

- `POST /api/admin/login` - Admin authentication
- `GET /api/admin/whatsapp/config` - WhatsApp configuration
- `POST /api/admin/whatsapp/test-message` - Send test messages

## File Upload Limits

- **Maximum file size**: 10MB
- **Supported image formats**: JPEG, PNG, GIF, WebP
- **Supported audio formats**: MP3, WAV, WebM, OGG

## WhatsApp Business API Integration

For detailed WhatsApp setup instructions, see [WHATSAPP_SETUP.md](./WHATSAPP_SETUP.md).

### Quick WhatsApp Setup

1. **Access Admin Panel**

   ```
   http://localhost:16000/admin
   Username: admin
   Password: admin123
   ```

2. **Configure WhatsApp**
   - Enter your WhatsApp Business API credentials
   - Copy the webhook URL
   - Configure webhook in Meta Developer Console

3. **Test Integration**
   - Use the test interface to send messages
   - Send a WhatsApp message to verify

## Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Make sure you've set `OPENAI_API_KEY` in `server/.env`
   - Restart the server after adding the key

2. **"CORS error"**
   - Ensure the backend is running on port 3001
   - Check that `FRONTEND_URL` in `.env` matches your frontend URL

3. **"File upload failed"**
   - Check file size (must be under 10MB)
   - Ensure file format is supported
   - Verify the uploads directory exists and is writable

4. **"Microphone not working"**
   - Grant microphone permissions in your browser
   - Use HTTPS in production (required for microphone access)

### Development Tips

1. **Check server logs** for detailed error messages
2. **Use browser dev tools** to inspect network requests
3. **Test with small files** first for uploads
4. **Verify OpenAI API quota** if getting API errors

## Production Deployment

### Backend

1. Set `NODE_ENV=production`
2. Use a process manager like PM2
3. Set up proper logging
4. Configure reverse proxy (nginx)
5. Use HTTPS

### Frontend

1. Build the application: `npm run build`
2. Deploy to Vercel, Netlify, or your preferred platform
3. Update API base URL for production

## Security Considerations

- Rate limiting is enabled (100 requests per 15 minutes)
- File uploads are validated and size-limited
- CORS is configured for specific origins
- Error messages are sanitized in production

## Support

For issues or questions:

1. Check the server logs for error details
2. Verify your OpenAI API key and quota
3. Ensure all dependencies are installed correctly
4. Test with a simple text message first

## License

MIT License - feel free to modify and use as needed.
