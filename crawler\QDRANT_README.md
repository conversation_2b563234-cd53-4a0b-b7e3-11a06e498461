# Qdrant Manager for Document Processing

This module provides functionality to push crawled documents (.md, .pdf, .docx, .txt) to Qdrant vector database and query them using semantic search.

## Features

- **Recursive file scanning**: Automatically finds all supported files in the output directory
- **Duplicate detection**: Checks file hash and size to avoid re-processing unchanged files
- **Content extraction**: Supports text extraction from PDF, DOCX, Markdown, and text files
- **Semantic search**: Uses sentence transformers for generating embeddings
- **Batch processing**: Efficiently processes files in batches
- **Comprehensive logging**: Detailed debug information and statistics
- **Flexible querying**: Support for filters, score thresholds, and result limits

## Installation

Install the required dependencies:

```bash
pip install qdrant-client sentence-transformers PyPDF2 python-docx
```

Or install from requirements.txt:

```bash
pip install -r requirements.txt
```

## Setup Qdrant

Start Qdrant server using Docker:

```bash
docker run -p 6333:6333 qdrant/qdrant
```

## Usage

### 1. Push Files to Collection

```python
from qdrant_manager import push_output_files_to_qdrant

# Push all files from output directory to collection
stats = push_output_files_to_qdrant(
    collection_name="my_documents",
    output_dir="output",  # Directory containing crawled files
    qdrant_url="http://localhost:6333"
)

print(f"Processed {stats['files_processed']} files")
```

### 2. Query Collection

```python
from qdrant_manager import query_qdrant_collection

# Basic query
results = query_qdrant_collection(
    collection_name="my_documents",
    query_text="halal certification requirements",
    limit=10
)

# Query with filters
pdf_results = query_qdrant_collection(
    collection_name="my_documents",
    query_text="food safety",
    limit=5,
    file_type_filter=".pdf",  # Only PDF files
    score_threshold=0.5       # Minimum similarity score
)
```

### 3. Advanced Usage with QdrantManager Class

```python
from qdrant_manager import QdrantManager

# Initialize manager
manager = QdrantManager(qdrant_url="http://localhost:6333")

# Push files with custom settings
stats = manager.push_files_to_collection(
    collection_name="documents",
    output_dir="output"
)

# Advanced querying
results = manager.query_collection(
    collection_name="documents",
    query_text="certification process",
    limit=20,
    score_threshold=0.3,
    file_type_filter=".md",
    file_name_filter="halal"
)
```

## Function Parameters

### push_files_to_collection()

- `collection_name` (str): Name of the Qdrant collection
- `output_dir` (str): Directory to scan for files (default: "output")
- `qdrant_url` (str): Qdrant server URL (default: "http://localhost:6333")

**Returns**: Dictionary with processing statistics

### query_collection()

- `collection_name` (str): Name of the collection to query
- `query_text` (str): Text to search for
- `limit` (int): Maximum number of results (default: 10)
- `score_threshold` (float): Minimum similarity score (default: 0.0)
- `file_type_filter` (str, optional): Filter by file extension (e.g., ".pdf")
- `file_name_filter` (str, optional): Filter by file name (partial match)
- `qdrant_url` (str): Qdrant server URL (default: "http://localhost:6333")

**Returns**: List of results sorted by score (highest first)

## Supported File Types

- **Markdown**: `.md`
- **PDF**: `.pdf`
- **Word Documents**: `.docx`
- **Text Files**: `.txt`

## File Processing Logic

1. **Duplicate Detection**: Files are checked by path, hash, and size
2. **Content Extraction**: Text is extracted based on file type
3. **Embedding Generation**: Uses sentence-transformers model
4. **Batch Upload**: Files are processed in batches for efficiency
5. **Error Handling**: Comprehensive error logging and recovery

## Result Format

Query results include:

```python
{
    "id": "unique_point_id",
    "score": 0.85,  # Similarity score (0-1)
    "file_path": "domain/file.pdf",
    "file_name": "file.pdf",
    "file_type": ".pdf",
    "file_size": 1024,
    "content_length": 5000,
    "content": "Full text content...",
    "processed_at": "2024-01-01T12:00:00",
    "collection": "my_documents"
}
```

## Example Script

Run the example script to see the functions in action:

```bash
python example_qdrant_usage.py
```

## Debugging

Enable debug logging to see detailed processing information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Error Handling

The functions include comprehensive error handling:

- Invalid file types are skipped with warnings
- Corrupted files are logged as errors
- Network issues with Qdrant are caught and reported
- Processing continues even if individual files fail
