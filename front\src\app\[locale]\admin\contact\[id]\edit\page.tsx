'use client'

export const runtime = 'edge'

import { ArrowLeft } from 'lucide-react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { ContactForm, type ContactFormData } from '@/components/ui/contact-form'
import { Link } from '@/i18n/navigation'
import { api } from '@/lib/api'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

interface ContactInfo {
  id: number
  name: string
  title: string
  department: string
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email: string
  phone?: string
  mobile?: string
  address?: string
  city?: string
  state?: string
  postcode?: string
  country?: string
  website?: string
  workingHours?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function EditContactPage() {
  const router = useRouter()
  const params = useParams()
  const contactId = params.id ? Number.parseInt(params.id as string, 10) : null

  const [contact, setContact] = useState<ContactInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isFetching, setIsFetching] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (contactId) {
      fetchContact()
    }
  }, [contactId])

  const fetchContact = async () => {
    if (!contactId) return

    try {
      setIsFetching(true)
      setError(null)
      const response = await api.get<{ data: ContactInfo }>(
        `/admin/contacts/${contactId}`
      )
      setContact(response.data)
    } catch (err: any) {
      console.error('Error fetching contact:', err)
      setError(
        err.response?.data?.error || err.message || 'Failed to load contact'
      )
    } finally {
      setIsFetching(false)
    }
  }

  const handleSubmit = async (data: ContactFormData) => {
    if (!contactId) return

    setIsLoading(true)
    setError(null)

    try {
      await api.put(`/admin/contacts/${contactId}`, data)
      router.push(`/admin/contact/${contactId}`)
    } catch (err: any) {
      console.error('Error updating contact:', err)
      setError(
        err.response?.data?.error || err.message || 'Failed to update contact'
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (contactId) {
      router.push(`/admin/contact/${contactId}`)
    } else {
      router.push('/admin/contact')
    }
  }

  if (isFetching) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contact">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Contact</h1>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading contact...</div>
        </div>
      </div>
    )
  }

  if (error || !contact) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/contact">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Contacts
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Contact</h1>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <span>{error || 'Contact not found'}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Link href={`/admin/contact/${contact.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Contact
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Contact</h1>
          <p className="text-gray-600 mt-2">
            Update contact information for {contact.name}
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <ContactForm
        mode="edit"
        initialData={contact}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}
