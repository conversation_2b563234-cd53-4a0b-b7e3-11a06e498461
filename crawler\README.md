# Website Crawler with Crawl4AI

A Docker-based website crawler that recursively crawls websites and downloads PDF/DOC/DOCX files.

## Features

- 🕷️ **Recursive Crawling**: Crawls websites recursively within the same domain/subdomain
- 📄 **Document Download**: Automatically downloads PDF, DOC, and DOCX files
- 📝 **Markdown Output**: Saves crawled content as organized markdown files
- 🐳 **Docker Support**: Runs in Docker for consistent environment
- 🔧 **Configurable**: Adjustable depth and page limits
- 📁 **Organized Output**: Saves content as `<domain>.md` and files in `<domain>/<filename>`

## Quick Start

### Using the Run Script (Recommended)

```bash
# Make the script executable
chmod +x run_crawler.sh

# Crawl the default website (myehalal.halal.gov.my)
./run_crawler.sh

# Crawl a specific website
./run_crawler.sh https://example.com

# Crawl with custom depth and page limits
./run_crawler.sh https://example.com 2 50
```

### Using Docker Compose

```bash
# Run with default settings
docker-compose up

# Edit docker-compose.yml to change the URL and parameters
```

### Using Docker Directly

```bash
# Build the image
docker build -t website-crawler .

# Run the crawler
docker run --rm -v "$(pwd)/output:/app/output" website-crawler https://www.myehalal.halal.gov.my 3 100
```

## Parameters

- **URL**: The website URL to crawl (required)
- **MAX_DEPTH**: Maximum crawling depth (default: 3)
- **MAX_PAGES**: Maximum number of pages to crawl (default: 100)

## Output Structure

```
output/
└── www_myehalal_halal_gov_my/
    ├── www_myehalal_halal_gov_my.md    # Main markdown report
    └── files/                           # Downloaded files
        ├── document1.pdf
        ├── guide.docx
        └── manual.doc
```

## Configuration

### Environment Variables

- `PYTHONUNBUFFERED=1`: Ensures real-time logging output
- `DISPLAY=:99`: Virtual display for headless browser

### Crawler Settings

Edit `crawl_website.py` to modify:

- User agent strings
- Request delays
- File type filters
- Content extraction rules

## Examples

### Crawl Malaysian Halal Website

```bash
./run_crawler.sh https://www.myehalal.halal.gov.my 3 100
```

### Crawl with Limited Depth

```bash
./run_crawler.sh https://example.com 2 50
```

### Crawl Multiple Websites

```bash
# Create a script to crawl multiple sites
for url in "https://site1.com" "https://site2.com"; do
    ./run_crawler.sh "$url" 2 25
done
```

## Troubleshooting

### Docker Issues

```bash
# Check if Docker is running
docker info

# Rebuild the image if needed
docker build --no-cache -t website-crawler .
```

### Permission Issues

```bash
# Make scripts executable
chmod +x run_crawler.sh

# Fix output directory permissions
sudo chown -R $USER:$USER output/
```

### Memory Issues

```bash
# Run with memory limits
docker run --rm --memory=2g -v "$(pwd)/output:/app/output" website-crawler [URL]
```

## Advanced Usage

### Custom Crawler Function

```python
import asyncio
from crawl_website import crawl_website

async def main():
    result = await crawl_website(
        url="https://example.com",
        max_depth=2,
        max_pages=50
    )
    print(f"Crawled {result['pages_crawled']} pages")
    print(f"Downloaded {result['files_downloaded']} files")

asyncio.run(main())
```

### Batch Crawling

```bash
# Create a list of URLs to crawl
echo "https://site1.com" > urls.txt
echo "https://site2.com" >> urls.txt

# Crawl each URL
while read url; do
    ./run_crawler.sh "$url" 2 25
done < urls.txt
```

## Requirements

- Docker
- 2GB+ RAM (recommended)
- Internet connection
- Sufficient disk space for downloaded content

## License

MIT License - feel free to modify and distribute.
