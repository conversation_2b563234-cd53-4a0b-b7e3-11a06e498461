import crypto from "crypto";
import type { ScrapedCompany } from "../types";

export class DataValidator {
  /**
   * Validate a scraped company record
   */
  static validateCompany(company: ScrapedCompany): {
    isValid: boolean;
    errors: string[];
    normalizedData?: ScrapedCompany;
  } {
    const errors: string[] = [];

    console.log("Validation received company:", typeof company, company);
    console.log("Company name from validation:", company?.companyName);

    // Required fields validation
    if (!company.companyName || company.companyName.trim().length < 2) {
      errors.push("Company name is required and must be at least 2 characters");
    }

    // Company name should not be too long
    if (company.companyName && company.companyName.length > 500) {
      errors.push("Company name is too long (max 500 characters)");
    }

    // Address validation (text field, but reasonable limit)
    if (company.address && company.address.length > 2000) {
      errors.push("Address is too long (max 2000 characters)");
    }

    // Expiry date validation
    if (company.expiryDate && company.expiryDate.length > 255) {
      errors.push("Expiry date is too long (max 255 characters)");
    }

    // Source URL validation
    if (company.sourceUrl && company.sourceUrl.length > 1000) {
      errors.push("Source URL is too long (max 1000 characters)");
    }

    // Email validation
    if (company.email && !DataValidator.isValidEmail(company.email)) {
      errors.push("Invalid email format");
    }

    // Website validation
    if (company.website && !DataValidator.isValidUrl(company.website)) {
      errors.push("Invalid website URL format");
    }

    // Phone validation
    if (company.phone && !DataValidator.isValidPhone(company.phone)) {
      errors.push("Invalid phone number format");
    }

    // Postcode validation for Malaysia
    if (
      company.postcode &&
      !DataValidator.isValidMalaysianPostcode(company.postcode)
    ) {
      errors.push("Invalid Malaysian postcode format");
    }

    const isValid = errors.length === 0;

    return {
      isValid,
      errors,
      normalizedData: isValid ? company : undefined,
    };
  }

  /**
   * Generate a hash for duplicate detection
   */
  static generateDataHash(company: ScrapedCompany): string {
    // Create a normalized string for hashing
    const normalizedData = [
      company.companyName?.toLowerCase().trim(),
      company.address?.toLowerCase().trim(),
      company.phone?.replace(/\D/g, ""), // Remove non-digits from phone
      company.email?.toLowerCase().trim(),
    ]
      .filter(Boolean)
      .join("|");

    return crypto.createHash("sha256").update(normalizedData).digest("hex");
  }

  /**
   * Check if company data appears to be a duplicate
   */
  static isDuplicateCandidate(
    company1: ScrapedCompany,
    company2: ScrapedCompany,
  ): boolean {
    // Exact name match
    if (
      company1.companyName?.toLowerCase() ===
      company2.companyName?.toLowerCase()
    ) {
      return true;
    }

    // Similar name with same address
    if (
      DataValidator.isSimilarName(company1.companyName, company2.companyName) &&
      company1.address?.toLowerCase() === company2.address?.toLowerCase()
    ) {
      return true;
    }

    // Same phone number (if both have phone numbers)
    if (
      company1.phone &&
      company2.phone &&
      DataValidator.normalizePhone(company1.phone) ===
        DataValidator.normalizePhone(company2.phone)
    ) {
      return true;
    }

    // Same email (if both have emails)
    if (
      company1.email &&
      company2.email &&
      company1.email.toLowerCase() === company2.email.toLowerCase()
    ) {
      return true;
    }

    return false;
  }

  /**
   * Clean and normalize company data
   */
  static normalizeCompany(company: ScrapedCompany): ScrapedCompany {
    return {
      ...company,
      companyName: DataValidator.normalizeText(company.companyName),
      address: DataValidator.normalizeText(company.address),
      city: DataValidator.normalizeText(company.city),
      state: DataValidator.normalizeText(company.state),
      country: company.country || "Malaysia",
      phone: company.phone
        ? DataValidator.normalizePhone(company.phone)
        : undefined,
      email: company.email ? company.email.toLowerCase().trim() : undefined,
      website: company.website
        ? DataValidator.normalizeUrl(company.website)
        : undefined,
    };
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private static isValidPhone(phone: string): boolean {
    // Malaysian phone number patterns
    const phoneRegex = /^(\+?6?0?1[0-9]-?[0-9]{7,8}|0[0-9]-?[0-9]{7,8})$/;
    const normalizedPhone = phone.replace(/[\s\-()]/g, "");
    return phoneRegex.test(normalizedPhone) || normalizedPhone.length >= 8;
  }

  private static isValidMalaysianPostcode(postcode: string): boolean {
    // Malaysian postcodes are 5 digits
    const postcodeRegex = /^[0-9]{5}$/;
    return postcodeRegex.test(postcode.trim());
  }

  private static isSimilarName(name1?: string, name2?: string): boolean {
    if (!name1 || !name2) return false;

    const normalize = (str: string) =>
      str.toLowerCase().replace(/[^a-z0-9]/g, "");
    const normalized1 = normalize(name1);
    const normalized2 = normalize(name2);

    // Check if one is a substring of the other (for cases like "ABC Sdn Bhd" vs "ABC")
    return (
      normalized1.includes(normalized2) || normalized2.includes(normalized1)
    );
  }

  private static normalizeText(text?: string): string | undefined {
    if (!text) return undefined;
    return text.trim().replace(/\s+/g, " ");
  }

  private static normalizePhone(phone: string): string {
    // Remove all non-digit characters except +
    return phone.replace(/[^\d+]/g, "");
  }

  private static normalizeUrl(url: string): string {
    const trimmed = url.trim();
    if (trimmed.startsWith("http")) return trimmed;
    if (trimmed.startsWith("www.")) return `https://${trimmed}`;
    return `https://${trimmed}`;
  }
}

export class DataQualityChecker {
  /**
   * Assess the quality of scraped data
   */
  static assessDataQuality(companies: ScrapedCompany[]): {
    totalRecords: number;
    validRecords: number;
    invalidRecords: number;
    completenessScore: number;
    qualityIssues: string[];
  } {
    const qualityIssues: string[] = [];
    let validRecords = 0;
    let totalCompleteness = 0;

    for (const company of companies) {
      const validation = DataValidator.validateCompany(company);
      if (validation.isValid) {
        validRecords++;
      } else {
        qualityIssues.push(...validation.errors);
      }

      // Calculate completeness score for this record
      const fields = [
        company.companyName,
        company.address,
        company.phone,
        company.email,
        company.state,
        company.category,
      ];
      const filledFields = fields.filter(
        (field) => field && field.trim().length > 0,
      ).length;
      totalCompleteness += (filledFields / fields.length) * 100;
    }

    return {
      totalRecords: companies.length,
      validRecords,
      invalidRecords: companies.length - validRecords,
      completenessScore:
        companies.length > 0 ? totalCompleteness / companies.length : 0,
      qualityIssues: [...new Set(qualityIssues)], // Remove duplicates
    };
  }
}
