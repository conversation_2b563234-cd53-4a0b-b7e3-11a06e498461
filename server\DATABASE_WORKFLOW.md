# Database Schema Management Workflow

This guide explains the **ideal workflow** for managing database schema changes in production.

## 🎯 **Core Principles**

1. **Migration-First**: All schema changes go through migrations
2. **Version Control**: Every schema change is tracked and versioned
3. **Rollback Ready**: Always have a rollback plan
4. **Production Safe**: Test migrations in staging first

## 📋 **Workflow Steps**

### 1. **Making Schema Changes**

```bash
# 1. Edit schema file
vim src/db/schema.ts

# 2. Generate migration
pnpm drizzle:generate

# 3. Review generated migration
cat drizzle/XXXX_*.sql

# 4. Test locally
pnpm db:push  # For local testing
pnpm db:seed  # Verify with test data
```

### 2. **Pre-Production Validation**

```bash
# 1. Backup production schema
pnpm db:backup:prod

# 2. Check current status
pnpm db:status:prod

# 3. Test migration on staging (if available)
# Run migration on staging database first
```

### 3. **Production Deployment**

```bash
# 1. Deploy (auto-runs migration)
pnpm deploy:server

# 2. Verify deployment
pnpm db:status:prod
pnpm server:logs
```

## 🔄 **Migration Types**

### **Safe Migrations** (Zero Downtime)

- Adding new tables
- Adding new columns (with defaults)
- Adding indexes (concurrently)
- Adding constraints (non-blocking)

### **Risky Migrations** (Potential Downtime)

- Dropping columns
- Changing column types
- Adding NOT NULL constraints to existing columns
- Renaming tables/columns

### **Complex Migrations** (Multi-Step)

For risky changes, use multi-step approach:

1. **Step 1**: Add new column/table
2. **Deploy**: Application supports both old and new
3. **Step 2**: Migrate data
4. **Deploy**: Application uses new schema
5. **Step 3**: Remove old column/table

## 📁 **File Organization**

```
server/
├── src/db/
│   ├── schema.ts              # Single source of truth
│   └── connection.ts          # Database connection
├── drizzle/
│   ├── 0000_initial.sql       # Initial schema
│   ├── 0001_add_users.sql     # Incremental changes
│   ├── seed.ts                # Development seed
│   └── seed-production.ts     # Production seed
└── scripts/
    └── db-management.ts       # Database utilities
```

## 🚨 **Emergency Procedures**

### **Rollback Migration**

```bash
# 1. Identify problematic migration
pnpm db:status:prod

# 2. Create rollback migration
# Manually create reverse migration in drizzle/

# 3. Apply rollback
pnpm db:migrate:prod

# 4. Deploy previous application version
git checkout <previous-commit>
pnpm deploy:server
```

### **Database Recovery**

```bash
# 1. Stop application (if needed)
# Scale down workers or disable routes

# 2. Restore from backup
# Use your database provider's backup restore

# 3. Verify data integrity
pnpm db:status:prod

# 4. Restart application
pnpm deploy:server
```

## ✅ **Best Practices**

### **Development**

- Always test migrations locally first
- Use descriptive migration names
- Keep migrations small and focused
- Never edit existing migration files

### **Production**

- Backup before every migration
- Monitor application during deployment
- Have rollback plan ready
- Test in staging environment first

### **Schema Design**

- Use nullable columns for new fields
- Add indexes concurrently
- Avoid large data migrations in schema changes
- Use separate data migration scripts for large changes

## 🔧 **Troubleshooting**

### **Migration Fails**

```bash
# Check migration status
pnpm db:status:prod

# View detailed error
dotenv -e .env.production -- npx drizzle-kit migrate --verbose

# Manual intervention (if needed)
dotenv -e .env.production -- npx drizzle-kit studio
```

### **Schema Drift**

```bash
# Compare schema with migrations
pnpm db:status:prod

# Generate corrective migration
pnpm drizzle:generate

# Apply correction
pnpm db:migrate:prod
```

### **Performance Issues**

```bash
# Check for missing indexes
# Use database provider's query analyzer

# Add indexes via migration
# Create new migration with index additions
```

## 📚 **Additional Resources**

- [Drizzle Migrations Guide](https://orm.drizzle.team/kit-docs/overview)
- [PostgreSQL Migration Best Practices](https://www.postgresql.org/docs/current/ddl-alter.html)
- [Zero-Downtime Migrations](https://blog.codeship.com/rails-migrations-zero-downtime/)
