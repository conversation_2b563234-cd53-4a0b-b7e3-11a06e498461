import { eq } from 'drizzle-orm'
import { type NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { products, categories, productCategories } from '@/lib/db/schema'
import {
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-response'

// Remove edge runtime to use Node.js runtime for database access

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params
  const productId = Number.parseInt(params.id)

  if (isNaN(productId)) {
    return createErrorResponse('Invalid product ID', undefined, 400)
  }

  try {
    const [product] = await db
      .select({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
        certificateNumber: products.certificateNumber,
        certificateType: products.certificateType,
        issuedDate: products.issuedDate,
        expiryDate: products.expiryDate,
        status: products.status,
        category: products.category,
        subcategory: products.subcategory,
        address: products.address,
        state: products.state,
        country: products.country,
        contactInfo: products.contactInfo,
        website: products.website,
        sourceUrl: products.sourceUrl,
        rawData: products.rawData,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      })
      .from(products)
      .where(eq(products.id, productId))
      .limit(1)

    if (!product) {
      return createErrorResponse('Product not found', undefined, 404)
    }

    // Fetch categories from junction table for this product
    const productCategoriesData = await db
      .select({
        categoryName: categories.categoryName,
      })
      .from(productCategories)
      .innerJoin(categories, eq(productCategories.categoryId, categories.id))
      .where(eq(productCategories.productId, productId))

    // Replace category field with junction table categories
    if (productCategoriesData.length > 0) {
      const junctionCategories = productCategoriesData.map(
        item => item.categoryName
      )
      // Use categories from junction table with spaces around pipe
      product.category = junctionCategories.join(' | ')
    } else {
      // No junction table categories found, set to empty
      product.category = ''
    }

    console.log('Product details response:', {
      productId,
      productName: product.productName,
    })

    return createSuccessResponse(product)
  } catch (error) {
    return handleApiError(error, 'Failed to fetch product details')
  }
}
