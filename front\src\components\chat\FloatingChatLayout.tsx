'use client'

import {
  <PERSON><PERSON>,
  Maximize2,
  MessageCircle,
  Mic,
  Mic<PERSON><PERSON>,
  Minimize2,
  Send,
  User,
  X,
} from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import { useMergeRefs } from 'use-callback-ref'
import type { ChatCoreRenderProps } from './ChatCore'
import { TypingIndicator } from './LoadingSpinner'
import { SourcesDisplay } from './SourcesDisplay'

interface FloatingChatLayoutProps {
  renderProps: ChatCoreRenderProps
  isStaffLoggedIn?: boolean
}

export function FloatingChatLayout({
  renderProps,
  isStaffLoggedIn = false,
}: FloatingChatLayoutProps) {
  const {
    messages,
    inputText,
    isLoading,
    sessionId,
    uploadedImage,
    integrationStatus,
    isHandedOver,
    agentName,
    showHandoverButton,
    hasNewMessage,
    isRecording,
    isProcessingVoice,
    voiceError,
    isVoiceSupported,
    isDragActive,
    getRootProps,
    getInputProps,
    setInputText,
    sendMessage,
    clearUploadedImage,
    requestHandover,
    toggleRecording,
    clearVoiceError,
    messagesContainerRef,
    inputRef,
    t,
  } = renderProps

  const [isOpen, setIsOpen] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)
  const [localHasNewMessage, setLocalHasNewMessage] = useState(false)

  // Merge refs for drag and drop and auto-scroll
  const mergedRef = useMergeRefs([messagesContainerRef, getRootProps().ref])

  // Auto-scroll to show latest message when any new message arrives
  useEffect(() => {
    // Only run if chat is open and not for staff
    if (messages.length > 0 && isOpen && !isStaffLoggedIn) {
      // Use a retry mechanism to ensure DOM is ready
      const attemptScroll = (attempt = 1, maxAttempts = 10) => {
        if (messagesContainerRef.current) {
          const container = messagesContainerRef.current

          // Get all message elements (excluding loading indicator)
          const messageElements = container.querySelectorAll(
            'div[data-message-id]:not([data-message-id="loading"])'
          )

          if (messageElements.length > 0) {
            // Get the last message element
            const lastMessage = messageElements[messageElements.length - 1]

            // Scroll to show the latest message at the top after scrolling
            lastMessage.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            })
          }
        } else if (attempt < maxAttempts) {
          // Retry with exponential backoff
          setTimeout(
            () => attemptScroll(attempt + 1, maxAttempts),
            50 * attempt
          )
        } else {
          console.log('Container not found after maximum attempts')
        }
      }

      // Start the scroll attempt
      attemptScroll()
    }
  }, [messages, isOpen, isStaffLoggedIn])

  // Show notification when chat is closed and new message arrives
  useEffect(() => {
    if (!isOpen && messages.length > 0) {
      const lastMessage = messages[messages.length - 1]
      if (lastMessage.role === 'assistant') {
        setLocalHasNewMessage(true)
      }
    }
  }, [messages, isOpen])

  // Clear notification and focus input when chat opens
  useEffect(() => {
    if (isOpen) {
      setLocalHasNewMessage(false)
      inputRef.current?.focus()
    } else {
      // Reset maximize state when closing
      setIsMaximized(false)
    }
  }, [isOpen])

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (inputText.length > 0 || uploadedImage) {
      sendMessage(inputText, uploadedImage?.url)
    }
  }

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50 text-gray-600">
        <button
          type="button"
          onClick={isStaffLoggedIn ? undefined : () => setIsOpen(true)}
          className={`relative p-4 rounded-full shadow-lg transition-all duration-200 ${
            isStaffLoggedIn
              ? 'bg-gray-400 text-gray-600 cursor-not-allowed opacity-50'
              : 'bg-blue-600 hover:bg-blue-700 text-white hover:scale-105'
          }`}
          aria-label={isStaffLoggedIn ? 'Chat disabled for staff' : 'Open chat'}
          disabled={isStaffLoggedIn}
          title={
            isStaffLoggedIn ? 'Staff members cannot use the chat' : 'Open chat'
          }
        >
          <MessageCircle className="w-6 h-6" />
          {!isStaffLoggedIn && (localHasNewMessage || hasNewMessage) && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
          )}
        </button>
      </div>
    )
  }

  return (
    <div
      className={`fixed z-50 transition-all duration-200 ${
        isMaximized ? 'inset-4' : 'bottom-4 right-4'
      }`}
    >
      <div
        className={`bg-white rounded-lg shadow-2xl border transition-all duration-200 flex flex-col ${
          isMaximized
            ? 'w-full h-full'
            : 'max-w-[calc(100vw-4rem)] w-140 h-[32rem]'
        }`}
      >
        {/* Header */}
        <div className="bg-blue-600 text-white p-3 rounded-t-lg flex-shrink-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              <span className="font-medium text-sm">Assistant</span>
            </div>
            <div className="flex items-center gap-1">
              <button
                type="button"
                onClick={() => setIsMaximized(!isMaximized)}
                className="p-1 hover:bg-blue-700 rounded"
                title={isMaximized ? 'Minimize' : 'Maximize'}
              >
                {isMaximized ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-blue-700 rounded"
                title="Close chat"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Agent Status & Handover Button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isHandedOver && agentName ? (
                <div className="flex items-center bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-2 py-1">
                  <User className="w-3 h-3 mr-1 text-green-300" />
                  <span className="text-xs text-green-100">
                    Connected to {agentName}
                  </span>
                </div>
              ) : (
                showHandoverButton && (
                  <button
                    type="button"
                    onClick={requestHandover}
                    className="flex items-center bg-orange-500 bg-opacity-20 border border-orange-400 rounded-full px-2 py-1 hover:bg-opacity-30 transition-colors"
                  >
                    <Headphones className="w-3 h-3 mr-1 text-orange-300" />
                    <span className="text-xs text-orange-100">
                      Talk to Human
                    </span>
                  </button>
                )
              )}
            </div>
          </div>
        </div>

        {/* Messages */}
        <div
          ref={mergedRef}
          className="chat-body flex-1 overflow-y-auto p-4 space-y-4 min-h-0"
          {...getRootProps()}
        >
          <input {...getInputProps()} />
          {isDragActive && (
            <div className="absolute inset-0 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center z-10">
              <p className="text-blue-600 font-medium">Drop files here...</p>
            </div>
          )}

          {messages.map((message, index) => (
            <div
              key={message.id}
              data-message-id={message.id}
              className={`flex ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-[85%] px-3 py-2 rounded-lg text-sm ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : message.role === 'agent'
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-gray-100 text-gray-800'
                }`}
              >
                {message.role === 'agent' && message.agentName && (
                  <div className="flex items-center mb-1">
                    <User className="w-3 h-3 mr-1 text-green-600" />
                    <span className="text-xs font-medium text-green-600">
                      {message.agentName}
                    </span>
                  </div>
                )}

                {message.imageUrl && (
                  <img
                    src={message.imageUrl}
                    alt="Uploaded"
                    className="w-full max-w-48 h-auto object-cover rounded mb-2"
                  />
                )}

                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown>{message.content}</ReactMarkdown>
                </div>

                {/* Display sources if available */}
                {message.sources && message.sources.length > 0 && (
                  <SourcesDisplay sources={message.sources} />
                )}

                <p className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}

          {isLoading && (
            <div data-message-id="loading" className="flex justify-start">
              <div className="bg-gray-100 text-gray-800 px-3 py-2 rounded-lg">
                <TypingIndicator />
              </div>
            </div>
          )}
        </div>

        {/* Image Preview */}
        {uploadedImage && (
          <div className="px-4 py-3 bg-gray-50 border-t flex-shrink-0">
            <div className="flex items-center space-x-3">
              <img
                src={uploadedImage.url}
                alt="Preview"
                className="w-10 h-10 object-cover rounded"
              />
              <span className="text-sm text-gray-600">Image ready</span>
              <button
                type="button"
                onClick={clearUploadedImage}
                className="text-red-500 hover:text-red-700"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Input */}
        <div className="p-4 border-t flex-shrink-0">
          <form onSubmit={handleSubmit} className="flex items-center space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={inputText}
              onChange={e => setInputText(e.target.value)}
              placeholder={t('placeholder') || 'Type your message...'}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-800"
              disabled={isLoading}
            />

            <button
              type="button"
              onClick={toggleRecording}
              className={`p-2 rounded-lg transition-colors ${
                !isVoiceSupported
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : isRecording
                    ? 'bg-red-500 hover:bg-red-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
              disabled={isLoading || isProcessingVoice || !isVoiceSupported}
              title={
                !isVoiceSupported
                  ? 'Voice recording not supported'
                  : isRecording
                    ? 'Stop recording'
                    : 'Start recording'
              }
            >
              {isRecording ? (
                <MicOff className="w-4 h-4" />
              ) : (
                <Mic className="w-4 h-4" />
              )}
            </button>

            <button
              type="submit"
              disabled={isLoading || (!inputText.trim() && !uploadedImage)}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white p-2 rounded-lg transition-colors"
              title="Send message"
            >
              <Send className="w-4 h-4" />
            </button>
          </form>

          {/* Voice error display */}
          {voiceError && (
            <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200">
              <div className="flex items-start justify-between">
                <span className="flex-1">{voiceError}</span>
                <button
                  type="button"
                  onClick={clearVoiceError}
                  className="ml-2 text-red-500 hover:text-red-700"
                  title="Dismiss error"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            </div>
          )}

          {/* Voice processing indicator */}
          {isProcessingVoice && (
            <div className="mt-2 text-xs text-blue-600 bg-blue-50 p-2 rounded border border-blue-200">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2" />
                Processing voice recording...
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
