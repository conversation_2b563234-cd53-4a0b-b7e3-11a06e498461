#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to list all chat sessions in the database
 */

import { config } from 'dotenv'
import DatabaseService from './src/services/database.ts'

// Load environment variables
config()

async function listSessions() {
  console.log('📋 Listing All Chat Sessions...\n')

  try {
    // Initialize database service
    const dbService = new DatabaseService()
    console.log('✅ Database service initialized')

    // Get all sessions (we'll need to implement this method)
    console.log('\n📝 Retrieving all sessions...')

    // For now, let's try to get the sessions we created in our tests
    const testSessionIds = [
      'test_session_1752149191648', // From our recent test
      'fb_session_1752148136075', // From our Facebook test
    ]

    console.log('\n📊 Available Test Sessions:')

    for (const sessionId of testSessionIds) {
      try {
        const session = await dbService.getChatSession(sessionId)
        if (session) {
          console.log(`\n🔍 Session: ${sessionId}`)
          console.log(`   Platform: ${session.platform}`)
          console.log(`   Platform ID: ${session.platformId}`)
          console.log(`   Status: ${session.status}`)
          console.log(`   Created: ${session.createdAt}`)
          console.log(`   Messages: ${session.messages?.length || 0}`)
          console.log(
            `   URL: http://localhost:3000/admin/sessions/${sessionId}`,
          )
        }
      } catch (error) {
        console.log(`   ❌ Session ${sessionId} not found or error: ${error}`)
      }
    }

    console.log('\n💡 Instructions:')
    console.log('1. Start the frontend server: cd front && npm run dev')
    console.log('2. Start the backend server: cd server && bun dev')
    console.log('3. Login to admin panel: http://localhost:3000/admin/login')
    console.log('4. Navigate to sessions: http://localhost:3000/admin/sessions')
    console.log('5. Click on any session to view messages')
  } catch (error) {
    console.error('❌ Error:', error)
    process.exit(1)
  }
}

// Run the script
listSessions().catch(console.error)
