import type { WorkerEnv } from '../types'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogContext {
  sessionId?: string
  userId?: string
  platform?: string
  requestId?: string
  toolCall?: string
  messageType?: string
  [key: string]: any
}

export class WorkerLogger {
  private static level: LogLevel = LogLevel.INFO
  private static isDevelopment = false

  static initialize(env?: WorkerEnv): void {
    // Set log level based on environment
    const logLevel = env?.LOG_LEVEL || 'INFO'
    switch (logLevel.toUpperCase()) {
      case 'DEBUG':
        WorkerLogger.level = LogLevel.DEBUG
        break
      case 'INFO':
        WorkerLogger.level = LogLevel.INFO
        break
      case 'WARN':
        WorkerLogger.level = LogLevel.WARN
        break
      case 'ERROR':
        WorkerLogger.level = LogLevel.ERROR
        break
      default:
        WorkerLogger.level = LogLevel.INFO
    }

    // Check if we're in development mode
    WorkerLogger.isDevelopment =
      env?.NODE_ENV === 'development' || env?.ENVIRONMENT === 'development'
  }

  static setLevel(level: LogLevel): void {
    WorkerLogger.level = level
  }

  static debug(message: string, context?: LogContext, ...args: any[]): void {
    if (WorkerLogger.level <= LogLevel.DEBUG) {
      WorkerLogger.log('DEBUG', '🔍', message, context, ...args)
    }
  }

  static info(message: string, context?: LogContext, ...args: any[]): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      WorkerLogger.log('INFO', 'ℹ️', message, context, ...args)
    }
  }

  static warn(message: string, context?: LogContext, ...args: any[]): void {
    if (WorkerLogger.level <= LogLevel.WARN) {
      WorkerLogger.log('WARN', '⚠️', message, context, ...args)
    }
  }

  static error(message: string, context?: LogContext, ...args: any[]): void {
    if (WorkerLogger.level <= LogLevel.ERROR) {
      WorkerLogger.log('ERROR', '❌', message, context, ...args)
    }
  }

  static success(message: string, context?: LogContext, ...args: any[]): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      WorkerLogger.log('SUCCESS', '✅', message, context, ...args)
    }
  }

  static messageFlow(
    stage: string,
    message: string,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      WorkerLogger.log('FLOW', '🔄', `[${stage}] ${message}`, context)
    }
  }

  static toolCall(
    toolName: string,
    message: string,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      WorkerLogger.log('TOOL', '🔧', `[${toolName}] ${message}`, context)
    }
  }

  static session(
    action: string,
    sessionId: string,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      WorkerLogger.log(
        'SESSION',
        '👤',
        `[${action}] Session: ${sessionId}`,
        context,
      )
    }
  }

  static database(
    operation: string,
    message: string,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.DEBUG) {
      WorkerLogger.log('DB', '🗄️', `[${operation}] ${message}`, context)
    }
  }

  static api(
    method: string,
    endpoint: string,
    status: number,
    duration?: number,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.INFO) {
      const statusEmoji = status >= 400 ? '❌' : status >= 300 ? '⚠️' : '✅'
      const durationText = duration ? ` (${duration}ms)` : ''
      WorkerLogger.log(
        'API',
        statusEmoji,
        `${method} ${endpoint} ${status}${durationText}`,
        context,
      )
    }
  }

  static performance(
    operation: string,
    duration: number,
    context?: LogContext,
  ): void {
    if (WorkerLogger.level <= LogLevel.DEBUG) {
      const emoji = duration > 5000 ? '🐌' : duration > 1000 ? '⏳' : '⚡'
      WorkerLogger.log(
        'PERF',
        emoji,
        `${operation} took ${duration}ms`,
        context,
      )
    }
  }

  private static log(
    level: string,
    emoji: string,
    message: string,
    context?: LogContext,
    ...args: any[]
  ): void {
    const timestamp = new Date().toISOString()
    const contextStr = WorkerLogger.formatContext(context)

    // Create the main log message
    const logMessage = `[${timestamp}] ${emoji} [${level}] ${message}${contextStr}`

    // Use appropriate console method
    switch (level) {
      case 'ERROR':
        console.error(logMessage, ...args)
        break
      case 'WARN':
        console.warn(logMessage, ...args)
        break
      default:
        console.log(logMessage, ...args)
        break
    }

    // In development, also log additional context details
    if (
      WorkerLogger.isDevelopment &&
      context &&
      Object.keys(context).length > 0
    ) {
      console.log('   📋 Context:', context)
    }

    // Log additional arguments if provided
    if (args.length > 0) {
      console.log('   📄 Details:', ...args)
    }
  }

  private static formatContext(context?: LogContext): string {
    if (!context || Object.keys(context).length === 0) {
      return ''
    }

    const parts: string[] = []

    if (context.sessionId) {
      parts.push(`session:${context.sessionId.substring(0, 8)}`)
    }

    if (context.userId) {
      parts.push(`user:${context.userId}`)
    }

    if (context.platform) {
      parts.push(`platform:${context.platform}`)
    }

    if (context.requestId) {
      parts.push(`req:${context.requestId.substring(0, 8)}`)
    }

    if (context.toolCall) {
      parts.push(`tool:${context.toolCall}`)
    }

    if (context.messageType) {
      parts.push(`type:${context.messageType}`)
    }

    return parts.length > 0 ? ` [${parts.join('|')}]` : ''
  }
}

// Performance tracking utility
export class PerformanceTracker {
  private startTime: number
  private operation: string
  private context?: LogContext

  constructor(operation: string, context?: LogContext) {
    this.operation = operation
    this.context = context
    this.startTime = Date.now()
    WorkerLogger.debug(`Starting ${operation}`, context)
  }

  end(message?: string): number {
    const duration = Date.now() - this.startTime
    const finalMessage = message || `Completed ${this.operation}`
    WorkerLogger.performance(finalMessage, duration, this.context)
    return duration
  }

  checkpoint(checkpointName: string): number {
    const duration = Date.now() - this.startTime
    WorkerLogger.debug(`${this.operation} - ${checkpointName}`, {
      ...this.context,
      checkpoint: checkpointName,
      duration,
    })
    return duration
  }
}

// Request tracking utility
export class RequestTracker {
  private requestId: string
  private startTime: number
  private context: LogContext

  constructor(sessionId?: string, userId?: string, platform?: string) {
    this.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
    this.startTime = Date.now()
    this.context = {
      requestId: this.requestId,
      sessionId,
      userId,
      platform,
    }

    WorkerLogger.messageFlow(
      'START',
      'Processing incoming message',
      this.context,
    )
  }

  getContext(): LogContext {
    return { ...this.context }
  }

  updateContext(updates: Partial<LogContext>): void {
    this.context = { ...this.context, ...updates }
  }

  stage(stageName: string, message: string): void {
    WorkerLogger.messageFlow(stageName, message, this.context)
  }

  complete(success: boolean, message?: string): void {
    const duration = Date.now() - this.startTime
    const status = success ? 'SUCCESS' : 'FAILED'
    const finalMessage = message || `Request ${status.toLowerCase()}`

    WorkerLogger.messageFlow(
      'END',
      `${finalMessage} (${duration}ms)`,
      this.context,
    )

    if (success) {
      WorkerLogger.success(`Request completed in ${duration}ms`, this.context)
    } else {
      WorkerLogger.error(`Request failed after ${duration}ms`, this.context)
    }
  }
}

// Export singleton instance
export const logger = WorkerLogger
