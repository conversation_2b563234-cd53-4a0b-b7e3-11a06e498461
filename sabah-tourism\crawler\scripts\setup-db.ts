#!/usr/bin/env bun

import "dotenv/config";
import chalk from "chalk";
import fs from "fs";
import path from "path";
import { createDatabaseConfig } from "../config/database.config.js";
import { DatabaseManager } from "../src/crawlers/base/DatabaseManager.js";
import logger from "../src/utils/logger.js";

async function setupDatabase() {
  console.log(chalk.blue("🔧 Setting up Sabah Tourism Crawler Database"));

  try {
    // Create data directory if it doesn't exist
    const dbConfig = createDatabaseConfig();
    const dbDir = path.dirname(dbConfig.path);

    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      console.log(chalk.green(`✅ Created database directory: ${dbDir}`));
    }

    // Initialize database
    console.log(chalk.blue("📊 Initializing database..."));
    const database = new DatabaseManager(dbConfig);

    console.log(chalk.green("✅ Database schema created successfully"));

    // Verify tables exist
    const tables = [
      "social_posts",
      "media_files",
      "crawl_sessions",
      "crawl_attempts",
      "search_keywords",
    ];

    console.log(chalk.blue("🔍 Verifying database tables..."));

    for (const table of tables) {
      try {
        // Simple query to check if table exists and is accessible
        const result = database["db"]
          .prepare(`SELECT COUNT(*) as count FROM ${table}`)
          .get();
        console.log(
          chalk.green(
            `✅ Table '${table}' verified (${(result as any).count} records)`,
          ),
        );
      } catch (error) {
        console.error(
          chalk.red(
            `❌ Error verifying table '${table}': ${(error as Error).message}`,
          ),
        );
        throw error;
      }
    }

    // Create output directories
    console.log(chalk.blue("📁 Creating output directories..."));

    const outputDir = process.env.MEDIA_OUTPUT_DIR || "./output";
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      console.log(chalk.green(`✅ Created output directory: ${outputDir}`));
    }

    // Create platform-specific directories
    const platforms = ["douyin", "tiktok", "instagram", "facebook", "youtube"];
    for (const platform of platforms) {
      const platformDir = path.join(outputDir, platform);
      if (!fs.existsSync(platformDir)) {
        fs.mkdirSync(platformDir, { recursive: true });
        console.log(
          chalk.green(`✅ Created platform directory: ${platformDir}`),
        );
      }
    }

    // Create logs directory
    const logsDir = "./logs";
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      console.log(chalk.green(`✅ Created logs directory: ${logsDir}`));
    }

    // Test database operations
    console.log(chalk.blue("🧪 Testing database operations..."));

    // Test session creation
    const testSessionId = database.createSession({
      platform: "test",
      keywords: JSON.stringify(["test"]),
      status: "completed",
      config: JSON.stringify({ test: true }),
    });

    console.log(
      chalk.green(`✅ Test session created with ID: ${testSessionId}`),
    );

    // Test session retrieval
    const testSession = database.getSession(testSessionId);
    if (testSession) {
      console.log(chalk.green("✅ Test session retrieved successfully"));
    } else {
      throw new Error("Failed to retrieve test session");
    }

    // Get initial statistics
    const stats = database.getStats();
    console.log(chalk.blue("\n📊 Initial Database Statistics:"));
    console.log(chalk.cyan(`  Total posts: ${stats.totalPosts}`));
    console.log(chalk.cyan(`  Total media files: ${stats.totalMediaFiles}`));
    console.log(chalk.cyan(`  Total sessions: ${stats.totalSessions}`));
    console.log(chalk.cyan(`  Active sessions: ${stats.activeSessions}`));

    // Close database connection
    database.close();

    console.log(chalk.green("\n🎉 Database setup completed successfully!"));
    console.log(chalk.blue("\n📝 Next steps:"));
    console.log(
      chalk.cyan("  1. Copy .env.example to .env and configure your settings"),
    );
    console.log(
      chalk.cyan(
        '  2. Run: bun run crawl --platform douyin --keywords "sabah,tourism"',
      ),
    );
    console.log(chalk.cyan("  3. Check logs in ./logs/ directory"));
    console.log(
      chalk.cyan("  4. View downloaded media in ./output/ directory"),
    );
  } catch (error) {
    console.error(
      chalk.red(`❌ Database setup failed: ${(error as Error).message}`),
    );
    logger.error("Database setup error", { error });
    process.exit(1);
  }
}

// Check if .env file exists
if (!fs.existsSync(".env")) {
  console.log(
    chalk.yellow("⚠️  No .env file found. Creating from .env.example..."),
  );

  if (fs.existsSync(".env.example")) {
    fs.copyFileSync(".env.example", ".env");
    console.log(chalk.green("✅ Created .env file from .env.example"));
    console.log(
      chalk.blue(
        "📝 Please review and update the .env file with your settings",
      ),
    );
  } else {
    console.log(chalk.red("❌ .env.example file not found"));
    process.exit(1);
  }
}

// Run setup
setupDatabase().catch((error) => {
  console.error(chalk.red(`❌ Unexpected error: ${error.message}`));
  process.exit(1);
});
