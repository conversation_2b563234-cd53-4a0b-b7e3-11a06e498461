export interface ScrapedProduct {
  productName: string;
  companyName: string;
  certificateNumber?: string;
  certificateType?: string;
  issuedDate?: string;
  expiryDate?: string;
  status?: string;
  category?: string;
  subcategory?: string;
  address?: string;
  state?: string;
  country?: string;
  contactInfo?: string;
  website?: string;
  sourceUrl: string;
  rawData: string; // JSON string of original scraped data
}

export interface CrawlerConfig {
  maxPages: number;
  concurrentPages: number;
  delayBetweenPages: number;
  headless: boolean;
  timeout: number;
}

export interface CrawlerStats {
  totalPages: number;
  successfulPages: number;
  failedPages: number;
  totalProducts: number;
  duplicateProducts: number;
  startTime: Date;
  endTime?: Date;
  errors: string[];
}

export interface NextPageElement {
  text: string;
  href: string;
  onclick: string;
  outerHTML: string;
}

export interface PageData {
  url: string;
  pageNumber: number;
  products: ScrapedProduct[];
  hasNextPage: boolean;
  nextPageElement?: NextPageElement;
}
