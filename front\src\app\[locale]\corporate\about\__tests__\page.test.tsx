import { render, screen } from '@testing-library/react'
import { cleanupMocks, mockLanguageContext } from '@/test-utils'
import AboutPage from '../page'

// Mock the language context
jest.mock('@/lib/language-context', () => ({
  useLanguage: () => mockLanguageContext,
}))

// Mock the PageWrapper component
jest.mock('@/components/page-wrapper', () => ({
  PageWrapper: ({
    children,
    title,
    titleBM,
    description,
    descriptionBM,
    breadcrumbs,
  }: any) => (
    <div data-testid="page-wrapper">
      <h1>{mockLanguageContext.language === 'en' ? title : titleBM}</h1>
      <p>
        {mockLanguageContext.language === 'en' ? description : descriptionBM}
      </p>
      <div data-testid="breadcrumbs">{JSON.stringify(breadcrumbs)}</div>
      {children}
    </div>
  ),
}))

describe('About Page', () => {
  beforeEach(() => {
    cleanupMocks()
  })

  it('should render page title and description in English', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(screen.getByText('About Us')).toBeInTheDocument()
    expect(
      screen.getByText(/Learn about JAKIM's Halal Management Division/)
    ).toBeInTheDocument()
  })

  it('should render page title and description in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<AboutPage />)

    expect(screen.getByText('Tentang Kami')).toBeInTheDocument()
    expect(
      screen.getByText(/Ketahui tentang Bahagian Pengurusan Halal JAKIM/)
    ).toBeInTheDocument()
  })

  it('should render correct breadcrumbs', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    const breadcrumbsElement = screen.getByTestId('breadcrumbs')
    const breadcrumbs = JSON.parse(breadcrumbsElement.textContent || '[]')

    expect(breadcrumbs).toHaveLength(3)
    expect(breadcrumbs[0]).toEqual({ label: 'Home', href: '/' })
    expect(breadcrumbs[1]).toEqual({ label: 'Corporate', href: '/corporate' })
    expect(breadcrumbs[2]).toEqual({
      label: 'About Us',
      href: '/corporate/about',
    })
  })

  it('should render main content sections in English', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(
      screen.getByText('About JAKIM Halal Management Division')
    ).toBeInTheDocument()
    expect(screen.getByText('Our Role & Responsibilities')).toBeInTheDocument()
    expect(screen.getByText('Our History')).toBeInTheDocument()
    expect(screen.getByText('Contact Information')).toBeInTheDocument()
  })

  it('should render main content sections in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<AboutPage />)

    expect(
      screen.getByText('Tentang Bahagian Pengurusan Halal JAKIM')
    ).toBeInTheDocument()
    expect(screen.getByText('Peranan & Tanggungjawab Kami')).toBeInTheDocument()
    expect(screen.getByText('Sejarah Kami')).toBeInTheDocument()
    expect(screen.getByText('Maklumat Hubungan')).toBeInTheDocument()
  })

  it('should render role and responsibilities items in English', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(screen.getByText('Halal Certification')).toBeInTheDocument()
    expect(screen.getByText('Standards Development')).toBeInTheDocument()
    expect(screen.getByText('Monitoring & Enforcement')).toBeInTheDocument()
    expect(screen.getByText('International Recognition')).toBeInTheDocument()
    expect(screen.getByText('Training & Education')).toBeInTheDocument()
    expect(screen.getByText('Research & Development')).toBeInTheDocument()
  })

  it('should render role and responsibilities items in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<AboutPage />)

    expect(screen.getByText('Pensijilan Halal')).toBeInTheDocument()
    expect(screen.getByText('Pembangunan Piawaian')).toBeInTheDocument()
    expect(screen.getByText('Pemantauan & Penguatkuasaan')).toBeInTheDocument()
    expect(screen.getByText('Pengiktirafan Antarabangsa')).toBeInTheDocument()
    expect(screen.getByText('Latihan & Pendidikan')).toBeInTheDocument()
    expect(screen.getByText('Penyelidikan & Pembangunan')).toBeInTheDocument()
  })

  it('should render contact information', () => {
    render(<AboutPage />)

    expect(screen.getByText('BAHAGIAN PENGURUSAN HALAL,')).toBeInTheDocument()
    expect(
      screen.getByText('JABATAN KEMAJUAN ISLAM MALAYSIA,')
    ).toBeInTheDocument()
    expect(screen.getByText('03-8892 5000')).toBeInTheDocument()
    expect(screen.getByText('03-8892 5005')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('should render introduction text in English', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(
      screen.getByText(/The Halal Management Division.*is the sole authority/)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Our division plays a crucial role/)
    ).toBeInTheDocument()
  })

  it('should render introduction text in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<AboutPage />)

    expect(
      screen.getByText(
        /Bahagian Pengurusan Halal.*adalah pihak berkuasa tunggal/
      )
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Bahagian kami memainkan peranan penting/)
    ).toBeInTheDocument()
  })

  it('should render history section content in English', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(
      screen.getByText(/The journey of Halal certification in Malaysia/)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Today, Malaysian Halal certification is recognized/)
    ).toBeInTheDocument()
  })

  it('should render history section content in Bahasa Malaysia', () => {
    mockLanguageContext.language = 'bm'
    render(<AboutPage />)

    expect(
      screen.getByText(/Perjalanan pensijilan Halal di Malaysia/)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/Hari ini, pensijilan Halal Malaysia diiktiraf/)
    ).toBeInTheDocument()
  })

  it('should render address and contact details sections', () => {
    mockLanguageContext.language = 'en'
    render(<AboutPage />)

    expect(screen.getByText('Address')).toBeInTheDocument()
    expect(screen.getByText('Contact Details')).toBeInTheDocument()
    expect(screen.getByText('Phone:')).toBeInTheDocument()
    expect(screen.getByText('Fax:')).toBeInTheDocument()
    expect(screen.getByText('Email:')).toBeInTheDocument()
  })

  it('should render proper card structure', () => {
    render(<AboutPage />)

    const cards = screen.getAllByText((_content, element) => {
      return element?.className?.includes('card') || false
    })

    // Should have multiple card sections
    expect(cards.length).toBeGreaterThan(0)
  })

  it('should have proper semantic structure', () => {
    render(<AboutPage />)

    // Should have main heading
    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument()

    // Should have multiple sections with headings
    const headings = screen.getAllByRole('heading')
    expect(headings.length).toBeGreaterThan(1)
  })
})
