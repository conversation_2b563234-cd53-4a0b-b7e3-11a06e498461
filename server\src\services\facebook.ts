import crypto from 'node:crypto'
import axios from 'axios'
import type {
  FacebookConfig,
  FacebookSendMessageRequest,
  FacebookSendMessageResponse,
  FacebookUserProfile,
} from '../types'
import type DatabaseService from './database'

class FacebookService {
  private config: FacebookConfig | null = null
  private readonly baseUrl = 'https://graph.facebook.com/v18.0'
  private databaseService: DatabaseService | null = null
  private siteId = 1 // Default site ID

  constructor(databaseService?: DatabaseService, siteId?: number) {
    this.databaseService = databaseService || null
    this.siteId = siteId || 1
    this.loadConfig()
  }

  /**
   * Initialize the service with database service and site ID
   */
  initialize(databaseService: DatabaseService, siteId = 1): void {
    console.log('🔧 FacebookService.initialize called with:', {
      hasDatabaseService: !!databaseService,
      databaseServiceType: databaseService?.constructor?.name,
      siteId: siteId,
    })
    this.databaseService = databaseService
    this.siteId = siteId
    console.log(
      '🔧 FacebookService.initialize completed, databaseService set:',
      !!this.databaseService,
    )
    this.loadConfig()
  }

  private async loadConfig(): Promise<void> {
    try {
      if (this.databaseService) {
        this.config = await this.databaseService.getFacebookConfig(this.siteId)
      }
    } catch (error) {
      console.error('Failed to load Facebook config:', error)
    }
  }

  async updateConfig(
    newConfig: Omit<
      FacebookConfig,
      'id' | 'createdAt' | 'updatedAt' | 'siteId'
    >,
  ): Promise<void> {
    try {
      console.log('🔄 FacebookService.updateConfig called with:', {
        pageId: newConfig.pageId,
        hasDatabase: !!this.databaseService,
        siteId: this.siteId,
      })

      if (!this.databaseService) {
        console.error(
          '❌ Database service not initialized, falling back to memory storage',
        )
        console.error(
          '❌ This means the Facebook service was not properly initialized with a database service',
        )
        console.error(
          '❌ Configuration will be stored in memory only and will not persist',
        )
        // Fallback to memory storage
        this.config = {
          ...newConfig,
          id: 1,
          siteId: this.siteId,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
        console.log('✅ Configuration stored in memory:', {
          pageId: this.config.pageId,
          id: this.config.id,
        })
        return
      }

      // Add siteId to the config
      const configWithSiteId = {
        ...newConfig,
        siteId: this.siteId,
      }

      console.log('💾 Saving to database with config:', {
        pageId: configWithSiteId.pageId,
        siteId: configWithSiteId.siteId,
      })

      // Save to database
      await this.databaseService.saveFacebookConfig(configWithSiteId)
      console.log('✅ Saved to database successfully')

      // Reload config from database to get the complete saved config
      this.config = await this.databaseService.getFacebookConfig(this.siteId)
      console.log('✅ Reloaded config from database:', {
        id: this.config?.id,
        pageId: this.config?.pageId,
        siteId: this.config?.siteId,
      })
    } catch (error) {
      console.error('❌ Failed to update Facebook config:', error)
      throw error
    }
  }

  isConfigured(): boolean {
    return this.config?.isActive
  }

  getConfig(): FacebookConfig | null {
    return this.config
  }

  // Verify webhook signature for security
  verifyWebhookSignature(payload: string, signature: string): boolean {
    if (!this.config) {
      return false
    }

    const expectedSignature = crypto
      .createHmac('sha256', this.config.appSecret)
      .update(payload)
      .digest('hex')

    return crypto.timingSafeEqual(
      Buffer.from(`sha256=${expectedSignature}`),
      Buffer.from(signature),
    )
  }

  // Verify webhook challenge for initial setup
  verifyWebhookChallenge(
    mode: string,
    token: string,
    challenge: string,
  ): string | null {
    if (!this.config) {
      return null
    }

    if (mode === 'subscribe' && token === this.config.verifyToken) {
      return challenge
    }
    return null
  }

  // Send text message via Facebook Messenger API
  async sendTextMessage(
    recipientId: string,
    message: string,
  ): Promise<FacebookSendMessageResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'Facebook Messenger not configured',
      }
    }

    try {
      const requestData: FacebookSendMessageRequest = {
        recipient: {
          id: recipientId,
        },
        message: {
          text: message,
        },
        messaging_type: 'RESPONSE',
      }

      const response = await axios.post(
        `${this.baseUrl}/me/messages`,
        requestData,
        {
          headers: {
            Authorization: `Bearer ${this.config?.pageAccessToken}`,
            'Content-Type': 'application/json',
          },
        },
      )

      const messageId = response.data.message_id

      // Log the outbound message to database
      if (this.databaseService) {
        await this.databaseService.logFacebookMessage(
          messageId,
          this.config!.pageId,
          recipientId,
          'text',
          message,
          'outbound',
        )
      }

      return {
        success: true,
        messageId,
      }
    } catch (error: any) {
      console.error(
        'Facebook send message error:',
        error.response?.data || error.message,
      )
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      }
    }
  }

  // Send image message via Facebook Messenger API
  async sendImageMessage(
    recipientId: string,
    imageUrl: string,
    caption?: string,
  ): Promise<FacebookSendMessageResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'Facebook Messenger not configured',
      }
    }

    try {
      const requestData: FacebookSendMessageRequest = {
        recipient: {
          id: recipientId,
        },
        message: {
          attachment: {
            type: 'image',
            payload: {
              url: imageUrl,
              is_reusable: true,
            },
          },
        },
        messaging_type: 'RESPONSE',
      }

      const response = await axios.post(
        `${this.baseUrl}/me/messages`,
        requestData,
        {
          headers: {
            Authorization: `Bearer ${this.config?.pageAccessToken}`,
            'Content-Type': 'application/json',
          },
        },
      )

      const messageId = response.data.message_id

      // Send caption as separate message if provided
      if (caption) {
        await this.sendTextMessage(recipientId, caption)
      }

      // Log the outbound message to database
      if (this.databaseService) {
        await this.databaseService.logFacebookMessage(
          messageId,
          this.config?.pageId || '',
          recipientId,
          'image',
          caption || 'Image',
          'outbound',
          imageUrl,
        )
      }

      return {
        success: true,
        messageId,
      }
    } catch (error: any) {
      console.error(
        'Facebook send image error:',
        error.response?.data || error.message,
      )
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      }
    }
  }

  // Get user profile information
  async getUserProfile(userId: string): Promise<FacebookUserProfile | null> {
    if (!this.isConfigured()) {
      return null
    }

    try {
      const response = await axios.get(`${this.baseUrl}/${userId}`, {
        params: {
          fields: 'first_name,last_name,profile_pic,locale,timezone,gender',
          access_token: this.config?.pageAccessToken,
        },
      })

      return response.data
    } catch (error: any) {
      console.error(
        'Facebook get user profile error:',
        error.response?.data || error.message,
      )
      return null
    }
  }

  // Get recent conversations to help find valid PSIDs
  async getRecentConversations(): Promise<{
    success: boolean
    conversations?: any[]
    error?: string
  }> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'Facebook Messenger not configured',
      }
    }

    try {
      // Get conversations from the page
      const response = await axios.get(`${this.baseUrl}/me/conversations`, {
        params: {
          fields: 'participants,updated_time,message_count',
          limit: 10,
          access_token: this.config?.pageAccessToken,
        },
      })

      const conversations = response.data.data.map((conv: any) => {
        // Find the participant that's not the page
        const participant = conv.participants.data.find(
          (p: any) => p.id !== this.config?.pageId,
        )
        return {
          psid: participant?.id || 'Unknown',
          name: participant?.name || 'Unknown User',
          updatedTime: conv.updated_time,
          messageCount: conv.message_count,
        }
      })

      return {
        success: true,
        conversations,
      }
    } catch (error: any) {
      console.error(
        'Facebook get conversations error:',
        error.response?.data || error.message,
      )
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      }
    }
  }

  // Test configuration by sending a test message to the page
  async testConfiguration(): Promise<{ success: boolean; error?: string }> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'Facebook Messenger not configured',
      }
    }

    try {
      // Test by getting page information
      const response = await axios.get(`${this.baseUrl}/me`, {
        params: {
          fields: 'name,id',
          access_token: this.config?.pageAccessToken,
        },
      })

      if (response.data.id === this.config?.pageId) {
        return { success: true }
      }
      return {
        success: false,
        error: 'Page ID mismatch',
      }
    } catch (error: any) {
      console.error(
        'Facebook test configuration error:',
        error.response?.data || error.message,
      )
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      }
    }
  }
}

// Create and export singleton instance
const facebookService = new FacebookService()
export default facebookService
