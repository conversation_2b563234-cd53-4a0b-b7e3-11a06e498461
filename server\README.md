# Halal Chat Server

Express.js server for the Halal Chat application with OpenAI integration, supporting text chat, voice transcription, and image analysis.

## Features

- **Text Chat**: Send text messages to OpenAI GPT models
- **Voice Transcription**: Upload audio files for transcription using Whisper
- **Image Analysis**: Upload images for analysis using gpt-4.1 Vision
- **File Upload**: Secure file upload with validation and size limits
- **Rate Limiting**: Protection against abuse
- **CORS Support**: Configured for frontend integration
- **Error Handling**: Comprehensive error handling and logging

### Database Structure

- **users**: Unified user accounts with multi-role support (ADMIN, EDITOR, AGENT, SUPERVISOR)
- **whatsapp_config**: WhatsApp Business API configuration
- **whatsapp_messages**: Message history and logging
- **chat_sessions**: Chat session management for agent handover
- **session_assignments**: Agent-to-session assignments
- **handover_requests**: Agent handover request management

### Seeded Data

The database comes pre-populated with:

- 5 users with different roles:
  - `admin` / `admin123` (ADMIN role)
  - `testadmin` / `test123` (EDITOR role)
  - `agent1` / `password123` (AGENT role)
  - `agent2` / `password123` (AGENT role)
  - `supervisor1` / `password123` (SUPERVISOR role)
  - `superuser` / `password123` (ADMIN + AGENT roles - multi-role example)
- Sample WhatsApp configuration
- 16 realistic conversation messages covering halal certification topics

### Database Commands

```bash
# Seed the database with test data
npm run db:seed

# Reset database schema
npm run db:schema

# Full database reset (schema + seed)
npm run db:reset

```

To generate migrations, use:

```bash
make db-migrate-add name=add_table_xyz

# Or for auto-generated migration based on table changes
make db-migrate-autoadd name=add_table_xyz

```

to aplly migrations

```
make db-migrate-up
```

## Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

## Installation

1. Clone the repository and navigate to the server directory:

```bash
cd server
```

2. Install dependencies:

```bash
npm install
```

3. Create environment file:

```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:

```env
OPENAI_API_KEY=your_openai_api_key_here
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
```

## Usage

### Development

```bash
npm run dev
```

### Production

```bash
npm start
```

The server will start on the port specified in your `.env` file (default: 3001).

## API Endpoints

### Chat Endpoints

#### Create Chat Session

```
POST /api/chat/session
```

Creates a new chat session and returns a session ID.

#### Send Text Message

```
POST /api/chat/message
Content-Type: application/json

{
  "sessionId": "uuid",
  "message": "Hello, how are you?",
  "model": "gpt-4.1-mini" // optional
}
```

#### Analyze Image

```
POST /api/chat/image
Content-Type: application/json

{
  "sessionId": "uuid",
  "imageUrl": "http://example.com/image.jpg",
  "prompt": "What's in this image?", // optional
  "model": "gpt-4.1-mini" // optional
}
```

#### Get Chat Session

```
GET /api/chat/session/:sessionId
```

#### Clear Chat Session

```
DELETE /api/chat/session/:sessionId
```

### Upload Endpoints

#### Upload File

```
POST /api/upload
Content-Type: multipart/form-data

Form data:
- file: Image or audio file
```

**Supported formats:**

- Images: JPEG, PNG, GIF, WebP
- Audio: MP3, WAV, WebM, OGG

**Response for images:**

```json
{
  "type": "image",
  "url": "http://localhost:3001/uploads/filename.jpg",
  "filename": "uuid.jpg",
  "originalFilename": "original.jpg",
  "size": 1024000,
  "mimetype": "image/jpeg"
}
```

**Response for audio:**

```json
{
  "type": "audio",
  "transcription": "Hello, this is the transcribed text",
  "originalFilename": "recording.wav"
}
```

#### Delete File

```
DELETE /api/upload/:filename
```

### Health Check

```
GET /health
```

## File Upload Configuration

- **Maximum file size**: 10MB (configurable via `MAX_FILE_SIZE`)
- **Allowed image types**: JPEG, PNG, GIF, WebP
- **Allowed audio types**: MP3, WAV, WebM, OGG
- **Upload directory**: `uploads/` (configurable via `UPLOAD_DIR`)

## Security Features

- **Helmet**: Security headers
- **Rate limiting**: 100 requests per 15 minutes per IP
- **CORS**: Configured for specific frontend origin
- **File validation**: Type and size validation for uploads
- **Error handling**: Sanitized error responses

## Environment Variables

| Variable         | Description                  | Default               |
| ---------------- | ---------------------------- | --------------------- |
| `OPENAI_API_KEY` | Your OpenAI API key          | Required              |
| `PORT`           | Server port                  | 3001                  |
| `NODE_ENV`       | Environment mode             | development           |
| `FRONTEND_URL`   | Frontend URL for CORS        | http://localhost:3000 |
| `MAX_FILE_SIZE`  | Maximum upload size in bytes | 10485760 (10MB)       |
| `UPLOAD_DIR`     | Upload directory name        | uploads               |

## Error Handling

The server includes comprehensive error handling:

- **Validation errors**: 400 Bad Request
- **File upload errors**: 400 Bad Request with specific messages
- **OpenAI API errors**: 500 Internal Server Error
- **Not found**: 404 Not Found
- **Rate limiting**: 429 Too Many Requests

## Development

### Running Tests

```bash
npm test
```

### Code Structure

```
src/
├── index.js              # Main server file
├── routes/
│   ├── chat.js           # Chat API routes
│   └── upload.js         # File upload routes
├── services/
│   └── openai.js         # OpenAI service wrapper
└── middleware/
    └── upload.js         # File upload middleware
```

## Deployment

1. Set `NODE_ENV=production`
2. Configure production environment variables
3. Ensure OpenAI API key is set
4. Start with `npm start`

## Message handling

See [MESSAGE_HANDLER.md](./MESSAGE_HANDLER.md) for details on the consolidated message handler.
