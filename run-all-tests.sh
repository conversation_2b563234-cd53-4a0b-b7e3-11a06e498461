#!/bin/bash

# Comprehensive test runner for the entire project
# This script runs all tests with proper .test.ts/.test.tsx naming conventions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        print_error "Directory $1 does not exist"
        return 1
    fi
    return 0
}

# Function to run tests in a directory
run_tests() {
    local dir=$1
    local name=$2
    
    print_status "Running $name tests..."
    
    if ! check_directory "$dir"; then
        return 1
    fi
    
    cd "$dir"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "No package.json found in $dir"
        cd ..
        return 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies for $name..."
        if command -v pnpm &> /dev/null; then
            pnpm install
        else
            npm install
        fi
    fi
    
    # Run tests
    if command -v pnpm &> /dev/null; then
        pnpm test --passWithNoTests
    else
        npm test -- --passWithNoTests
    fi
    
    local exit_code=$?
    cd ..
    
    if [ $exit_code -eq 0 ]; then
        print_success "$name tests passed"
    else
        print_error "$name tests failed"
    fi
    
    return $exit_code
}

# Function to run tests with coverage
run_tests_with_coverage() {
    local dir=$1
    local name=$2
    
    print_status "Running $name tests with coverage..."
    
    if ! check_directory "$dir"; then
        return 1
    fi
    
    cd "$dir"
    
    if command -v pnpm &> /dev/null; then
        pnpm test:coverage --passWithNoTests
    else
        npm run test:coverage -- --passWithNoTests
    fi
    
    local exit_code=$?
    cd ..
    
    if [ $exit_code -eq 0 ]; then
        print_success "$name coverage tests passed"
    else
        print_error "$name coverage tests failed"
    fi
    
    return $exit_code
}

# Main execution
main() {
    print_status "Starting comprehensive test suite..."
    print_status "Testing all .test.ts and .test.tsx files"
    
    local overall_exit_code=0
    
    # Test frontend
    print_status "=== Frontend Tests ==="
    if ! run_tests "front" "Frontend"; then
        overall_exit_code=1
    fi
    
    # Test server
    print_status "=== Server Tests ==="
    if ! run_tests "server" "Server"; then
        overall_exit_code=1
    fi
    
    # Run coverage tests if requested
    if [ "$1" = "--coverage" ]; then
        print_status "=== Coverage Tests ==="
        
        print_status "Frontend Coverage:"
        if ! run_tests_with_coverage "front" "Frontend"; then
            overall_exit_code=1
        fi
        
        print_status "Server Coverage:"
        if ! run_tests_with_coverage "server" "Server"; then
            overall_exit_code=1
        fi
    fi
    
    # Summary
    echo ""
    print_status "=== Test Summary ==="
    
    if [ $overall_exit_code -eq 0 ]; then
        print_success "All tests passed! ✅"
        print_status "Test files follow .test.ts/.test.tsx naming convention"
        print_status "Tests use actual implementations instead of excessive mocking"
    else
        print_error "Some tests failed! ❌"
        print_status "Check the output above for details"
    fi
    
    # List all test files found
    print_status "=== Test Files Found ==="
    echo "Frontend test files:"
    find front/src -name "*.test.ts" -o -name "*.test.tsx" | sort
    
    echo ""
    echo "Server test files:"
    find server/src -name "*.test.ts" -o -name "*.test.tsx" | sort
    
    exit $overall_exit_code
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --coverage    Run tests with coverage reports"
    echo "  --help        Show this help message"
    echo ""
    echo "This script runs all tests in the project using the .test.ts/.test.tsx naming convention."
    echo "It avoids excessive mocking and uses actual implementations where possible."
}

# Parse command line arguments
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    --coverage)
        main --coverage
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
