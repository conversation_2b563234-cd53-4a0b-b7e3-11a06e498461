'use client'

import { AlertCircle, CheckCircle, Info, X, XCircle } from 'lucide-react'
import { useEffect, useState } from 'react'
import { type Notification, useNotifications } from '@/lib/notifications'

interface NotificationToastProps {
  notification: Notification
  onDismiss: (id: string) => void
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

export function NotificationToast({
  notification,
  onDismiss,
  position = 'top-right',
}: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 100)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Auto-dismiss non-urgent notifications after 5 seconds
    if (!notification.urgent) {
      const timer = setTimeout(() => {
        handleDismiss()
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [notification.urgent])

  const handleDismiss = () => {
    setIsExiting(true)
    setTimeout(() => {
      onDismiss(notification.id)
    }, 300)
  }

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getBgColor = () => {
    const baseClasses = notification.urgent ? 'ring-2' : ''
    switch (notification.type) {
      case 'success':
        return `bg-green-50 border-green-200 ${notification.urgent ? 'ring-green-500' : ''} ${baseClasses}`
      case 'error':
        return `bg-red-50 border-red-200 ${notification.urgent ? 'ring-red-500' : ''} ${baseClasses}`
      case 'warning':
        return `bg-yellow-50 border-yellow-200 ${notification.urgent ? 'ring-yellow-500' : ''} ${baseClasses}`
      case 'info':
      default:
        return `bg-blue-50 border-blue-200 ${notification.urgent ? 'ring-blue-500' : ''} ${baseClasses}`
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'top-right':
      default:
        return 'top-4 right-4'
    }
  }

  const getAnimationClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-in-out'

    if (isExiting) {
      return `${baseClasses} opacity-0 transform translate-x-full`
    }

    if (isVisible) {
      return `${baseClasses} opacity-100 transform translate-x-0`
    }

    return `${baseClasses} opacity-0 transform translate-x-full`
  }

  return (
    <div
      className={`fixed z-50 max-w-sm w-full ${getPositionClasses()} ${getAnimationClasses()}`}
    >
      <div className={`rounded-lg border p-4 shadow-lg ${getBgColor()}`}>
        <div className="flex items-start">
          <div className="flex-shrink-0">{getIcon()}</div>

          <div className="ml-3 flex-1">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {notification.title}
                  {notification.urgent && (
                    <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                      Urgent
                    </span>
                  )}
                </p>
                <p className="mt-1 text-sm text-gray-600">
                  {notification.message}
                </p>

                {notification.actionUrl && (
                  <div className="mt-2">
                    <a
                      href={notification.actionUrl}
                      className="text-sm font-medium text-blue-600 hover:text-blue-800"
                      onClick={handleDismiss}
                    >
                      View Details →
                    </a>
                  </div>
                )}
              </div>

              <button
                type="button"
                onClick={handleDismiss}
                className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Progress bar for auto-dismiss */}
        {!notification.urgent && (
          <div className="mt-3 w-full bg-gray-200 rounded-full h-1">
            <div
              className="bg-gray-400 h-1 rounded-full transition-all duration-5000 ease-linear"
              style={{
                width: isVisible ? '0%' : '100%',
                transitionDuration: isVisible ? '5000ms' : '0ms',
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Container component for managing multiple toasts
export function NotificationToastContainer() {
  const { notifications, dismissNotification } = useNotifications()
  const [visibleNotifications, setVisibleNotifications] = useState<
    Notification[]
  >([])

  useEffect(() => {
    // Only show unread, non-dismissed notifications as toasts
    const toastNotifications = notifications
      .filter(n => !n.read && !n.dismissed)
      .slice(0, 5) // Limit to 5 toasts at once

    setVisibleNotifications(toastNotifications)
  }, [notifications])

  const handleDismiss = (id: string) => {
    dismissNotification(id)
    setVisibleNotifications(prev => prev.filter(n => n.id !== id))
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {visibleNotifications.map((notification, index) => (
        <div
          key={notification.id}
          className="pointer-events-auto"
          style={{
            transform: `translateY(${index * 80}px)`,
          }}
        >
          <NotificationToast
            notification={notification}
            onDismiss={handleDismiss}
            position="top-right"
          />
        </div>
      ))}
    </div>
  )
}

// Hook for triggering system notifications
export function useSystemNotifications() {
  const { addNotification } = useNotifications()

  const notifyHandoverRequest = (sessionId: string, customerName?: string) => {
    addNotification({
      type: 'warning',
      title: 'New Handover Request',
      message: `${customerName || 'A customer'} has requested agent assistance`,
      urgent: true,
      actionUrl: `/agent/chat/${sessionId}`,
    })
  }

  const notifyAgentAssignment = (sessionId: string, agentName: string) => {
    addNotification({
      type: 'info',
      title: 'Agent Assigned',
      message: `${agentName} has been assigned to handle the session`,
      actionUrl: '/admin/sessions',
    })
  }

  const notifySessionCompleted = (sessionId: string, rating?: number) => {
    addNotification({
      type: 'success',
      title: 'Session Completed',
      message: rating
        ? `Session completed with ${rating}/5 rating`
        : 'Session has been completed',
      actionUrl: '/admin/sessions',
    })
  }

  const notifySystemAlert = (
    message: string,
    type: 'error' | 'warning' = 'warning'
  ) => {
    addNotification({
      type,
      title: 'System Alert',
      message,
      urgent: type === 'error',
    })
  }

  const notifyAgentStatusChange = (agentName: string, isOnline: boolean) => {
    addNotification({
      type: 'info',
      title: 'Agent Status Update',
      message: `${agentName} is now ${isOnline ? 'online' : 'offline'}`,
    })
  }

  return {
    notifyHandoverRequest,
    notifyAgentAssignment,
    notifySessionCompleted,
    notifySystemAlert,
    notifyAgentStatusChange,
  }
}
