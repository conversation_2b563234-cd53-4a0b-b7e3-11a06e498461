'use client'

import {
  AlertTriangle,
  Calendar,
  ChevronRight,
  FileText,
  Megaphone,
} from 'lucide-react'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'
import { cn } from '@/lib/utils'
import type { Announcement } from '@/types'

interface AnnouncementCardProps {
  announcement: Announcement
  variant?: 'default' | 'compact' | 'featured'
  showCategory?: boolean
  showExcerpt?: boolean
  className?: string
}

export function AnnouncementCard({
  announcement,
  variant = 'default',
  showCategory = true,
  showExcerpt = true,
  className,
}: AnnouncementCardProps) {
  const { language } = useLanguage()

  const title = language === 'bm' ? announcement.titleBM : announcement.title
  const content =
    language === 'bm' ? announcement.contentBM : announcement.content

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'media-statement':
        return <Megaphone className="w-4 h-4" />
      case 'withdrawal':
        return <AlertTriangle className="w-4 h-4" />
      case 'announcement':
        return <FileText className="w-4 h-4" />
      default:
        return <FileText className="w-4 h-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'media-statement':
        return 'text-blue-600 bg-blue-100'
      case 'withdrawal':
        return 'text-red-600 bg-red-100'
      case 'announcement':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels = {
      'media-statement': { en: 'Media Statement', bm: 'Kenyataan Media' },
      withdrawal: { en: 'Withdrawal', bm: 'Penarikan Balik' },
      announcement: { en: 'Announcement', bm: 'Pengumuman' },
      general: { en: 'General', bm: 'Umum' },
    }
    return labels[category as keyof typeof labels]?.[language] || category
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString(language === 'bm' ? 'ms-MY' : 'en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const getExcerpt = (text: string, maxLength = 150) => {
    if (text.length <= maxLength) {
      return text
    }
    return `${text.substring(0, maxLength).trim()}...`
  }

  if (variant === 'compact') {
    return (
      <Link
        href={`/announcements/${announcement.id}`}
        className={cn(
          'block p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-all duration-200',
          announcement.featured && 'border-primary-green bg-bg-light-green',
          className
        )}
      >
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {showCategory && (
                <span
                  className={cn(
                    'inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full',
                    getCategoryColor(announcement.category)
                  )}
                >
                  {getCategoryIcon(announcement.category)}
                  {getCategoryLabel(announcement.category)}
                </span>
              )}
              <span className="text-xs text-gray-500 flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDate(announcement.date)}
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 line-clamp-2 mb-1">
              {title}
            </h3>
            {showExcerpt && (
              <p className="text-sm text-gray-600 line-clamp-2">
                {getExcerpt(content)}
              </p>
            )}
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0 mt-1" />
        </div>
      </Link>
    )
  }

  if (variant === 'featured') {
    return (
      <div
        className={cn(
          'relative overflow-hidden rounded-xl bg-gradient-to-r from-primary-green to-islamic-green text-white',
          className
        )}
      >
        <div className="absolute inset-0 bg-black bg-opacity-20" />
        <div className="relative p-6 md:p-8">
          <div className="flex items-center gap-2 mb-4">
            <span className="inline-flex items-center gap-1 px-3 py-1 bg-white bg-opacity-20 text-white text-sm font-medium rounded-full">
              {getCategoryIcon(announcement.category)}
              {getCategoryLabel(announcement.category)}
            </span>
            <span className="text-white text-opacity-90 text-sm flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              {formatDate(announcement.date)}
            </span>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold mb-4 line-clamp-3">
            {title}
          </h2>
          <p className="text-white text-opacity-90 mb-6 line-clamp-3">
            {getExcerpt(content, 200)}
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Link
              href={`/announcements/${announcement.id}`}
              className="inline-flex items-center justify-center px-6 py-3 bg-white text-primary-green font-semibold rounded-lg hover:bg-gray-100 transition-colors"
            >
              {language === 'en' ? 'Read More' : 'Baca Lagi'}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Link>
            {announcement.pdfUrl && (
              <a
                href={announcement.pdfUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-primary-green transition-colors"
              >
                <FileText className="w-4 h-4 mr-2" />
                {language === 'en' ? 'Download PDF' : 'Muat Turun PDF'}
              </a>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('card', className)}>
      <div className="flex items-start justify-between gap-4 mb-4">
        {showCategory && (
          <span
            className={cn(
              'inline-flex items-center gap-1 px-3 py-1 text-sm font-medium rounded-full',
              getCategoryColor(announcement.category)
            )}
          >
            {getCategoryIcon(announcement.category)}
            {getCategoryLabel(announcement.category)}
          </span>
        )}
        <span className="text-sm text-gray-500 flex items-center gap-1 flex-shrink-0">
          <Calendar className="w-4 h-4" />
          {formatDate(announcement.date)}
        </span>
      </div>

      <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
        {title}
      </h3>

      {showExcerpt && (
        <p className="text-gray-600 mb-4 line-clamp-3">{getExcerpt(content)}</p>
      )}

      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100">
        <Link
          href={`/announcements/${announcement.id}`}
          className="inline-flex items-center justify-center px-4 py-2 text-primary-green hover:text-primary-green-dark font-medium transition-colors"
        >
          {language === 'en' ? 'Read More' : 'Baca Lagi'}
          <ChevronRight className="w-4 h-4 ml-1" />
        </Link>
        {announcement.pdfUrl && (
          <a
            href={announcement.pdfUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
          >
            <FileText className="w-4 h-4 mr-2" />
            {language === 'en' ? 'PDF' : 'PDF'}
          </a>
        )}
      </div>
    </div>
  )
}

// List component for multiple announcements
export function AnnouncementList({
  announcements,
  variant = 'default',
  showLoadMore = false,
  onLoadMore,
  className,
}: {
  announcements: Announcement[]
  variant?: 'default' | 'compact' | 'featured'
  showLoadMore?: boolean
  onLoadMore?: () => void
  className?: string
}) {
  const { language } = useLanguage()

  if (announcements.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {language === 'en'
          ? 'No announcements available'
          : 'Tiada pengumuman tersedia'}
      </div>
    )
  }

  return (
    <div className={className}>
      <div className={cn('space-y-6', variant === 'compact' && 'space-y-3')}>
        {announcements.map(announcement => (
          <AnnouncementCard
            key={announcement.id}
            announcement={announcement}
            variant={variant}
          />
        ))}
      </div>

      {showLoadMore && onLoadMore && (
        <div className="text-center mt-8">
          <button onClick={onLoadMore} className="btn-secondary">
            {language === 'en' ? 'Load More' : 'Muat Lebih Banyak'}
          </button>
        </div>
      )}
    </div>
  )
}
