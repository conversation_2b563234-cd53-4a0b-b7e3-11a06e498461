-- Ensure pgvector extension is enabled
CREATE EXTENSION IF NOT EXISTS vector;

-- Add missing vector fields to products table if they don't exist
DO $$
BEGIN
    -- Add r2r_document_id if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='products' AND column_name='r2r_document_id') THEN
        ALTER TABLE "products" ADD COLUMN "r2r_document_id" varchar(255);
    END IF;

    -- Add embedding if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='products' AND column_name='embedding') THEN
        ALTER TABLE "products" ADD COLUMN "embedding" vector(1536);
    END IF;

    -- Add vectorized_at if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='products' AND column_name='vectorized_at') THEN
        ALTER TABLE "products" ADD COLUMN "vectorized_at" timestamp;
    END IF;

    -- Add vectorization_model if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='products' AND column_name='vectorization_model') THEN
        ALTER TABLE "products" ADD COLUMN "vectorization_model" varchar(100);
    END IF;

    -- Add searchable_text if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='products' AND column_name='searchable_text') THEN
        ALTER TABLE "products" ADD COLUMN "searchable_text" text;
    END IF;
END $$;
