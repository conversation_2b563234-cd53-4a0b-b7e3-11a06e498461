#!/usr/bin/env node

/**
 * Database Inspection Utility
 *
 * Quick tool to inspect Twilio-related data in the database
 * Usage: node tests/inspect-database.js
 */

import 'dotenv/config'

async function inspectDatabase() {
  console.log('🔍 Database Inspection Tool')
  console.log('='.repeat(40))

  try {
    const { initializeDatabase } = await import('../db/connection.ts')
    const db = initializeDatabase()

    // Twilio configurations
    console.log('\n📞 Twilio Configurations:')
    const twilioConfigs = await db.query(`
      SELECT id, site_id, account_sid, phone_number, is_active, created_at 
      FROM twilio_configs 
      ORDER BY created_at DESC 
      LIMIT 5
    `)

    if (twilioConfigs.length === 0) {
      console.log('   📭 No configurations found')
      console.log('   💡 Run: pnpm db:seed:twilio')
    } else {
      twilioConfigs.forEach((config, i) => {
        console.log(
          `   ${i + 1}. Site ${config.site_id}: ${config.phone_number} (${config.is_active ? 'Active' : 'Inactive'})`,
        )
      })
    }

    // S3 configurations
    console.log('\n☁️  S3 Configurations:')
    const s3Configs = await db.query(`
      SELECT id, service_name, bucket_name, region, created_at 
      FROM s3_configurations 
      ORDER BY created_at DESC 
      LIMIT 3
    `)

    if (s3Configs.length === 0) {
      console.log('   📭 No S3 configurations found')
    } else {
      s3Configs.forEach((config, i) => {
        console.log(
          `   ${i + 1}. ${config.service_name}: ${config.bucket_name} (${config.region})`,
        )
      })
    }

    // Recent messages
    console.log('\n📨 Recent Twilio Messages (Last 5):')
    const recentMessages = await db.query(`
      SELECT id, "from", "to", body, type, status, direction, created_at 
      FROM twilio_messages 
      ORDER BY created_at DESC 
      LIMIT 5
    `)

    if (recentMessages.length === 0) {
      console.log('   📭 No messages found yet')
    } else {
      recentMessages.forEach((msg, i) => {
        const body = msg.body
          ? msg.body.substring(0, 50) + (msg.body.length > 50 ? '...' : '')
          : '[No text]'
        console.log(
          `   ${i + 1}. ${msg.direction.toUpperCase()}: ${body} (${msg.status})`,
        )
        console.log(`      ${msg.from} → ${msg.to} | ${msg.created_at}`)
      })
    }

    // Summary
    console.log('\n📊 Summary:')
    console.log(`   📞 Twilio Configs: ${twilioConfigs.length}`)
    console.log(`   ☁️  S3 Configs: ${s3Configs.length}`)
    console.log(`   📨 Messages: ${recentMessages.length}`)

    await db.$client.end()
  } catch (error) {
    console.error('❌ Database inspection failed:', error.message)
  }
}

inspectDatabase()
