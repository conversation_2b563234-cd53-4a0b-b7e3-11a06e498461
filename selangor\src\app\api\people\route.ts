import { eq } from 'drizzle-orm'
import { type NextRequest } from 'next/server'
import { db, people } from '@/lib/db'
import {
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-response'

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (slug) {
      // Get specific person by slug
      const person = await db.select().from(people).where(eq(people.slug, slug))

      if (person.length === 0) {
        return createErrorResponse('Person not found', undefined, 404)
      }

      return createSuccessResponse(person[0])
    }

    // Get all people
    const allPeople = await db.select().from(people)
    return createSuccessResponse(allPeople)
  } catch (error) {
    return handleApiError(error, 'Error fetching people')
  }
}
