'use client'

import { Edit, Plus, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { AdminLayout } from '@/components/admin/AdminLayout'
import { Button, type Column, Modal, Table } from '@/components/ui'

interface Company {
  id: number
  name: string
  registrationNumber?: string
  businessType?: string
  category?: string
  subcategory?: string
  address?: string
  state?: string
  postcode?: string
  city?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  contactPerson?: string
  certificateNumber?: string
  certificateType?: string
  certificateStatus?: string
  createdAt: string
  updatedAt: string
}

interface CompaniesResponse {
  companies: Company[]
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
    totalPages: number
  }
}

export default function CompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
    totalPages: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean
    company: Company | null
  }>({ isOpen: false, company: null })

  const fetchCompanies = async (page = 1) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/companies?page=${page}&limit=20`)
      if (response.ok) {
        const data: CompaniesResponse = await response.json()
        setCompanies(data.companies)
        setPagination(data.pagination)
      } else {
        console.error('Failed to fetch companies')
      }
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (company: Company) => {
    try {
      const response = await fetch(`/api/companies?id=${company.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchCompanies(pagination.page)
        setDeleteModal({ isOpen: false, company: null })
      } else {
        console.error('Failed to delete company')
      }
    } catch (error) {
      console.error('Error deleting company:', error)
    }
  }

  useEffect(() => {
    fetchCompanies()
  }, [])

  const columns: Column<Company>[] = [
    {
      key: 'name',
      header: 'Company Name',
      sortable: true,
    },
    {
      key: 'registrationNumber',
      header: 'Registration Number',
    },
    {
      key: 'businessType',
      header: 'Business Type',
    },
    {
      key: 'category',
      header: 'Category',
    },
    {
      key: 'certificateStatus',
      header: 'Certificate Status',
      render: company => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            company.certificateStatus === 'Active'
              ? 'bg-green-100 text-green-800'
              : company.certificateStatus === 'Expired'
                ? 'bg-red-100 text-red-800'
                : 'bg-gray-100 text-gray-800'
          }`}
        >
          {company.certificateStatus || 'Unknown'}
        </span>
      ),
    },
    {
      key: 'state',
      header: 'State',
    },
    {
      key: 'city',
      header: 'City',
    },
    {
      key: 'actions',
      header: 'Actions',
      isActions: true,
      render: company => (
        <div className="flex space-x-2">
          <Link href={`/cp1/companies/${company.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="danger"
            size="sm"
            onClick={() => setDeleteModal({ isOpen: true, company })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Companies</h1>
            <p className="text-gray-600">Manage halal certified companies</p>
          </div>
          <Link href="/cp1/companies/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Company
            </Button>
          </Link>
        </div>

        {/* Companies Table */}
        <Table
          data={companies}
          columns={columns}
          isLoading={isLoading}
          pagination={pagination}
          onPageChange={fetchCompanies}
        />

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={deleteModal.isOpen}
          onClose={() => setDeleteModal({ isOpen: false, company: null })}
          title="Delete Company"
        >
          <div className="space-y-4">
            <p className="text-gray-600">
              Are you sure you want to delete "{deleteModal.company?.name}"?
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setDeleteModal({ isOpen: false, company: null })}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={() =>
                  deleteModal.company && handleDelete(deleteModal.company)
                }
              >
                Delete
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </AdminLayout>
  )
}
