import { Suspense } from 'react'
import { Footer } from '@/components/Footer'
import { Header } from '@/components/Header'
import { fetchApiData } from '@/lib/api-client'

export const runtime = 'edge'

async function HomeContent() {
  try {
    const page = await fetchApiData(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:16010'}/api/pages?slug=home`,
      { cache: 'no-store' }
    )

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 py-20">
            <div className="text-center">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">
                Halal Selangor
              </h1>
              <p className="text-xl md:text-2xl text-green-100 mb-8 max-w-3xl mx-auto">
                Your trusted authority for Halal certification in Selangor
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  type="button"
                  className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  Apply for Certification
                </button>
                <button
                  type="button"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors"
                >
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="container mx-auto px-4 py-16 text-gray-600">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Why Choose Halal Selangor?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We provide comprehensive Halal certification services with the
              highest standards of integrity and compliance.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">🏆</div>
              <h3 className="text-xl font-bold mb-4 ">Certified Excellence</h3>
              <p className="text-gray-600">
                Recognized standards and rigorous certification processes ensure
                the highest quality Halal compliance.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">⚡</div>
              <h3 className="text-xl font-bold mb-4">Fast Processing</h3>
              <p className="text-gray-600">
                Streamlined application and review processes to get your
                certification quickly and efficiently.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">🤝</div>
              <h3 className="text-xl font-bold mb-4">Expert Support</h3>
              <p className="text-gray-600">
                Our team of experts provides guidance and support throughout
                your certification journey.
              </p>
            </div>
          </div>
        </div>

        {/* Dynamic Content */}
        {page && (
          <div className="container mx-auto px-4 py-12">
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: page.content }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="bg-green-600 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Get Certified?
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Start your Halal certification journey with Selangor&apos;s most
              trusted authority.
            </p>
            <button
              type="button"
              className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Start Application
            </button>
          </div>
        </div>
      </div>
    )
  } catch {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section - Fallback */}
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 py-20">
            <div className="text-center">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">
                Halal Selangor
              </h1>
              <p className="text-xl md:text-2xl text-green-100 mb-8 max-w-3xl mx-auto">
                Your trusted authority for Halal certification in Selangor
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  type="button"
                  className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  Apply for Certification
                </button>
                <button
                  type="button"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors"
                >
                  Learn More
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Why Choose Halal Selangor?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We provide comprehensive Halal certification services with the
              highest standards of integrity and compliance.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">🏆</div>
              <h3 className="text-xl font-bold mb-4">Certified Excellence</h3>
              <p className="text-gray-600">
                Recognized standards and rigorous certification processes ensure
                the highest quality Halal compliance.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">⚡</div>
              <h3 className="text-xl font-bold mb-4">Fast Processing</h3>
              <p className="text-gray-600">
                Streamlined application and review processes to get your
                certification quickly and efficiently.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-green-600 text-5xl mb-4">🤝</div>
              <h3 className="text-xl font-bold mb-4">Expert Support</h3>
              <p className="text-gray-600">
                Our team of experts provides guidance and support throughout
                your certification journey.
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-green-600 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Get Certified?
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Start your Halal certification journey with Selangor&apos;s most
              trusted authority.
            </p>
            <button
              type="button"
              className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Start Application
            </button>
          </div>
        </div>
      </div>
    )
  }
}

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense
          fallback={
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600" />
            </div>
          }
        >
          <HomeContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  )
}
