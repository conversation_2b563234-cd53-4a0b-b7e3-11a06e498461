/**
 * Contact CRUD Functionality Test
 *
 * This test verifies that all contact CRUD operations work correctly
 * with the backend API endpoints.
 */

import { api } from '@/lib/api'

interface ContactFormData {
  name: string
  title?: string
  department?: string
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email: string
  phone?: string
  mobile?: string
  address?: string
  city?: string
  state?: string
  postcode?: string
  country?: string
  website?: string
  workingHours?: string
  isActive: boolean
}

interface ContactInfo extends ContactFormData {
  id: number
  createdAt: string
  updatedAt: string
}

describe('Contact CRUD Operations', () => {
  let testContactId: number

  const testContactData: ContactFormData = {
    name: 'Test Contact',
    title: 'Test Manager',
    department: 'administration',
    type: 'general',
    email: '<EMAIL>',
    phone: '+60123456789',
    mobile: '+60198765432',
    address: '123 Test Street',
    city: 'Kuala Lumpur',
    state: 'Selangor',
    postcode: '50000',
    country: 'Malaysia',
    website: 'https://example.com',
    workingHours: 'Mon-Fri 9:00 AM - 5:00 PM',
    isActive: true,
  }

  // Test CREATE operation
  test('should create a new contact', async () => {
    try {
      const response = await api.post('/admin/contacts', testContactData)

      expect(response.success).toBe(true)
      expect(response.data).toBeDefined()
      expect(response.data.name).toBe(testContactData.name)
      expect(response.data.email).toBe(testContactData.email)
      expect(response.data.id).toBeDefined()

      testContactId = response.data.id
      console.log(
        '✅ CREATE: Contact created successfully with ID:',
        testContactId
      )
    } catch (error) {
      console.error('❌ CREATE: Failed to create contact:', error)
      throw error
    }
  })

  // Test READ operation (single contact)
  test('should fetch a single contact by ID', async () => {
    try {
      const response = await api.get(`/admin/contacts/${testContactId}`)

      expect(response.success).toBe(true)
      expect(response.data).toBeDefined()
      expect(response.data.id).toBe(testContactId)
      expect(response.data.name).toBe(testContactData.name)
      expect(response.data.email).toBe(testContactData.email)

      console.log('✅ READ: Contact fetched successfully:', response.data.name)
    } catch (error) {
      console.error('❌ READ: Failed to fetch contact:', error)
      throw error
    }
  })

  // Test READ operation (list contacts)
  test('should fetch contacts list', async () => {
    try {
      const response = await api.get('/admin/contacts?page=1&limit=10')

      expect(response.success).toBe(true)
      expect(response.data).toBeDefined()
      expect(response.data.contacts).toBeDefined()
      expect(Array.isArray(response.data.contacts)).toBe(true)
      expect(response.data.pagination).toBeDefined()

      // Check if our test contact is in the list
      const testContact = response.data.contacts.find(
        (c: ContactInfo) => c.id === testContactId
      )
      expect(testContact).toBeDefined()

      console.log(
        '✅ READ LIST: Contacts list fetched successfully, found',
        response.data.contacts.length,
        'contacts'
      )
    } catch (error) {
      console.error('❌ READ LIST: Failed to fetch contacts list:', error)
      throw error
    }
  })

  // Test UPDATE operation
  test('should update an existing contact', async () => {
    const updateData = {
      ...testContactData,
      name: 'Updated Test Contact',
      title: 'Updated Manager',
      phone: '+60111111111',
    }

    try {
      const response = await api.put(
        `/admin/contacts/${testContactId}`,
        updateData
      )

      expect(response.success).toBe(true)
      expect(response.data).toBeDefined()
      expect(response.data.name).toBe(updateData.name)
      expect(response.data.title).toBe(updateData.title)
      expect(response.data.phone).toBe(updateData.phone)

      console.log(
        '✅ UPDATE: Contact updated successfully:',
        response.data.name
      )
    } catch (error) {
      console.error('❌ UPDATE: Failed to update contact:', error)
      throw error
    }
  })

  // Test DELETE operation
  test('should delete a contact', async () => {
    try {
      const response = await api.delete(`/admin/contacts/${testContactId}`)

      expect(response.success).toBe(true)

      console.log('✅ DELETE: Contact deleted successfully')

      // Verify the contact is actually deleted
      try {
        await api.get(`/admin/contacts/${testContactId}`)
        throw new Error('Contact should have been deleted')
      } catch (error: any) {
        expect(error.response?.status).toBe(404)
        console.log('✅ DELETE VERIFICATION: Contact confirmed deleted')
      }
    } catch (error) {
      console.error('❌ DELETE: Failed to delete contact:', error)
      throw error
    }
  })

  // Test validation
  test('should validate required fields', async () => {
    const invalidData = {
      // Missing required name and email
      type: 'general',
      isActive: true,
    }

    try {
      await api.post('/admin/contacts', invalidData)
      throw new Error('Should have failed validation')
    } catch (error: any) {
      expect(error.response?.status).toBe(400)
      expect(error.response?.data?.error).toContain('required')
      console.log('✅ VALIDATION: Required field validation working correctly')
    }
  })

  // Test email validation
  test('should validate email format', async () => {
    const invalidEmailData = {
      name: 'Test Contact',
      email: 'invalid-email',
      type: 'general',
      isActive: true,
    }

    try {
      await api.post('/admin/contacts', invalidEmailData)
      throw new Error('Should have failed email validation')
    } catch (error: any) {
      expect(error.response?.status).toBe(400)
      expect(error.response?.data?.error).toContain('email')
      console.log(
        '✅ EMAIL VALIDATION: Email format validation working correctly'
      )
    }
  })

  // Test type validation
  test('should validate contact type', async () => {
    const invalidTypeData = {
      name: 'Test Contact',
      email: '<EMAIL>',
      type: 'invalid-type',
      isActive: true,
    }

    try {
      await api.post('/admin/contacts', invalidTypeData)
      throw new Error('Should have failed type validation')
    } catch (error: any) {
      expect(error.response?.status).toBe(400)
      expect(error.response?.data?.error).toContain('type')
      console.log(
        '✅ TYPE VALIDATION: Contact type validation working correctly'
      )
    }
  })
})

// Manual test runner function
export async function runContactCRUDTests() {
  console.log('🧪 Starting Contact CRUD Tests...\n')

  try {
    // Test data
    const testContactData: ContactFormData = {
      name: 'Manual Test Contact',
      title: 'Test Manager',
      department: 'administration',
      type: 'general',
      email: '<EMAIL>',
      phone: '+60123456789',
      mobile: '+60198765432',
      address: '123 Manual Test Street',
      city: 'Kuala Lumpur',
      state: 'Selangor',
      postcode: '50000',
      country: 'Malaysia',
      website: 'https://manual-test.example.com',
      workingHours: 'Mon-Fri 9:00 AM - 5:00 PM',
      isActive: true,
    }

    // CREATE
    console.log('1. Testing CREATE...')
    const createResponse = await api.post('/admin/contacts', testContactData)
    const contactId = createResponse.data.id
    console.log('✅ Contact created with ID:', contactId)

    // READ
    console.log('\n2. Testing READ...')
    const readResponse = await api.get(`/admin/contacts/${contactId}`)
    console.log('✅ Contact fetched:', readResponse.data.name)

    // UPDATE
    console.log('\n3. Testing UPDATE...')
    const updateData = {
      ...testContactData,
      name: 'Updated Manual Test Contact',
    }
    const updateResponse = await api.put(
      `/admin/contacts/${contactId}`,
      updateData
    )
    console.log('✅ Contact updated:', updateResponse.data.name)

    // DELETE
    console.log('\n4. Testing DELETE...')
    await api.delete(`/admin/contacts/${contactId}`)
    console.log('✅ Contact deleted successfully')

    console.log('\n🎉 All Contact CRUD tests passed!')
    return true
  } catch (error) {
    console.error('\n❌ Contact CRUD test failed:', error)
    return false
  }
}
