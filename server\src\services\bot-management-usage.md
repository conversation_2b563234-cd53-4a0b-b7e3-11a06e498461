# Bot Management Methods Usage Guide

## Overview

The DatabaseService class now provides comprehensive bot management functionality including CRUD operations and usage tracking.

## Available Methods

### Basic Bot Retrieval

- `getBotBySlug(slug: string, siteId?: number): Promise<Bot | null>`
- `getAllBots(siteId?: number): Promise<Bot[]>`
- `getDefaultBot(siteId?: number): Promise<Bot | null>`
- `getBotById(id: number): Promise<Bot | null>`

### Bot CRUD Operations

- `createBot(data: BotCreationRequest): Promise<Bot>`
- `updateBot(id: number, data: BotUpdateRequest): Promise<Bot | null>`
- `deleteBot(id: number): Promise<boolean>`

### Bot Usage Tracking

- `getBotUsage(botId: number): Promise<{ totalMessages: number; totalInputTokens: number; totalOutputTokens: number; }>`
- `getBotWithUsage(id: number): Promise<BotWithUsage | null>`
- `getAllBotsWithUsage(siteId?: number): Promise<BotWithUsage[]>`

## Usage Examples

### Creating a New Bot

```typescript
const botData: BotCreationRequest = {
  siteId: 1,
  name: 'Customer Support Bot',
  slug: 'customer-support',
  provider: 'openai',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  isDefault: false,
  systemPrompt: 'You are a helpful customer support assistant...',
}

const newBot = await dbService.createBot(botData)
```

### Updating a Bot

```typescript
const updateData: BotUpdateRequest = {
  name: 'Updated Bot Name',
  temperature: 0.8,
  isDefault: true,
}

const updatedBot = await dbService.updateBot(1, updateData)
```

### Retrieving Bots with Usage

```typescript
// Get all bots with usage statistics
const botsWithUsage = await dbService.getAllBotsWithUsage(1)

// Get a specific bot with usage
const botWithUsage = await dbService.getBotWithUsage(1)
console.log(
  `Bot ${botWithUsage.name} has ${botWithUsage.totalUsage?.totalMessages} total messages`,
)
```

### Deleting a Bot

```typescript
const success = await dbService.deleteBot(1)
if (success) {
  console.log('Bot deleted successfully')
}
```

### Getting Bot Usage Statistics

```typescript
const usage = await dbService.getBotUsage(1)
console.log(`Total messages: ${usage.totalMessages}`)
console.log(`Total input tokens: ${usage.totalInputTokens}`)
console.log(`Total output tokens: ${usage.totalOutputTokens}`)
```

## Error Handling

All methods include proper error handling and will return null or throw descriptive errors when operations fail.
