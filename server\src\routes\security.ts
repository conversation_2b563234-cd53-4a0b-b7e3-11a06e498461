import express, { type Request, type Response } from 'express'
import authService from '../middleware/auth'
import {
  asyncErrorHandler,
  ErrorType,
  SecurityError,
} from '../middleware/errorHandling'
import { validateBody } from '../middleware/inputValidation'
import { authLimiter, logSecurityEvent } from '../middleware/security'
import { sessionManager } from '../utils/sessionManager'

const router = express.Router()

// Security monitoring endpoint - requires admin authentication
router.get(
  '/stats',
  authLimiter,
  authService.authenticateAdmin,
  asyncErrorHandler(async (req: Request, res: Response) => {
    // Log security stats access
    logSecurityEvent({
      event: 'SECURITY_STATS_ACCESSED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        adminUser: req.user?.username,
        accessTime: new Date().toISOString(),
      },
    })

    // Get session statistics
    const sessionStats = sessionManager.getStats()
    const timeoutConfig = sessionManager.getTimeoutConfig()

    // Get system information
    const systemStats = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
    }

    // Security configuration status
    const securityConfig = {
      sessionTimeout: timeoutConfig.timeout,
      cleanupInterval: timeoutConfig.cleanupInterval,
      maxSessionsPerUser: 5, // From session manager config
      rateLimiting: {
        general: { windowMs: 15 * 60 * 1000, max: 1000 },
        chat: { windowMs: 1 * 60 * 1000, max: 60 },
        auth: { windowMs: 1 * 60 * 1000, max: 5 },
        upload: { windowMs: 60 * 60 * 1000, max: 10 },
      },
      securityHeaders: true,
      inputValidation: true,
      errorHandling: true,
    }

    res.json({
      success: true,
      data: {
        sessions: sessionStats,
        system: systemStats,
        security: securityConfig,
      },
    })
  }),
)

// Force session cleanup endpoint - requires admin authentication
router.post(
  '/cleanup-sessions',
  authLimiter,
  authService.authenticateAdmin,
  asyncErrorHandler(async (req: Request, res: Response) => {
    const cleanedUp = sessionManager.forceCleanup()

    logSecurityEvent({
      event: 'MANUAL_SESSION_CLEANUP',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        adminUser: req.user?.username,
        sessionsRemoved: cleanedUp,
        remainingSessions: sessionManager.getStats().totalSessions,
      },
    })

    res.json({
      success: true,
      message: `Cleaned up ${cleanedUp} expired sessions`,
      data: {
        sessionsRemoved: cleanedUp,
        remainingSessions: sessionManager.getStats().totalSessions,
      },
    })
  }),
)

// Update session timeout configuration - requires admin authentication
const timeoutConfigSchema = {
  timeout: {
    required: false,
    type: 'number',
    min: 5 * 60 * 1000, // 5 minutes minimum
    max: 24 * 60 * 60 * 1000, // 24 hours maximum
  },
  cleanupInterval: {
    required: false,
    type: 'number',
    min: 1 * 60 * 1000, // 1 minute minimum
    max: 60 * 60 * 1000, // 1 hour maximum
  },
}

router.put(
  '/session-config',
  authLimiter,
  authService.authenticateAdmin,
  validateBody(timeoutConfigSchema),
  asyncErrorHandler(async (req: Request, res: Response) => {
    const { timeout, cleanupInterval } = req.body

    // Update configuration
    sessionManager.updateTimeoutConfig(timeout, cleanupInterval)

    logSecurityEvent({
      event: 'SESSION_CONFIG_UPDATED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        adminUser: req.user?.username,
        newTimeout: timeout,
        newCleanupInterval: cleanupInterval,
        previousConfig: sessionManager.getTimeoutConfig(),
      },
    })

    res.json({
      success: true,
      message: 'Session configuration updated successfully',
      data: sessionManager.getTimeoutConfig(),
    })
  }),
)

// Security health check endpoint - public but rate limited
router.get(
  '/health',
  authLimiter,
  asyncErrorHandler(async (req: Request, res: Response) => {
    const sessionStats = sessionManager.getStats()

    // Basic health indicators
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        sessions: {
          status: sessionStats.totalSessions < 10000 ? 'healthy' : 'warning',
          totalSessions: sessionStats.totalSessions,
          activeSessions: sessionStats.activeSessions,
        },
        memory: {
          status:
            process.memoryUsage().heapUsed < 500 * 1024 * 1024
              ? 'healthy'
              : 'warning', // 500MB threshold
          heapUsed: process.memoryUsage().heapUsed,
          heapTotal: process.memoryUsage().heapTotal,
        },
        uptime: {
          status: process.uptime() > 0 ? 'healthy' : 'error',
          uptime: process.uptime(),
        },
      },
    }

    // Determine overall status
    const hasWarnings = Object.values(health.checks).some(
      (check) => check.status === 'warning',
    )
    const hasErrors = Object.values(health.checks).some(
      (check) => check.status === 'error',
    )

    if (hasErrors) {
      health.status = 'error'
    } else if (hasWarnings) {
      health.status = 'warning'
    }

    // Log health check access
    logSecurityEvent({
      event: 'SECURITY_HEALTH_CHECK',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      details: {
        healthStatus: health.status,
        totalSessions: sessionStats.totalSessions,
        memoryUsage: process.memoryUsage().heapUsed,
      },
    })

    const statusCode = health.status === 'error' ? 503 : 200
    res.status(statusCode).json(health)
  }),
)

// Get recent security events - requires admin authentication
router.get(
  '/events',
  authLimiter,
  authService.authenticateAdmin,
  asyncErrorHandler(async (req: Request, res: Response) => {
    // This would typically fetch from a logging service or database
    // For now, we'll return a placeholder response

    logSecurityEvent({
      event: 'SECURITY_EVENTS_ACCESSED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        adminUser: req.user?.username,
      },
    })

    res.json({
      success: true,
      message: 'Security events endpoint - integrate with your logging service',
      data: {
        note: 'This endpoint should be connected to your centralized logging system',
        recommendation:
          'Implement integration with services like ELK Stack, Splunk, or cloud logging',
      },
    })
  }),
)

// Test security logging endpoint - requires admin authentication
router.post(
  '/test-logging',
  authLimiter,
  authService.authenticateAdmin,
  asyncErrorHandler(async (req: Request, res: Response) => {
    // Generate test security events
    const testEvents = [
      { event: 'TEST_INFO_EVENT', level: 'info' as const },
      { event: 'TEST_WARN_EVENT', level: 'warn' as const },
      { event: 'TEST_ERROR_EVENT', level: 'error' as const },
    ]

    testEvents.forEach(({ event, level }) => {
      logSecurityEvent({
        event,
        level,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          testEvent: true,
          adminUser: req.user?.username,
          timestamp: new Date().toISOString(),
        },
      })
    })

    res.json({
      success: true,
      message: 'Test security events generated successfully',
      data: {
        eventsGenerated: testEvents.length,
        events: testEvents,
      },
    })
  }),
)

export default router
