/// <reference types="node" />
import { eq } from 'drizzle-orm'
import { initializeDatabase } from '../src/db/connection'
import { s3Configurations, twilioConfigs } from '../src/db/schema'

/**
 * Dedicated Twilio Migration Seeder
 *
 * This script is specifically for migrating Twilio integration to existing production servers.
 * It safely adds Twilio and S3 configurations without affecting existing data.
 *
 * Usage:
 * dotenv -e .env.production -- bun drizzle/seed-twilio-migration.ts
 */
async function seedTwilioMigration() {
  console.log('🚀 Starting Twilio Migration Seeding...')
  console.log(
    '📋 This will add Twilio and S3 configurations to existing production database',
  )

  // Use production environment
  const db = initializeDatabase()

  try {
    // Check if Twilio configuration already exists
    console.log('📞 Checking existing Twilio configuration...')
    const existingTwilioConfig = await db
      .select()
      .from(twilioConfigs)
      .where(eq(twilioConfigs.siteId, 1))
      .limit(1)

    if (existingTwilioConfig[0]) {
      console.log(
        '🔄 Twilio configuration already exists. Updating with new values...',
      )

      const {
        TWILIO_ACCOUNT_SID,
        TWILIO_AUTH_TOKEN,
        TWILIO_PHONE_NUMBER,
        TWILIO_WEBHOOK_URL,
      } = process.env

      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
        console.error(
          '❌ Missing required Twilio environment variables in .env.production:',
        )
        console.error('   - TWILIO_ACCOUNT_SID')
        console.error('   - TWILIO_AUTH_TOKEN')
        console.error('   - TWILIO_PHONE_NUMBER')
        console.error('   Please add these to .env.production and run again.')
        process.exit(1)
      }

      // Update existing Twilio configuration
      await db
        .update(twilioConfigs)
        .set({
          accountSid: TWILIO_ACCOUNT_SID,
          authToken: TWILIO_AUTH_TOKEN,
          phoneNumber: TWILIO_PHONE_NUMBER,
          webhookUrl: TWILIO_WEBHOOK_URL || null,
          isActive: true,
          updatedAt: new Date(),
        })
        .where(eq(twilioConfigs.id, existingTwilioConfig[0].id))

      console.log('✅ Twilio configuration updated successfully!')
    } else {
      // Add Twilio configuration from environment variables
      const {
        TWILIO_ACCOUNT_SID,
        TWILIO_AUTH_TOKEN,
        TWILIO_PHONE_NUMBER,
        TWILIO_WEBHOOK_URL,
      } = process.env

      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
        console.error(
          '❌ Missing required Twilio environment variables in .env.production:',
        )
        console.error('   - TWILIO_ACCOUNT_SID')
        console.error('   - TWILIO_AUTH_TOKEN')
        console.error('   - TWILIO_PHONE_NUMBER')
        console.error('   Please add these to .env.production and run again.')
        process.exit(1)
      }

      console.log('📞 Creating Twilio configuration...')
      await db.insert(twilioConfigs).values({
        siteId: 1,
        accountSid: TWILIO_ACCOUNT_SID,
        authToken: TWILIO_AUTH_TOKEN,
        phoneNumber: TWILIO_PHONE_NUMBER,
        webhookUrl: TWILIO_WEBHOOK_URL || null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      console.log('✅ Twilio configuration created successfully!')
    }

    // Check if S3 configuration already exists
    console.log('☁️  Checking existing S3 configuration...')
    const existingS3Configs = await db.select().from(s3Configurations).limit(1)

    if (existingS3Configs[0]) {
      console.log(
        '🔄 S3 configuration already exists. Checking if update is needed...',
      )

      const {
        S3_ACCESS_KEY_ID,
        S3_SECRET_ACCESS_KEY,
        S3_BUCKET_NAME,
        S3_REGION,
        S3_ENDPOINT_URL,
        DEFAULT_S3_SERVICE_NAME,
      } = process.env

      if (!S3_ACCESS_KEY_ID || !S3_SECRET_ACCESS_KEY || !S3_BUCKET_NAME) {
        console.warn('⚠️  Missing S3 environment variables in .env.production:')
        console.warn('   - S3_ACCESS_KEY_ID')
        console.warn('   - S3_SECRET_ACCESS_KEY')
        console.warn('   - S3_BUCKET_NAME')
        console.warn('   S3 configuration will not be updated.')
      } else {
        // Update existing S3 configuration
        await db
          .update(s3Configurations)
          .set({
            serviceName: DEFAULT_S3_SERVICE_NAME || 'AWS S3',
            accessKeyId: S3_ACCESS_KEY_ID,
            secretAccessKey: S3_SECRET_ACCESS_KEY,
            bucketName: S3_BUCKET_NAME,
            region: S3_REGION || 'us-east-1',
            endpointUrl: S3_ENDPOINT_URL || null,
            updatedAt: new Date(),
          })
          .where(eq(s3Configurations.id, existingS3Configs[0].id))

        console.log('✅ S3 configuration updated successfully!')
      }
    } else {
      // Add S3 configuration from environment variables
      const {
        S3_ACCESS_KEY_ID,
        S3_SECRET_ACCESS_KEY,
        S3_BUCKET_NAME,
        S3_REGION,
        S3_ENDPOINT_URL,
        DEFAULT_S3_SERVICE_NAME,
      } = process.env

      if (!S3_ACCESS_KEY_ID || !S3_SECRET_ACCESS_KEY || !S3_BUCKET_NAME) {
        console.warn('⚠️  Missing S3 environment variables in .env.production:')
        console.warn('   - S3_ACCESS_KEY_ID')
        console.warn('   - S3_SECRET_ACCESS_KEY')
        console.warn('   - S3_BUCKET_NAME')
        console.warn('   S3 media processing will not be available.')
        console.warn(
          '   Add these to .env.production if you want media processing.',
        )
      } else {
        console.log('☁️  Creating S3 configuration...')
        await db.insert(s3Configurations).values({
          serviceName: DEFAULT_S3_SERVICE_NAME || 'AWS S3',
          accessKeyId: S3_ACCESS_KEY_ID,
          secretAccessKey: S3_SECRET_ACCESS_KEY,
          bucketName: S3_BUCKET_NAME,
          region: S3_REGION || 'us-east-1',
          endpointUrl: S3_ENDPOINT_URL || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        console.log('✅ S3 configuration created successfully!')
      }
    }

    console.log('')
    console.log('🎉 Twilio Migration Seeding completed successfully!')
    console.log('')
    console.log('📋 Summary:')
    console.log(
      '  📞 Twilio configuration: Created/Updated - Ready for WhatsApp integration',
    )
    console.log(
      '  ☁️  S3 configuration: Created/Updated - Ready for media processing',
    )
    console.log('')
    console.log('🔗 Next steps:')
    console.log(
      '  1. Update Twilio Console webhook URL to point to your Hono.js server',
    )
    console.log('  2. Test the integration by sending a WhatsApp message')
    console.log('  3. Monitor logs to ensure everything works correctly')
    console.log('  4. Use admin interface for future configuration updates')
  } catch (error) {
    console.error('❌ Error during Twilio migration seeding:', error)
    process.exit(1)
  } finally {
    // Always close database connection
    await db.$client.end()
  }
}

// Run the seed function when executed directly
seedTwilioMigration()
  .then(() => {
    console.log('🎉 Twilio migration seed completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Twilio migration seed failed:', error)
    process.exit(1)
  })

export { seedTwilioMigration }
