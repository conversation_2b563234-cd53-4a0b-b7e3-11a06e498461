export interface Company {
  id: number
  name: string
  registrationNumber?: string
  businessType?: string
  category?: string
  subcategory?: string
  address?: string
  state?: string
  postcode?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  website?: string
  contactPerson?: string
  certificateNumber?: string
  certificateType?: string
  certificateStatus?: string
  issuedDate?: string
  expiryDate?: string
  sourceUrl?: string
  createdAt: string
  updatedAt: string
}

export interface CompanySearchResponse {
  companies: Company[]
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
    totalPages: number
  }
  query: string
}
