
build:
	docker build -t website-crawler .

run: build
	# ./run_crawler.sh https://myehalal.halal.gov.my 9 9999
	# ./run_crawler.sh www.hdcglobal.com 9 9999
	# ./run_crawler.sh https://myehalal.halal.gov.my/portal-halal/v1/index.php 9 9999 
	./run_crawler.sh https://halalselangor.com 9 9999 --force-recrawl
	# ./run_crawler.sh https://myehalal.halal.gov.my/portal-halal/v1/index.php 9 9999 --force-recrawl

index-push: build
	@echo "🚀 Pushing crawled files to Qdrant..."
	@if [ -z "$(COLLECTION)" ]; then \
		echo "❌ Error: COLLECTION parameter is required"; \
		echo "Usage: make index-push COLLECTION=my_collection [QDRANT_URL=http://localhost:6333]"; \
		exit 1; \
	fi
	@QDRANT_URL=$${QDRANT_URL:-http://host.docker.internal:6333}; \
	echo "📊 Collection: $(COLLECTION)"; \
	echo "🔗 Qdrant URL: $$QDRANT_URL"; \
	echo "📁 Output directory: output"; \
	docker run --rm \
		-v $(PWD)/output:/app/output:ro \
		--network host \
		website-crawler \
		python push_to_qdrant.py "$(COLLECTION)" "$$QDRANT_URL" "output"

# Helper target to show usage
index-push-help:
	@echo "📚 Qdrant Index Push Usage:"
	@echo ""
	@echo "  make index-push COLLECTION=<collection_name> [QDRANT_URL=<url>]"
	@echo ""
	@echo "Examples:"
	@echo "  make index-push COLLECTION=halal_docs"
	@echo "  make index-push COLLECTION=halal_docs QDRANT_URL=http://localhost:6333"
	@echo "  make index-push COLLECTION=my_collection QDRANT_URL=http://*************:6333"
	@echo ""
	@echo "Default Qdrant URL: http://host.docker.internal:6333"
	@echo ""
	@echo "Prerequisites:"
	@echo "  - Qdrant server running on host machine"
	@echo "  - Crawled files in ./output directory"