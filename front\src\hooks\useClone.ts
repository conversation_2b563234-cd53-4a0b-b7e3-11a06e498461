import { useRouter } from 'next/navigation'
import { useState } from 'react'

export interface CloneConfig<T> {
  entityName: string
  fetchOriginal: (id: number) => Promise<T>
  createNew: (data: any) => Promise<any>
  prepareCloneData: (original: T) => any
  redirectPath: string
}

export function useClone<T>(config: CloneConfig<T>) {
  const router = useRouter()
  const [originalData, setOriginalData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadOriginal = async (id: number) => {
    try {
      setError(null)
      const data = await config.fetchOriginal(id)
      setOriginalData(data)
      return config.prepareCloneData(data)
    } catch (err) {
      const errorMessage = `Failed to load ${config.entityName} data for cloning.`
      setError(errorMessage)
      console.error(errorMessage, err)
      throw err
    }
  }

  const submitClone = async (cloneData: any) => {
    setIsLoading(true)
    setError(null)

    try {
      await config.createNew(cloneData)
      router.push(config.redirectPath)
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error ||
        err.message ||
        `Failed to create ${config.entityName}.`
      setError(errorMessage)
      console.error(errorMessage, err)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    originalData,
    isLoading,
    error,
    loadOriginal,
    submitClone,
    setError,
  }
}
