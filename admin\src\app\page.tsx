'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAuthStore } from '@/stores/auth'

export default function HomePage() {
  const router = useRouter()
  const { isAuthenticated, user } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
    } else if (user && !user.roles.includes('SUPERADMIN' as any)) {
      // Only SUPERADMIN can access this admin dashboard
      router.push('/unauthorized')
    } else {
      router.push('/dashboard')
    }
  }, [isAuthenticated, user, router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Halal Admin Dashboard</h1>
        <p className="text-muted-foreground">Redirecting...</p>
      </div>
    </div>
  )
}
