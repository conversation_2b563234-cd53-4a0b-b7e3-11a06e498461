export interface Product {
  id: number
  productName: string
  companyName: string
  certificateNumber: string | null
  certificateType: string | null
  issuedDate: string | null
  expiryDate: string | null
  status: string | null
  category: string | null
  subcategory: string | null
  address: string | null
  state: string | null
  country: string | null
  contactInfo: string | null
  website: string | null
  sourceUrl: string | null
  rawData?: string | null
  r2rDocumentId?: string | null
  vectorizedAt?: Date | null
  vectorizationModel?: string | null
  searchableText?: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export interface ProductSearchResponse {
  query: string
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
    totalPages: number
  }
  translatedTerms?: string[]
  detectedLanguage?: string
  confidence?: number
  searchType?: 'keyword' | 'multilingual'
}

export interface ProductWithSimilarity extends Product {
  similarityScore?: number
  foundBySearchTerm?: string // Track which search term found this product
}

export interface SemanticProductSearchResponse {
  query: string
  searchType: 'semantic' | 'keyword' | 'multilingual-semantic'
  products: ProductWithSimilarity[]
  pagination: {
    page: number
    limit: number
    total: number
    hasMore: boolean
    totalPages: number
  }
  translatedQueries?: string[]
  translatedTerms?: string[]
  detectedLanguage?: string
  confidence?: number
}
