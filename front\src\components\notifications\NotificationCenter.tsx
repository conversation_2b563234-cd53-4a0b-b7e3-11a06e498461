'use client'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bell,
  CheckCircle,
  Info,
  <PERSON>tings,
  X,
  XCircle,
} from 'lucide-react'
import { useState } from 'react'
import { type Notification, useNotifications } from '@/lib/notifications'

export function NotificationCenter() {
  const {
    notifications,
    unreadCount,
    soundEnabled,
    browserNotificationsEnabled,
    mark<PERSON>Read,
    markAllAsRead,
    dismissNotification,
    clearAll,
    setSoundEnabled,
    setBrowserNotificationsEnabled,
    requestBrowserPermission,
  } = useNotifications()

  const [isOpen, setIsOpen] = useState(false)
  const [showSettings, setShowSettings] = useState(false)

  const visibleNotifications = notifications.filter(n => !n.dismissed)
  const unreadNotifications = visibleNotifications.filter(n => !n.read)

  const handleToggle = () => {
    setIsOpen(!isOpen)
    if (!isOpen && unreadNotifications.length > 0) {
      // Mark all as read when opening
      markAllAsRead()
    }
  }

  const handleBrowserPermissionToggle = async () => {
    if (!browserNotificationsEnabled) {
      const granted = await requestBrowserPermission()
      if (!granted) {
        alert(
          'Browser notifications permission was denied. You can enable it in your browser settings.'
        )
      }
    } else {
      setBrowserNotificationsEnabled(false)
    }
  }

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getNotificationBgColor = (
    type: Notification['type'],
    urgent?: boolean
  ) => {
    const baseClasses = urgent ? 'border-l-4' : ''
    switch (type) {
      case 'success':
        return `bg-green-50 ${urgent ? 'border-l-green-500' : ''} ${baseClasses}`
      case 'error':
        return `bg-red-50 ${urgent ? 'border-l-red-500' : ''} ${baseClasses}`
      case 'warning':
        return `bg-yellow-50 ${urgent ? 'border-l-yellow-500' : ''} ${baseClasses}`
      case 'info':
      default:
        return `bg-blue-50 ${urgent ? 'border-l-blue-500' : ''} ${baseClasses}`
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={handleToggle}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        title="Notifications"
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Notifications
            </h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Settings"
              >
                <Settings className="h-4 w-4" />
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Close"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <h4 className="text-sm font-medium text-gray-900 mb-3">
                Notification Settings
              </h4>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={soundEnabled}
                    onChange={e => setSoundEnabled(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Sound alerts
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={browserNotificationsEnabled}
                    onChange={handleBrowserPermissionToggle}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Browser notifications
                  </span>
                </label>
              </div>
            </div>
          )}

          {/* Actions */}
          {visibleNotifications.length > 0 && (
            <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
              <span className="text-sm text-gray-600">
                {visibleNotifications.length} notification
                {visibleNotifications.length !== 1 ? 's' : ''}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Mark all read
                </button>
                <button
                  onClick={clearAll}
                  className="text-xs text-red-600 hover:text-red-800"
                >
                  Clear all
                </button>
              </div>
            </div>
          )}

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {visibleNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No notifications
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  You're all caught up!
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {visibleNotifications.map(notification => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 ${getNotificationBgColor(notification.type, notification.urgent)} ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {notification.title}
                              {notification.urgent && (
                                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  Urgent
                                </span>
                              )}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              {formatTimestamp(notification.timestamp)}
                            </p>
                          </div>
                          <button
                            onClick={() => dismissNotification(notification.id)}
                            className="ml-2 flex-shrink-0 text-gray-400 hover:text-gray-600"
                            title="Dismiss"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        {notification.actionUrl && (
                          <div className="mt-2">
                            <a
                              href={notification.actionUrl}
                              className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                              onClick={() => setIsOpen(false)}
                            >
                              View Details →
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
