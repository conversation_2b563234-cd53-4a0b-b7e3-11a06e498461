// import 'dotenv/config';

import chalk from "chalk";
import { Command } from "commander";
import {
  createCrawlerConfig,
  getSupportedPlatforms,
} from "../config/crawler.config.js";
import { createDatabaseConfig } from "../config/database.config.js";
import { DatabaseManager } from "./crawlers/base/DatabaseManager.js";
import logger from "./utils/logger.js";

const program = new Command();

program
  .name("sabah-tourism-crawler")
  .description("Social media crawler for tourism content")
  .version("1.0.0");

program
  .command("crawl")
  .description("Start crawling a social media platform")
  .requiredOption(
    "-p, --platform <platform>",
    `Platform to crawl (${getSupportedPlatforms().join(", ")})`,
  )
  .requiredOption(
    "-k, --keywords <keywords>",
    "Comma-separated list of keywords to search for",
  )
  .option("-m, --max-posts <number>", "Maximum number of posts to crawl", "100")
  .option("--no-media", "Skip media downloads")
  .option(
    "--media-types <types>",
    "Comma-separated list of media types to download (image,video,audio)",
    "image,video,audio",
  )
  .option(
    "-o, --output <dir>",
    "Output directory for downloaded media",
    "./output",
  )
  .option("--headless", "Run browser in headless mode", false)
  .option("--resume <sessionId>", "Resume a previous crawl session")
  .option("--dry-run", "Perform a dry run without downloading media", false)
  .option("--verbose", "Enable verbose logging", false)
  .action(async (options) => {
    try {
      console.log(chalk.blue("🚀 Starting Sabah Tourism Crawler"));

      // Validate platform
      const supportedPlatforms = getSupportedPlatforms();
      if (!supportedPlatforms.includes(options.platform)) {
        console.error(
          chalk.red(`❌ Unsupported platform: ${options.platform}`),
        );
        console.log(
          chalk.yellow(`Supported platforms: ${supportedPlatforms.join(", ")}`),
        );
        process.exit(1);
      }

      // Parse keywords
      const keywords = options.keywords
        .split(",")
        .map((k: string) => k.trim())
        .filter(Boolean);
      if (keywords.length === 0) {
        console.error(chalk.red("❌ No valid keywords provided"));
        process.exit(1);
      }

      // Parse media types
      const mediaTypes = options.mediaTypes
        .split(",")
        .map((t: string) => t.trim())
        .filter(Boolean);
      const validMediaTypes = ["image", "video", "audio"];
      const invalidTypes = mediaTypes.filter(
        (t: string) => !validMediaTypes.includes(t),
      );
      if (invalidTypes.length > 0) {
        console.error(
          chalk.red(`❌ Invalid media types: ${invalidTypes.join(", ")}`),
        );
        console.log(chalk.yellow(`Valid types: ${validMediaTypes.join(", ")}`));
        process.exit(1);
      }

      // Create crawler configuration
      const crawlerConfig = createCrawlerConfig(options.platform, keywords, {
        maxPosts: Number.parseInt(options.maxPosts),
        downloadMedia: options.media && !options.dryRun,
        mediaTypes: mediaTypes as any[],
        outputDir: options.output,
        browserConfig: {
          headless: options.headless,
          timeout: 30000,
        },
      });

      // Initialize database
      const dbConfig = createDatabaseConfig();
      const database = new DatabaseManager(dbConfig);

      console.log(chalk.green("✅ Configuration loaded"));
      console.log(chalk.cyan(`Platform: ${options.platform}`));
      console.log(chalk.cyan(`Keywords: ${keywords.join(", ")}`));
      console.log(chalk.cyan(`Max posts: ${options.maxPosts}`));
      console.log(
        chalk.cyan(
          `Download media: ${crawlerConfig.downloadMedia ? "Yes" : "No"}`,
        ),
      );
      console.log(chalk.cyan(`Output directory: ${options.output}`));

      // Import and create platform-specific crawler
      let crawler;
      switch (options.platform) {
        case "douyin": {
          const { DouyinCrawler } = await import(
            "./crawlers/douyin/DouyinCrawler.js"
          );
          crawler = new DouyinCrawler(crawlerConfig, database);
          break;
        }
        default:
          console.error(
            chalk.red(
              `❌ Crawler not implemented for platform: ${options.platform}`,
            ),
          );
          process.exit(1);
      }

      // Set up event listeners
      crawler.on("progress", (progress) => {
        const { processedPosts, totalPosts, successfulPosts, failedPosts } =
          progress.progress;
        console.log(
          chalk.blue(
            `📊 Progress: ${processedPosts}/${totalPosts} posts processed (✅ ${successfulPosts} success, ❌ ${failedPosts} failed)`,
          ),
        );

        if (progress.currentActivity) {
          console.log(chalk.gray(`   ${progress.currentActivity}`));
        }
      });

      crawler.on("media-downloaded", (downloadProgress) => {
        if (downloadProgress.status === "completed") {
          console.log(
            chalk.green(`📥 Downloaded: ${downloadProgress.fileName}`),
          );
        } else if (downloadProgress.status === "failed") {
          console.log(
            chalk.red(
              `❌ Failed to download: ${downloadProgress.fileName} - ${downloadProgress.error}`,
            ),
          );
        }
      });

      crawler.on("error", (error) => {
        console.error(chalk.red(`❌ Crawler error: ${error.message}`));
      });

      crawler.on("completed", async (summary) => {
        console.log(chalk.green("\n🎉 Crawl completed!"));
        console.log(chalk.cyan("📊 Summary:"));
        console.log(chalk.cyan(`   Total posts: ${summary.totalPosts}`));
        console.log(chalk.cyan(`   Successful: ${summary.successfulPosts}`));
        console.log(chalk.cyan(`   Failed: ${summary.failedPosts}`));
        console.log(
          chalk.cyan(
            `   Media downloaded: ${summary.downloadedMedia}/${summary.totalMedia}`,
          ),
        );
        console.log(
          chalk.cyan(`   Duration: ${Math.round(summary.duration / 1000)}s`),
        );
        console.log(chalk.cyan(`   Errors: ${summary.errors}`));

        // Show performance metrics
        try {
          const { performanceMonitor } = await import("./utils/performance.js");
          const perfSummary = performanceMonitor.getSummary();
          if (perfSummary.totalOperations > 0) {
            console.log(chalk.blue("\n⚡ Performance Metrics:"));
            console.log(
              chalk.cyan(`   Total operations: ${perfSummary.totalOperations}`),
            );
            console.log(
              chalk.cyan(
                `   Average operation time: ${Math.round(perfSummary.avgDuration)}ms`,
              ),
            );
            console.log(
              chalk.cyan(
                `   Peak memory usage: ${Math.round(perfSummary.memoryPeak / 1024 / 1024)}MB`,
              ),
            );
          }
        } catch (perfError) {
          console.log(chalk.yellow("⚠️  Performance metrics unavailable"));
        }
      });

      // Handle graceful shutdown
      process.on("SIGINT", async () => {
        console.log(chalk.yellow("\n⏸️  Gracefully shutting down..."));
        await crawler.stop();
        await crawler.cleanup();
        database.close();
        process.exit(0);
      });

      // Start crawling
      const crawlOptions = {
        resumeSession: options.resume
          ? Number.parseInt(options.resume)
          : undefined,
        dryRun: options.dryRun,
        verbose: options.verbose,
      };

      await crawler.crawl(crawlOptions);
      await crawler.cleanup();
      database.close();
    } catch (error) {
      console.error(chalk.red(`❌ Error: ${(error as Error).message}`));
      logger.error("CLI error", { error });
      process.exit(1);
    }
  });

program
  .command("status")
  .description("Show crawler status and statistics")
  .action(async () => {
    try {
      const dbConfig = createDatabaseConfig();
      const database = new DatabaseManager(dbConfig);

      const stats = database.getStats();

      console.log(chalk.blue("📊 Crawler Statistics"));
      console.log(chalk.cyan(`Total posts: ${stats.totalPosts}`));
      console.log(chalk.cyan(`Total media files: ${stats.totalMediaFiles}`));
      console.log(chalk.cyan(`Total sessions: ${stats.totalSessions}`));
      console.log(chalk.cyan(`Active sessions: ${stats.activeSessions}`));

      console.log(chalk.blue("\n📈 Platform Breakdown:"));
      Object.entries(stats.platformBreakdown).forEach(([platform, count]) => {
        console.log(chalk.cyan(`  ${platform}: ${count} posts`));
      });

      console.log(chalk.blue("\n📅 Recent Activity:"));
      console.log(
        chalk.cyan(`  Today: ${stats.recentActivity.postsToday} posts`),
      );
      console.log(
        chalk.cyan(`  This week: ${stats.recentActivity.postsThisWeek} posts`),
      );
      console.log(
        chalk.cyan(
          `  This month: ${stats.recentActivity.postsThisMonth} posts`,
        ),
      );

      database.close();
    } catch (error) {
      console.error(chalk.red(`❌ Error: ${(error as Error).message}`));
      process.exit(1);
    }
  });

program
  .command("setup")
  .description("Set up the database and create necessary directories")
  .action(async () => {
    try {
      console.log(chalk.blue("🔧 Setting up Sabah Tourism Crawler"));

      const dbConfig = createDatabaseConfig();
      const database = new DatabaseManager(dbConfig);

      console.log(chalk.green("✅ Database initialized"));

      // Create output directories
      const fs = await import("fs");
      const outputDir = process.env.MEDIA_OUTPUT_DIR || "./output";
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(chalk.green(`✅ Created output directory: ${outputDir}`));
      }

      const logsDir = "./logs";
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
        console.log(chalk.green(`✅ Created logs directory: ${logsDir}`));
      }

      database.close();
      console.log(chalk.green("🎉 Setup completed successfully!"));
    } catch (error) {
      console.error(chalk.red(`❌ Setup error: ${(error as Error).message}`));
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();

export default program;
