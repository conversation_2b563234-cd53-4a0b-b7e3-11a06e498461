# Form AI System

An intelligent form-filling AI that can analyze web forms and fill them automatically based on chat requests. The system consists of a server-side AI service and an embeddable JavaScript client that can be added to any web page.

## Features

- **Intelligent Form Analysis**: Automatically detects and analyzes form fields, labels, and structure
- **Natural Language Processing**: Understands user requests in plain English
- **Smart Form Filling**: Uses OpenAI GPT-4 to intelligently fill forms based on context
- **Universal Compatibility**: Works with any HTML form using plain JavaScript
- **Embeddable Client**: Single JavaScript file that can be embedded on any website
- **Real-time Chat Interface**: Interactive chat UI for communicating with the AI
- **Visual Feedback**: Highlights fields as they are filled with smooth animations

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Server API    │    │   OpenAI API    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ FormAI JS   │◄┼────┼►│ FormAI      │◄┼────┼►│ GPT-4       │ │
│ │ Client      │ │    │ │ Service     │ │    │ │ Model       │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │                 │    │                 │
│ │ HTML Forms  │ │    │                 │    │                 │
│ └─────────────┘ │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Server Components

### 1. FormAI Service (`server/src/services/formAI.ts`)

Core service that handles:

- HTML form parsing and analysis
- Field type detection (input, select, textarea, checkbox, radio)
- Form purpose inference (contact, registration, survey, etc.)
- AI-powered form filling using OpenAI GPT-4
- Action generation for step-by-step form completion

**Key Methods:**

- `analyzeForm(formHTML)`: Parses HTML and extracts form structure
- `fillForm(request)`: Uses AI to determine field values based on user request
- `generateFormActions(analysis, filledFields)`: Creates step-by-step filling instructions

### 2. FormAI Routes (`server/src/routes/formAI.ts`)

RESTful API endpoints:

- `POST /api/form-ai/analyze`: Analyze form structure
- `POST /api/form-ai/fill`: Fill form using AI
- `GET /api/form-ai/client.js`: Serve the embeddable JavaScript client
- `GET /api/form-ai/health`: Health check endpoint

### 3. Integration with Main Server

The FormAI routes are integrated into the main Hono server at `/api/form-ai/*` with proper CORS configuration for cross-origin requests.

## Client Components

### Embeddable JavaScript Client

The client is served dynamically from `/api/form-ai/client.js` and provides:

**Features:**

- **Floating Action Button**: Blue robot icon in bottom-right corner
- **Chat Interface**: Collapsible chat UI for AI interaction
- **Form Detection**: Automatically detects when user focuses on form fields
- **Real-time Filling**: Fills forms with visual feedback and animations
- **Event Handling**: Triggers proper form events (input, change) for framework compatibility

**Usage:**

```html
<script src="http://your-server.com/api/form-ai/client.js"></script>
```

The client automatically initializes and adds the Form AI interface to any webpage.

## API Reference

### Analyze Form

**Endpoint:** `POST /api/form-ai/analyze`

**Request:**

```json
{
  "formHTML": "<form>...</form>"
}
```

**Response:**

```json
{
  "success": true,
  "analysis": {
    "fields": [
      {
        "id": "name",
        "name": "name",
        "type": "text",
        "label": "Full Name",
        "required": true,
        "placeholder": "Enter your name"
      }
    ],
    "purpose": "contact",
    "context": "Contact form for customer inquiries"
  }
}
```

### Fill Form

**Endpoint:** `POST /api/form-ai/fill`

**Request:**

```json
{
  "formHTML": "<form>...</form>",
  "userRequest": "Fill this with John Doe's contact information",
  "context": "Contact page - Company Website"
}
```

**Response:**

```json
{
  "success": true,
  "result": {
    "success": true,
    "filledFields": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+****************"
    },
    "reasoning": "Filled contact form with John Doe's information",
    "confidence": 0.95,
    "actions": [
      {
        "type": "fill",
        "selector": "#name",
        "value": "John Doe",
        "description": "Fill Full Name with \"John Doe\""
      }
    ]
  }
}
```

## Setup Instructions

### 1. Server Setup

1. Ensure OpenAI API key is configured:

   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

2. The FormAI service is automatically included when you start the server:

   ```bash
   cd server
   npm run dev
   ```

3. The API will be available at `http://localhost:3000/api/form-ai/`

### 2. Client Integration

Add the embeddable script to any webpage:

```html
<!DOCTYPE html>
<html>
  <head>
    <title>Your Website</title>
  </head>
  <body>
    <!-- Your existing forms -->
    <form>
      <input type="text" name="name" placeholder="Name" />
      <input type="email" name="email" placeholder="Email" />
      <button type="submit">Submit</button>
    </form>

    <!-- Add Form AI -->
    <script src="http://localhost:3000/api/form-ai/client.js"></script>
  </body>
</html>
```

## Usage Examples

### Basic Form Filling

1. User clicks on any form field
2. Form AI detects the form and highlights the robot button
3. User clicks the robot button to open chat
4. User types: "Fill this form with my contact information"
5. AI analyzes the form and fills appropriate fields

### Advanced Requests

- "Set name to Jane Smith and <NAME_EMAIL>"
- "Fill the registration form for a new software developer"
- "Complete this survey with positive feedback"
- "Fill billing information for John Doe at 123 Main St"

### Supported Form Elements

- Text inputs (`<input type="text">`)
- Email inputs (`<input type="email">`)
- Password inputs (`<input type="password">`)
- Phone inputs (`<input type="tel">`)
- Number inputs (`<input type="number">`)
- Textareas (`<textarea>`)
- Select dropdowns (`<select>`)
- Checkboxes (`<input type="checkbox">`)
- Radio buttons (`<input type="radio">`)

## Testing

Use the provided test page to try out the Form AI system:

```bash
# Open the test page in your browser
open test-form-ai.html
```

The test page includes:

- Contact form
- Registration form
- Survey form
- Instructions for testing different scenarios

## Security Considerations

- The AI service only fills forms with appropriate, non-sensitive data
- Personal information is generated contextually, not stored
- API endpoints use CORS restrictions
- Form data is processed temporarily and not stored permanently
- OpenAI API calls are made server-side to protect API keys

## Troubleshooting

### Common Issues

1. **Robot button not appearing**
   - Check console for JavaScript errors
   - Verify the script is loading from the correct URL
   - Ensure server is running on correct port

2. **AI not responding**
   - Verify OpenAI API key is configured
   - Check server logs for API errors
   - Ensure internet connectivity for OpenAI API calls

3. **Forms not being filled**
   - Click on a form field first to detect the form
   - Check that form elements have proper IDs or names
   - Verify the form HTML is valid

### Debug Mode

Enable console logging by setting `CONFIG.enableLogs = true` in the client code.

## Customization

### Styling the UI

The Form AI interface uses inline styles for portability. You can customize:

- Button position and appearance
- Chat interface colors and sizing
- Animation effects

### AI Behavior

Modify the AI prompts in `formAI.ts` to change how the AI interprets requests and fills forms.

### Supported Form Types

Add new form type detection in the `inferFormPurpose()` method to handle specialized forms.

## Performance

- Client JavaScript: ~15KB minified
- Server response time: ~1-3 seconds (depends on OpenAI API)
- Memory usage: Minimal (no persistent storage)
- Browser compatibility: Modern browsers (ES6+)

## Future Enhancements

- Support for file uploads
- Multi-step form workflows
- Form validation integration
- Undo/redo functionality
- Form template library
- Machine learning for improved field detection
