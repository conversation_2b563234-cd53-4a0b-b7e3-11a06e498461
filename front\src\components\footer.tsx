'use client'

import Image from 'next/image'
import { CONTACT_INFO, GOVERNMENT_LINKS, SOCIAL_LINKS } from '@/data/constants'
import { Link } from '@/i18n/navigation'
import { useLanguage } from '@/lib/language-context'
import { useChatContext } from './chat/ChatProvider'
import FloatingChatWidget from './chat/FloatingChatWidget'

export function Footer() {
  const { language, t } = useLanguage()

  // Safe way to use chat context with fallback
  let isFloatingChatEnabled = true
  let isStaffLoggedIn = false
  const chatContext = useChatContext() // Call hook unconditionally
  if (chatContext) {
    isFloatingChatEnabled = chatContext.isFloatingChatEnabled
    isStaffLoggedIn = chatContext.isStaffLoggedIn
  } else {
    // ChatProvider not available, use default
    console.log('ChatProvider not available, using default chat state')
  }

  return (
    <>
      <footer className="bg-gray-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* Contact Information */}
            <div className="sm:col-span-2 lg:col-span-1">
              <h3 className="text-lg font-semibold mb-4">
                {t('contact-title', 'Address / Location', 'Alamat / Lokasi')}
              </h3>
              <div className="text-sm text-gray-300 space-y-2">
                <p className="whitespace-pre-line leading-relaxed">
                  {language === 'bm'
                    ? CONTACT_INFO.address.bm
                    : CONTACT_INFO.address.en}
                </p>
                <div className="space-y-1 pt-2">
                  <p className="flex items-center gap-2">
                    <span className="font-medium">
                      {t('phone', 'Phone', 'Telefon')}:
                    </span>
                    <a
                      href={`tel:${CONTACT_INFO.phone}`}
                      className="hover:text-white transition-colors"
                    >
                      {CONTACT_INFO.phone}
                    </a>
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="font-medium">
                      {t('fax', 'Fax', 'Faks')}:
                    </span>
                    <span>{CONTACT_INFO.fax}</span>
                  </p>
                  <p className="flex items-center gap-2">
                    <span className="font-medium">
                      {t('email', 'Email', 'Emel')}:
                    </span>
                    <a
                      href={`mailto:${CONTACT_INFO.email}`}
                      className="hover:text-white transition-colors"
                    >
                      {CONTACT_INFO.email}
                    </a>
                  </p>
                </div>
              </div>
            </div>

            {/* Government Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">
                {t('gov-links', 'Government Links', 'Pautan Kerajaan')}
              </h3>
              <div className="grid grid-cols-1 gap-3">
                {GOVERNMENT_LINKS.map(link => (
                  <Link
                    key={link.url}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors p-2 rounded-md hover:bg-gray-800"
                  >
                    <Image
                      src={link.logo}
                      alt={language === 'bm' ? link.nameBM : link.name}
                      width={24}
                      height={24}
                      className="w-6 h-6 flex-shrink-0"
                    />
                    <span className="text-sm overflow-hidden text-ellipsis">
                      {language === 'bm' ? link.nameBM : link.name}
                    </span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Social Media & Chat */}
            <div className="sm:col-span-2 lg:col-span-1">
              <h3 className="text-lg font-semibold mb-4">
                {t(
                  'social-title',
                  'Follow Us on Social Media',
                  'Ikuti Kami di Media Sosial'
                )}
              </h3>
              <div className="flex gap-3 sm:gap-4 mb-4">
                {SOCIAL_LINKS.map(social => (
                  <Link
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-3 bg-primary-green rounded-full hover:bg-primary-green-dark transition-all duration-200 hover:scale-105"
                    aria-label={`Follow us on ${social.name}`}
                  >
                    <Image
                      src={social.icon}
                      alt={`Follow us on ${social.name}`}
                      width={20}
                      height={20}
                      className="w-5 h-5 filter invert"
                    />
                  </Link>
                ))}
              </div>

              {/* Chat Link */}
              <div className="border-t border-gray-700 pt-4">
                <h4 className="text-sm font-medium mb-2 text-gray-300">
                  {t('chat-title', 'Need Help?', 'Perlukan Bantuan?')}
                </h4>
                <Link
                  href="/chat"
                  className="inline-flex items-center gap-2 text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                    <path
                      d="M8 10h.01M12 10h.01M16 10h.01"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                  {t(
                    'chat-link',
                    'Chat with AI Assistant',
                    'Berbual dengan Pembantu AI'
                  )}
                </Link>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>
              © {new Date().getFullYear()}{' '}
              {t(
                'copyright',
                'Halal Malaysia Portal - JAKIM. All rights reserved.',
                'Portal Halal Malaysia - JAKIM. Hak cipta terpelihara.'
              )}
            </p>
          </div>
        </div>
      </footer>

      {/* Floating Chat Widget */}
      {(isFloatingChatEnabled || isStaffLoggedIn) && <FloatingChatWidget />}
    </>
  )
}
