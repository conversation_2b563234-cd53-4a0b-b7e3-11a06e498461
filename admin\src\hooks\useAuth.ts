import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useAuthStore } from '@/stores/auth'

export function useAuth(requireSuperAdmin = true) {
  const router = useRouter()
  const { isAuthenticated, user, token } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated || !token) {
      router.push('/login')
      return
    }

    if (
      requireSuperAdmin &&
      user &&
      !user.roles.includes('SUPERADMIN' as any)
    ) {
      router.push('/unauthorized')
      return
    }
  }, [isAuthenticated, user, token, requireSuperAdmin, router])

  return {
    isAuthenticated,
    user,
    token,
    isSuperAdmin: user?.roles.includes('SUPERADMIN' as any) || false,
  }
}

export function useRequireAuth() {
  return useAuth(true)
}
