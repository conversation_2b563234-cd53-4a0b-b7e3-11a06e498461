import express from 'express'
import request from 'supertest'
import { securityMiddleware } from '../security'

// Create test app with security middleware
const createTestApp = () => {
  const app = express()
  app.use(express.json())
  app.use(securityMiddleware)

  // Test routes
  app.get('/test', (req, res) => {
    res.json({ message: 'success' })
  })

  app.post('/test', (req, res) => {
    res.json({ message: 'success', body: req.body })
  })

  return app
}

describe('Security Middleware', () => {
  let app: express.Application

  beforeEach(() => {
    app = createTestApp()
  })

  describe('Basic Security Headers', () => {
    it('should add security headers to responses', async () => {
      const response = await request(app).get('/test').expect(200)

      // Check for common security headers
      expect(response.headers).toHaveProperty('x-content-type-options')
      expect(response.headers).toHaveProperty('x-frame-options')
      expect(response.headers).toHaveProperty('x-xss-protection')
    })

    it('should prevent clickjacking with X-Frame-Options', async () => {
      const response = await request(app).get('/test').expect(200)

      expect(response.headers['x-frame-options']).toBeDefined()
    })

    it('should set content type options', async () => {
      const response = await request(app).get('/test').expect(200)

      expect(response.headers['x-content-type-options']).toBe('nosniff')
    })
  })

  describe('Input Validation', () => {
    it('should accept valid JSON input', async () => {
      const validData = {
        message: 'Hello world',
        sessionId: 'test-123',
      }

      const response = await request(app)
        .post('/test')
        .send(validData)
        .expect(200)

      expect(response.body.body).toEqual(validData)
    })

    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/test')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should limit request size', async () => {
      const largeData = {
        message: 'A'.repeat(1000000), // 1MB of data
      }

      const response = await request(app).post('/test').send(largeData)

      // Should either accept or reject based on size limits
      expect(response.status).toBeGreaterThanOrEqual(200)
    })
  })

  describe('Rate Limiting Protection', () => {
    it('should allow reasonable request rates', async () => {
      // Send a few requests in quick succession
      const promises = Array(5)
        .fill(0)
        .map(() => request(app).get('/test'))

      const responses = await Promise.all(promises)

      // Most requests should succeed
      const successCount = responses.filter((r) => r.status === 200).length
      expect(successCount).toBeGreaterThan(0)
    })

    it('should handle concurrent requests', async () => {
      const promises = Array(10)
        .fill(0)
        .map((_, i) =>
          request(app)
            .post('/test')
            .send({ message: `Test ${i}` }),
        )

      const responses = await Promise.all(promises)

      // Should handle concurrent requests gracefully
      responses.forEach((response) => {
        expect(response.status).toBeGreaterThanOrEqual(200)
        expect(response.status).toBeLessThan(500)
      })
    })
  })

  describe('CORS Protection', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/test')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')

      // Should handle OPTIONS request
      expect(response.status).toBeLessThan(500)
    })

    it('should set appropriate CORS headers', async () => {
      const response = await request(app)
        .get('/test')
        .set('Origin', 'http://localhost:3000')

      // Check for CORS headers if configured
      if (response.headers['access-control-allow-origin']) {
        expect(response.headers['access-control-allow-origin']).toBeDefined()
      }
    })
  })

  describe('XSS Protection', () => {
    it('should sanitize potentially dangerous input', async () => {
      const xssAttempt = {
        message: '<script>alert("xss")</script>',
        sessionId: 'test-xss',
      }

      const response = await request(app).post('/test').send(xssAttempt)

      // Should either sanitize or reject dangerous input
      expect(response.status).toBeLessThan(500)

      if (response.status === 200) {
        // If accepted, should be sanitized
        expect(response.body.body.message).not.toContain('<script>')
      }
    })

    it('should handle SQL injection attempts', async () => {
      const sqlInjection = {
        message: "'; DROP TABLE users; --",
        sessionId: 'test-sql',
      }

      const response = await request(app).post('/test').send(sqlInjection)

      // Should handle gracefully
      expect(response.status).toBeLessThan(500)
    })
  })

  describe('Authentication Security', () => {
    it('should handle missing authentication gracefully', async () => {
      const response = await request(app).get('/test')

      // Should work for public endpoints
      expect(response.status).toBeLessThan(500)
    })

    it('should handle invalid tokens gracefully', async () => {
      const response = await request(app)
        .get('/test')
        .set('Authorization', 'Bearer invalid-token')

      // Should handle invalid auth gracefully
      expect(response.status).toBeLessThan(500)
    })
  })

  describe('Error Handling', () => {
    it('should not expose sensitive error information', async () => {
      // Try to trigger an error
      const response = await request(app)
        .post('/test')
        .send(null)
        .set('Content-Type', 'application/json')

      if (response.status >= 400) {
        // Error responses should not expose sensitive info
        expect(response.body).not.toHaveProperty('stack')
        expect(response.body).not.toHaveProperty('config')
      }
    })

    it('should handle unexpected errors gracefully', async () => {
      // This test depends on the actual implementation
      // but should verify that errors don't crash the server
      const response = await request(app).get('/nonexistent-endpoint')

      expect(response.status).toBeGreaterThanOrEqual(400)
      expect(response.status).toBeLessThan(500)
    })
  })

  describe('Content Security', () => {
    it('should validate content types', async () => {
      const response = await request(app)
        .post('/test')
        .send('plain text')
        .set('Content-Type', 'text/plain')

      // Should handle different content types appropriately
      expect(response.status).toBeLessThan(500)
    })

    it('should handle empty requests', async () => {
      const response = await request(app).post('/test')

      expect(response.status).toBeLessThan(500)
    })
  })
})
