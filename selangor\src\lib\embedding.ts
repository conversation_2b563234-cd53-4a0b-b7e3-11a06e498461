/**
 * Embedding service for generating and managing vector embeddings
 */

export interface EmbeddingOptions {
  model?: string
  maxTokens?: number
}

export interface EmbeddingResult {
  embedding: number[]
  model: string
  usage?: {
    prompt_tokens: number
    total_tokens: number
  }
}

export class EmbeddingService {
  private apiKey: string
  private defaultModel = 'text-embedding-3-large'

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.OPENAI_API_KEY || ''
    if (!this.apiKey) {
      throw new Error('OpenAI API key is required for embedding service')
    }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(
    text: string,
    options: EmbeddingOptions = {}
  ): Promise<EmbeddingResult> {
    const model = options.model || this.defaultModel

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: text,
          model,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenAI API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ''
          }`
        )
      }

      const data = await response.json()

      return {
        embedding: data.data[0].embedding,
        model,
        usage: data.usage,
      }
    } catch (error) {
      console.error('Error generating embedding:', error)
      throw error
    }
  }

  /**
   * Generate embeddings for multiple texts in batch
   */
  async generateBatchEmbeddings(
    texts: string[],
    options: EmbeddingOptions = {}
  ): Promise<EmbeddingResult[]> {
    const model = options.model || this.defaultModel

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: texts,
          model,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(
          `OpenAI API error: ${response.status} ${response.statusText}. ${
            errorData.error?.message || ''
          }`
        )
      }

      const data = await response.json()

      return data.data.map((item: any, index: number) => ({
        embedding: item.embedding,
        model,
        usage: index === 0 ? data.usage : undefined, // Only include usage for first item
      }))
    } catch (error) {
      console.error('Error generating batch embeddings:', error)
      throw error
    }
  }

  /**
   * Create searchable text from product data
   */
  static createProductSearchableText(product: {
    productName: string
    companyName: string
    category?: string | null
    subcategory?: string | null
    status?: string | null
    address?: string | null
  }): string {
    const parts = [
      product.productName,
      product.companyName,
      product.category,
      product.subcategory,
      product.status,
      product.address,
    ].filter(Boolean)

    return parts.join(' ')
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  static cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length')
    }

    let dotProduct = 0
    let normA = 0
    let normB = 0

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i]
      normA += a[i] * a[i]
      normB += b[i] * b[i]
    }

    normA = Math.sqrt(normA)
    normB = Math.sqrt(normB)

    if (normA === 0 || normB === 0) {
      return 0
    }

    return dotProduct / (normA * normB)
  }

  /**
   * Validate embedding vector
   */
  static validateEmbedding(
    embedding: number[],
    expectedDimensions = 3072
  ): boolean {
    if (!Array.isArray(embedding)) {
      return false
    }

    if (embedding.length !== expectedDimensions) {
      return false
    }

    return embedding.every(value => typeof value === 'number' && !isNaN(value))
  }
}

// Export a default instance
export const embeddingService = new EmbeddingService()

// Export utility functions
export const {
  createProductSearchableText,
  cosineSimilarity,
  validateEmbedding,
} = EmbeddingService
