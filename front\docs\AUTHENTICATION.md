# Authentication System with Zustand

This project uses Zustand for state management with persistence, similar to how Pinia works in Vue.js. The authentication system provides centralized state management for both admin and agent authentication with automatic token verification and persistence.

## Features

- 🔐 **Centralized Authentication State** - Single source of truth for auth state
- 💾 **Automatic Persistence** - Login sessions persist across browser refreshes
- 🔄 **Token Verification** - Automatic token validation and refresh
- 🛡️ **Route Protection** - Built-in guards for protected routes
- 🚀 **Easy Integration** - Simple hooks for components
- 🔀 **Multi-Role Support** - Separate admin and agent authentication

## Store Structure

### Auth Store (`/src/stores/auth.ts`)

The main authentication store manages both admin and agent authentication:

```typescript
interface AuthState {
  // Admin authentication
  adminToken: string | null
  adminUser: AdminUser | null
  isAdminAuthenticated: boolean

  // Agent authentication
  agentToken: string | null
  agentUser: AgentUser | null
  isAgentAuthenticated: boolean

  // Loading states
  isLoading: boolean

  // Actions
  setAdminAuth: (token: string, user: AdminUser) => void
  setAgentAuth: (token: string, user: AgentUser) => void
  clearAdminAuth: () => void
  clearAgentAuth: () => void
  loginAdmin: (username: string, password: string) => Promise<LoginResult>
  loginAgent: (username: string, password: string) => Promise<LoginResult>
  logoutAdmin: () => Promise<void>
  logoutAgent: () => Promise<void>
  verifyAdminToken: () => Promise<boolean>
  verifyAgentToken: () => Promise<boolean>
}
```

## Usage Examples

### 1. Login Pages

```typescript
// Admin Login Page
import { useAdminAuth } from '@/stores/auth';
import { useLoginRedirect } from '@/hooks/useAuthGuard';

export default function AdminLoginPage() {
  const [form, setForm] = useState({ username: '', password: '' });
  const [error, setError] = useState<string | null>(null);

  const { login, isLoading } = useAdminAuth();

  // Automatically redirect if already authenticated
  useLoginRedirect('admin');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const result = await login(form.username, form.password);

    if (!result.success) {
      setError(result.error || 'Login failed');
    }
    // If successful, useLoginRedirect handles the redirect
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Login form JSX */}
    </form>
  );
}
```

### 2. Protected Dashboard Pages

```typescript
// Admin Dashboard
import { useAdminAuth } from '@/stores/auth';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';

export default function AdminDashboard() {
  const { user, logout } = useAdminAuth();
  const { isAuthenticated, isLoading } = useAdminAuthGuard();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push('/admin');
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1>Welcome, {user?.username}</h1>
      <button onClick={handleLogout}>Logout</button>
      {/* Dashboard content */}
    </div>
  );
}
```

### 3. Component-Level Authentication

```typescript
// Any component that needs auth info
import { useAdminAuth, useAgentAuth } from '@/stores/auth';

export function UserProfile() {
  const adminAuth = useAdminAuth();
  const agentAuth = useAgentAuth();

  if (adminAuth.isAuthenticated) {
    return <div>Admin: {adminAuth.user?.username}</div>;
  }

  if (agentAuth.isAuthenticated) {
    return <div>Agent: {agentAuth.user?.firstName} {agentAuth.user?.lastName}</div>;
  }

  return <div>Not authenticated</div>;
}
```

## Available Hooks

### Authentication Hooks

- `useAdminAuth()` - Admin authentication state and actions
- `useAgentAuth()` - Agent authentication state and actions

### Guard Hooks

- `useAdminAuthGuard()` - Protects admin routes, redirects if not authenticated
- `useAgentAuthGuard()` - Protects agent routes, redirects if not authenticated
- `useLoginRedirect(userType)` - Redirects to dashboard if already authenticated
- `useTokenRefresh()` - Automatic token verification (used in AuthProvider)

## Authentication Flow

### Login Process

1. User enters credentials
2. `login()` function is called
3. API request is made to login endpoint
4. On success:
   - Token and user info are stored in Zustand store
   - Data is automatically persisted to localStorage
   - User is redirected to dashboard
5. On failure:
   - Error message is returned

### Route Protection

1. Protected pages use `useAdminAuthGuard()` or `useAgentAuthGuard()`
2. Guard checks if user is authenticated
3. If not authenticated, attempts to verify existing token
4. If token is invalid, redirects to login page
5. If authenticated, allows access to page

### Token Refresh

1. `AuthProvider` runs `useTokenRefresh()` hook
2. Every 5 minutes, tokens are verified
3. Invalid tokens are automatically cleared
4. User is redirected to login if token expires

## Setup Instructions

### 1. Install Dependencies

```bash
pnpm add zustand
```

### 2. Add AuthProvider to Layout

```typescript
// app/[locale]/layout.tsx
import { AuthProvider } from '@/components/providers/AuthProvider';

export default function Layout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### 3. Use in Components

```typescript
// Login pages
import { useAdminAuth } from '@/stores/auth'
import { useLoginRedirect } from '@/hooks/useAuthGuard'

// Protected pages
import { useAdminAuthGuard } from '@/hooks/useAuthGuard'
```

## Migration from Old System

### Before (localStorage + useEffect)

```typescript
const [user, setUser] = useState(null)
const [isLoading, setIsLoading] = useState(true)

useEffect(() => {
  const token = localStorage.getItem('admin_token')
  if (token) {
    // Manual token verification
    verifyToken(token)
  }
}, [])

const handleLogin = async credentials => {
  // Manual API call
  // Manual localStorage management
  // Manual redirect
}
```

### After (Zustand)

```typescript
const { user, login, isLoading } = useAdminAuth()
useAdminAuthGuard() // Automatic protection

const handleLogin = async credentials => {
  const result = await login(credentials.username, credentials.password)
  // Everything else is handled automatically
}
```

## Benefits

1. **Reduced Boilerplate** - No more manual localStorage management
2. **Automatic Persistence** - Sessions persist across refreshes
3. **Centralized State** - Single source of truth for auth state
4. **Type Safety** - Full TypeScript support
5. **Automatic Verification** - Tokens are verified automatically
6. **Easy Testing** - Zustand stores are easy to test
7. **Performance** - Only re-renders when auth state changes

## Best Practices

1. Always use the provided hooks instead of accessing the store directly
2. Use auth guards on all protected routes
3. Handle loading states appropriately
4. Clear sensitive data on logout
5. Verify tokens periodically for security
6. Use TypeScript for better type safety
