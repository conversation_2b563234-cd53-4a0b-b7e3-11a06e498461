import {
  createCrawlerConfig,
  validatePlatform,
} from "../../config/crawler.config.js";
import type { CrawlerConfig } from "../types/crawler.js";
import type { BaseCrawler } from "./base/BaseCrawler.js";
import type { DatabaseManager } from "./base/DatabaseManager.js";
import { DouyinCrawler } from "./douyin/DouyinCrawler.js";

export interface CrawlerFactory {
  createCrawler(
    platform: string,
    keywords: string[],
    database: DatabaseManager,
    overrides?: Partial<CrawlerConfig>,
  ): BaseCrawler;
  getSupportedPlatforms(): string[];
  validatePlatform(platform: string): boolean;
}

export class DefaultCrawlerFactory implements CrawlerFactory {
  public createCrawler(
    platform: string,
    keywords: string[],
    database: DatabaseManager,
    overrides?: Partial<CrawlerConfig>,
  ): BaseCrawler {
    if (!this.validatePlatform(platform)) {
      throw new Error(
        `Unsupported platform: ${platform}. Supported platforms: ${this.getSupportedPlatforms().join(", ")}`,
      );
    }

    const config = createCrawlerConfig(platform, keywords, overrides);

    switch (platform.toLowerCase()) {
      case "douyin":
        return new DouyinCrawler(config, database);

      case "tiktok":
        // TODO: Implement TikTokCrawler
        throw new Error("TikTok crawler not yet implemented");

      case "instagram":
        // TODO: Implement InstagramCrawler
        throw new Error("Instagram crawler not yet implemented");

      case "facebook":
        // TODO: Implement FacebookCrawler
        throw new Error("Facebook crawler not yet implemented");

      case "youtube":
        // TODO: Implement YouTubeCrawler
        throw new Error("YouTube crawler not yet implemented");

      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  public getSupportedPlatforms(): string[] {
    return ["douyin"]; // Add more as they are implemented
  }

  public validatePlatform(platform: string): boolean {
    return this.getSupportedPlatforms().includes(platform.toLowerCase());
  }
}

// Export the default factory instance
export const crawlerFactory = new DefaultCrawlerFactory();

// Export individual crawler classes
export { BaseCrawler } from "./base/BaseCrawler.js";
export { DatabaseManager } from "./base/DatabaseManager.js";
export { MediaDownloader } from "./base/MediaDownloader.js";
export { DouyinCrawler } from "./douyin/DouyinCrawler.js";

// Export utility functions
export function createCrawlerForPlatform(
  platform: string,
  keywords: string[],
  database: DatabaseManager,
  overrides?: Partial<CrawlerConfig>,
): BaseCrawler {
  return crawlerFactory.createCrawler(platform, keywords, database, overrides);
}

export function isSupportedPlatform(platform: string): boolean {
  return crawlerFactory.validatePlatform(platform);
}

export function getSupportedPlatforms(): string[] {
  return crawlerFactory.getSupportedPlatforms();
}
