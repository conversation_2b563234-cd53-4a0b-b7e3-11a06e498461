'use client';

export const runtime = 'edge';

import { ArrowLeft, Edit, Upload, Plus } from 'lucide-react';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Link, useRouter } from '@/i18n/navigation';
import { useCollectionsStore } from '@/stores/collections';
import { useDocumentsStore } from '@/stores/documents';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { createColumns } from '../../documents/columns';
import { DocumentsDataTable } from '../../documents/data-table';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface CollectionDetailPageProps {
  params: {
    id: string;
  };
}

export default function CollectionDetailPage({ params }: CollectionDetailPageProps) {
  const router = useRouter();
  const collectionId = parseInt(params.id, 10);

  const { currentCollection, isLoading: collectionsLoading, error: collectionsError, fetchCollectionById, clearError: clearCollectionsError } = useCollectionsStore();
  const { documents, isLoading: documentsLoading, error: documentsError, fetchDocuments, clearError: clearDocumentsError } = useDocumentsStore();

  // Auth guard
  useAdminAuthGuard();

  useEffect(() => {
    if (!isNaN(collectionId)) {
      fetchCollectionById(collectionId);
      fetchDocuments(collectionId);
    }
  }, [collectionId, fetchCollectionById, fetchDocuments]);

  // Create columns with refresh callback
  const columns = createColumns(() => {
    fetchDocuments(collectionId); // Refresh the documents list after deletion
  });

  if (isNaN(collectionId)) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Invalid Collection ID</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">The collection ID provided is not valid.</p>
            <Link href="/admin/collections">
              <Button>Back to Collections</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (collectionsError || documentsError) {
    const error = collectionsError || documentsError;
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={() => {
                fetchCollectionById(collectionId);
                fetchDocuments(collectionId);
              }}>
                Try Again
              </Button>
              <Button variant="outline" onClick={() => {
                clearCollectionsError();
                clearDocumentsError();
              }}>
                Clear Error
              </Button>
              <Link href="/admin/collections">
                <Button variant="outline">Back to Collections</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (collectionsLoading || !currentCollection) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/collections">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Collections
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">{currentCollection.name}</h1>
          <p className="text-gray-600">Collection details and documents</p>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/collections/${collectionId}/upload`}>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </Link>
          <Link href={`/admin/collections/${collectionId}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit Collection
            </Button>
          </Link>
        </div>
      </div>

      {/* Collection Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Collection Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  currentCollection.status === 'ACTIVE'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {currentCollection.status}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Documents</p>
              <p className="text-lg font-semibold">{currentCollection._count?.documents || 0}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Created</p>
              <p className="text-lg font-semibold">
                {new Date(currentCollection.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Documents</CardTitle>
              <CardDescription>
                Documents in this collection
              </CardDescription>
            </div>
            <Link href={`/admin/collections/${collectionId}/upload`}>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Document
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {documentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500 text-lg">No documents in this collection</p>
              <p className="text-gray-400 mt-1">Upload your first document to get started</p>
              <Link href={`/admin/collections/${collectionId}/upload`}>
                <Button className="mt-4">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </Link>
            </div>
          ) : (
            <DocumentsDataTable columns={columns} data={documents} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
    const initialLoad = async () => {
      setCheckingAuth(true);
      try {
        const meResponse = await api.admin.getMe();
        const userFromApi = meResponse.user as AdminUser | undefined;
        if (
          userFromApi &&
          (userFromApi.role === UserRole.ADMIN ||
            userFromApi.role === UserRole.EDITOR)
        ) {
          setCurrentUser(userFromApi);
          // Fetch S3 configs for the dropdown (only ADMINs get full details, but ID/name is enough for selection)
          const s3ConfigsResponse = await api.admin.listS3Configurations();
          const s3Data = s3ConfigsResponse.data || s3ConfigsResponse;
          if (Array.isArray(s3Data)) {
            setS3Configs(s3Data);
            if (s3Data.length > 0) {
              setSelectedS3ConfigId(s3Data[0].id.toString()); // Default to first S3 config
            }
          } else {
            setS3Configs([]);
          }
        } else {
          setError(
            'Access Denied: You do not have permission to manage collections.',
          );
        }
      } catch (err: any) {
        setError(
          err.message ||
            'Failed to verify user role or fetch S3 configurations.',
        );
        console.error(err);
      } finally {
        setCheckingAuth(false);
      }
    };
    initialLoad();
  }, []);

  // Fetch collection details
  const fetchCollectionDetails = useCallback(
    async (id: number) => {
      if (!canPerformAction) {
        return;
      }
      setIsFetchingCollection(true);
      try {
        const response = await api.admin.getCollectionById(id);
        const collectionData = response.data || response;
        if (collectionData) {
          setCollection(collectionData);
          setName(collectionData.name);
          setStatus(collectionData.status);
        } else {
          setError('Collection not found.');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch collection details.');
        console.error(err);
      } finally {
        setIsFetchingCollection(false);
      }
    },
    [canPerformAction],
  );

  // Fetch documents for the collection
  const fetchDocuments = useCallback(
    async (id: number, page: number) => {
      if (!canPerformAction) {
        return;
      }
      setIsFetchingDocs(true);
      setDocError(null);
      try {
        const response = await api.admin.getDocumentsByCollection(
          id,
          page,
          ITEMS_PER_PAGE,
        );
        // Assuming response.data contains PaginatedResponse<DocumentResponse>
        const paginatedData = response.data || response;
        if (paginatedData && Array.isArray(paginatedData.items)) {
          // Changed from .documents to .items
          setDocuments(paginatedData.items);
          setTotalDocs(paginatedData.total);
          setCurrentDocPage(paginatedData.page);
        } else {
          console.warn('Unexpected document response structure:', response);
          setDocuments([]);
          setTotalDocs(0);
        }
      } catch (err: any) {
        setDocError(err.message || 'Failed to fetch documents.');
        console.error(err);
      } finally {
        setIsFetchingDocs(false);
      }
    },
    [canPerformAction],
  );

  useEffect(() => {
    if (collectionId && canPerformAction) {
      fetchCollectionDetails(collectionId);
      fetchDocuments(collectionId, currentDocPage);
    }
  }, [
    collectionId,
    canPerformAction,
    fetchCollectionDetails,
    fetchDocuments,
    currentDocPage,
  ]);

  const handleCollectionSubmit = async (
    e: React.FormEvent<HTMLFormElement>,
  ) => {
    e.preventDefault();
    if (!collectionId) {
      return;
    }
    if (!name.trim()) {
      setError('Collection name is required.');
      return;
    }
    setIsLoading(true);
    setError(null);
    const updateData: CollectionUpdateRequest = { name, status };
    try {
      await api.admin.updateCollection(collectionId, updateData);
      // Optionally show success message
      // No redirect, just update state or re-fetch
      if (collection) {
        setCollection({ ...collection, name, status });
      }
    } catch (err: any) {
      setError(
        err.response?.data?.error ||
          err.message ||
          'Failed to update collection.',
      );
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      setFileToUpload(e.target.files[0]);
    } else {
      setFileToUpload(null);
    }
  };

  const handleDocumentUpload = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!fileToUpload || !collectionId || !selectedS3ConfigId) {
      setDocError(
        'File, collection, and S3 configuration are required for upload.',
      );
      return;
    }
    setIsUploading(true);
    setDocError(null);
    try {
      await api.admin.uploadDocument(
        collectionId,
        Number.parseInt(selectedS3ConfigId, 10),
        fileToUpload,
      );
      setFileToUpload(null); // Reset file input
      // (document.getElementById('fileUploadForm') as HTMLFormElement)?.reset(); // Or this way
      if (e.currentTarget) {
        e.currentTarget.reset();
      }

      fetchDocuments(collectionId, currentDocPage); // Refresh document list
    } catch (err: any) {
      setDocError(
        err.response?.data?.error ||
          err.message ||
          'Failed to upload document.',
      );
      console.error(err);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDocumentDelete = async (docId: number, docName: string) => {
    if (!collectionId) {
      return;
    }
    if (
      window.confirm(`Are you sure you want to delete document "${docName}"?`)
    ) {
      setDocError(null);
      try {
        await api.admin.deleteDocument(docId);
        fetchDocuments(collectionId, currentDocPage); // Refresh document list
      } catch (err: any) {
        setDocError(
          err.response?.data?.error ||
            err.message ||
            'Failed to delete document.',
        );
        console.error(err);
      }
    }
  };

  const totalDocPages = Math.ceil(totalDocs / ITEMS_PER_PAGE);

  // Render logic
  if (
    checkingAuth ||
    (canPerformAction && isFetchingCollection && collectionId)
  ) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (!canPerformAction) {
    const accessError =
      error || 'Access Denied: You do not have permission to manage this page.';
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>{' '}
          <span className="block sm:inline">{accessError}</span>
        </div>
        <Link
          href="/admin/collections"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to Collections
        </Link>
      </div>
    );
  }

  if (error && !collection) {
    // Error likely from fetching collection details
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error fetching collection: </strong>{' '}
          <span className="block sm:inline">{error}</span>
        </div>
        <Link
          href="/admin/collections"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to Collections
        </Link>
      </div>
    );
  }

  if (!collection && !isFetchingCollection) {
    return (
      <div className="container mx-auto p-4">
        Collection not found or error occurred.{' '}
        <Link
          href="/admin/collections"
          className="text-blue-500 hover:text-blue-700"
        >
          Return to list.
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-8">
      <div>
        <Link
          href="/admin/collections"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center mb-4"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to Collections List
        </Link>
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          Edit Collection: {collection?.name}
        </h1>
      </div>

      {/* Edit Collection Form */}
      <form
        onSubmit={handleCollectionSubmit}
        className="bg-white shadow-md rounded-lg p-6 md:p-8"
      >
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Collection Details
        </h2>
        {error && ( // Display collection update errors
          <div
            className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
            role="alert"
          >
            <strong className="font-bold">Error: </strong>{' '}
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        <div className="mb-4">
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Collection Name*
          </label>
          <input
            type="text"
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            disabled={isLoading}
            className="w-full input-class"
          />
        </div>
        <div className="mb-6">
          <label
            htmlFor="status"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Status
          </label>
          <select
            id="status"
            value={status}
            onChange={(e) => setStatus(e.target.value as CollectionStatus)}
            disabled={isLoading}
            className="w-full input-class bg-white"
          >
            <option value={CollectionStatus.ACTIVE}>Active</option>
            <option value={CollectionStatus.DISABLED}>Disabled</option>
          </select>
        </div>
        <div className="flex items-center justify-end">
          <CloneButton 
            entityType="collections" 
            entityId={collection.id} 
            className="mr-2"
          />
          <button
            type="submit"
            className="btn-primary disabled:opacity-50"
            disabled={isLoading || !canPerformAction}
          >
            <Save size={18} className="mr-2" />{' '}
            {isLoading ? 'Saving...' : 'Save Collection Details'}
          </button>
        </div>
      </form>

      {/* Document Management Section */}
      <div className="bg-white shadow-md rounded-lg p-6 md:p-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Manage Documents
        </h2>

        {/* Upload Document Form */}
        <form
          onSubmit={handleDocumentUpload}
          className="mb-8 p-4 border border-gray-200 rounded-md"
          id="fileUploadForm"
        >
          <h3 className="text-lg font-medium text-gray-700 mb-3">
            Upload New Document
          </h3>
          {docError && (
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
              role="alert"
            >
              <strong className="font-bold">Upload Error: </strong>{' '}
              <span className="block sm:inline">{docError}</span>
            </div>
          )}
          <div className="mb-4">
            <label
              htmlFor="s3ConfigurationId"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              S3 Storage Target*
            </label>
            <select
              id="s3ConfigurationId"
              value={selectedS3ConfigId}
              onChange={(e) => setSelectedS3ConfigId(e.target.value)}
              required
              disabled={isUploading || s3Configs.length === 0}
              className="w-full input-class bg-white"
            >
              {s3Configs.length === 0 && (
                <option value="">No S3 configurations available</option>
              )}
              {s3Configs.map((conf) => (
                <option key={conf.id} value={conf.id.toString()}>
                  {conf.serviceName} (Bucket: {conf.bucketName})
                </option>
              ))}
            </select>
            {s3Configs.length === 0 && (
              <p className="text-xs text-red-500 mt-1">
                You must create an S3 configuration first.
              </p>
            )}
          </div>
          <div className="mb-4">
            <label
              htmlFor="file"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Document File*
            </label>
            <input
              type="file"
              id="file"
              onChange={handleFileChange}
              required
              disabled={isUploading || !selectedS3ConfigId}
              className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>
          <button
            type="submit"
            className="btn-primary disabled:opacity-50"
            disabled={
              isUploading ||
              !fileToUpload ||
              !selectedS3ConfigId ||
              !canPerformAction
            }
          >
            <UploadCloud size={18} className="mr-2" />{' '}
            {isUploading ? 'Uploading...' : 'Upload Document'}
          </button>
        </form>

        {/* List Documents */}
        <h3 className="text-lg font-medium text-gray-700 mb-3">
          Uploaded Documents
        </h3>
        {isFetchingDocs && <p>Loading documents...</p>}
        {!isFetchingDocs && documents.length === 0 && (
          <div className="text-center py-6 bg-gray-50 rounded-md">
            <FileText size={36} className="mx-auto text-gray-400 mb-2" />
            <p className="text-gray-500">
              No documents found in this collection.
            </p>
          </div>
        )}
        {!isFetchingDocs && documents.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Filename
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Uploaded
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {documents.map((doc) => (
                  <tr key={doc.id}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                      {doc.filename}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {doc.filesize && doc.filesize > 0
                        ? `${(doc.filesize / 1024).toFixed(2)} KB`
                        : 'N/A'}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {doc.mimetype || 'N/A'}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {doc.createdAt
                        ? new Date(doc.createdAt).toLocaleDateString()
                        : 'N/A'}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() =>
                          handleDocumentDelete(doc.id, doc.filename)
                        }
                        className="text-red-600 hover:text-red-800 disabled:opacity-50"
                        disabled={!canPerformAction}
                        title="Delete Document"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination for Documents */}
            {totalDocPages > 1 && (
              <div className="mt-4 flex justify-center items-center space-x-2">
                <button
                  onClick={() =>
                    fetchDocuments(collectionId!, currentDocPage - 1)
                  }
                  disabled={currentDocPage === 1 || isFetchingDocs}
                  className="pagination-btn"
                >
                  Previous
                </button>
                <span>
                  Page {currentDocPage} of {totalDocPages}
                </span>
                <button
                  onClick={() =>
                    fetchDocuments(collectionId!, currentDocPage + 1)
                  }
                  disabled={currentDocPage === totalDocPages || isFetchingDocs}
                  className="pagination-btn"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}
      </div>
      <style jsx>{`
        .input-class { @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500; }
        .btn-primary { @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out; }
        .pagination-btn { @apply px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed; }
      `}</style>
    </div>
  );
}
