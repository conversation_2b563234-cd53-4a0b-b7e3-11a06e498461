// Jest setup for server tests
// This file runs before each test file

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.PORT = '0' // Use random port for tests
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
process.env.JWT_EXPIRES_IN = '1h'

// Mock console methods to reduce noise in tests (optional)
if (process.env.SILENT_TESTS === 'true') {
  global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}

// Global test utilities
global.testUtils = {
  // Helper to create test user data
  createTestUser: (overrides = {}) => ({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    roles: ['USER'],
    ...overrides,
  }),

  // Helper to create test message data
  createTestMessage: (overrides = {}) => ({
    message: 'Test message',
    sessionId: 'test-session-123',
    platform: 'web',
    messageType: 'text',
    userId: 'test-user-123',
    ...overrides,
  }),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise((resolve) => setTimeout(resolve, ms)),
}

// Setup test database connection
beforeAll(async () => {
  // Database setup will be handled by global setup
})

afterAll(async () => {
  // Cleanup will be handled by global teardown
})

// Clean up after each test
afterEach(async () => {
  // Clear any test data if needed
  jest.clearAllMocks()
})
