import { create } from 'zustand';
import type { Collection, CollectionCreateRequest, CollectionUpdateRequest } from '@/types/collection';
import { api } from '@/lib/api';

interface CollectionsState {
  collections: Collection[];
  currentCollection: Collection | null;
  isLoading: boolean;
  error: string | null;
}

interface CollectionsActions {
  fetchCollections: () => Promise<void>;
  fetchCollectionById: (id: number) => Promise<Collection | null>;
  createCollection: (data: CollectionCreateRequest) => Promise<boolean>;
  updateCollection: (id: number, data: CollectionUpdateRequest) => Promise<boolean>;
  deleteCollection: (id: number) => Promise<boolean>;
  setCurrentCollection: (collection: Collection | null) => void;
  clearError: () => void;
}

type CollectionsStore = CollectionsState & CollectionsActions;

export const useCollectionsStore = create<CollectionsStore>((set, get) => ({
  collections: [],
  currentCollection: null,
  isLoading: false,
  error: null,

  fetchCollections: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.getCollections();
      if (response.success && response.data) {
        set({ collections: response.data, isLoading: false });
      } else {
        set({ error: response.error || 'Failed to fetch collections', isLoading: false });
      }
    } catch (error) {
      console.error('Error fetching collections:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch collections', 
        isLoading: false 
      });
    }
  },

  fetchCollectionById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.getCollectionById(id);
      if (response.success && response.data) {
        const collection = response.data;
        set({ currentCollection: collection, isLoading: false });
        return collection;
      } else {
        set({ error: response.error || 'Failed to fetch collection', isLoading: false });
        return null;
      }
    } catch (error) {
      console.error('Error fetching collection:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch collection', 
        isLoading: false 
      });
      return null;
    }
  },

  createCollection: async (data: CollectionCreateRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.createCollection(data);
      if (response.success) {
        // Refresh collections list
        await get().fetchCollections();
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || 'Failed to create collection', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error creating collection:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create collection', 
        isLoading: false 
      });
      return false;
    }
  },

  updateCollection: async (id: number, data: CollectionUpdateRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.updateCollection(id, data);
      if (response.success) {
        // Refresh collections list
        await get().fetchCollections();
        // Update current collection if it's the one being updated
        const current = get().currentCollection;
        if (current && current.id === id) {
          await get().fetchCollectionById(id);
        }
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || 'Failed to update collection', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error updating collection:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update collection', 
        isLoading: false 
      });
      return false;
    }
  },

  deleteCollection: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.admin.deleteCollection(id);
      if (response.success) {
        // Remove from collections list
        const collections = get().collections.filter(c => c.id !== id);
        set({ collections, isLoading: false });
        // Clear current collection if it's the one being deleted
        const current = get().currentCollection;
        if (current && current.id === id) {
          set({ currentCollection: null });
        }
        return true;
      } else {
        set({ error: response.error || 'Failed to delete collection', isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error deleting collection:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete collection', 
        isLoading: false 
      });
      return false;
    }
  },

  setCurrentCollection: (collection: Collection | null) => {
    set({ currentCollection: collection });
  },

  clearError: () => {
    set({ error: null });
  },
}));
