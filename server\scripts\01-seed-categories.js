/**
 * Category Seeding Script
 *
 * This script seeds the categories table with predefined halal product categories.
 * It's safe to run multiple times as it uses ON CONFLICT DO NOTHING.
 *
 * Usage: node scripts/seed-categories.js
 *
 * Features:
 * - Seeds 7 predefined halal categories
 * - Prevents duplicate entries
 * - Displays existing and new categories
 * - Safe to run multiple times
 */

const postgres = require('postgres')
require('dotenv').config()

const CATEGORIES = [
  'Meat & Poultry',
  'Seafood',
  'Packaged & Processed Foods',
  'Snacks & Confectionery',
  'Beverages',
  'Dairy Products',
  'Ingredients & Additives',
]

async function seedCategories() {
  const sql = postgres(process.env.DATABASE_URL)

  try {
    console.log('🌱 Starting to seed categories...')

    // Check if categories already exist
    const existingCategories = await sql`SELECT category_name FROM categories`
    const existingNames = existingCategories.map((cat) => cat.category_name)

    // Filter out categories that already exist
    const categoriesToInsert = CATEGORIES.filter(
      (cat) => !existingNames.includes(cat),
    )

    if (categoriesToInsert.length === 0) {
      console.log('✅ All categories already exist in the database')
    } else {
      console.log(`📝 Inserting ${categoriesToInsert.length} new categories...`)

      // Insert categories one by one to get better error handling
      for (const categoryName of categoriesToInsert) {
        try {
          const result = await sql`
            INSERT INTO categories (category_name) 
            VALUES (${categoryName}) 
            RETURNING id, category_name, created_at
          `
          console.log(
            `✅ Inserted: ${result[0].category_name} (ID: ${result[0].id})`,
          )
        } catch (error) {
          console.error(`❌ Failed to insert ${categoryName}:`, error.message)
        }
      }
    }

    // Display all categories
    console.log('\n📋 All categories in database:')
    const allCategories =
      await sql`SELECT id, category_name, created_at FROM categories ORDER BY id`
    allCategories.forEach((cat) => {
      console.log(`  ${cat.id}. ${cat.category_name}`)
    })

    console.log('\n🎉 Category seeding completed!')
  } catch (error) {
    console.error('❌ Error seeding categories:', error.message)
  } finally {
    await sql.end()
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  seedCategories()
}

module.exports = { seedCategories, CATEGORIES }
